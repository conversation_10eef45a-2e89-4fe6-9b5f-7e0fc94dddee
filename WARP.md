# WARP.md

Este arquivo fornece orientações ao WARP (warp.dev) ao trabalhar com código neste repositório.

## Visão Geral do Projeto

O Bastidor é um site de notícias baseado em WordPress para análises exclusivas sobre política, direito e economia. O projeto usa um tema WordPress personalizado construído com ferramentas modernas incluindo Tailwind CSS, TypeScript e Alpine.js.

## Comandos de Desenvolvimento

### Gerenciamento de Assets
```bash
# Instalar dependências
npm install

# Gerar todos os assets (CSS e JS)
npm run assets

# Observar mudanças no CSS (Tailwind)
npm run css-all-watch

# Observar mudanças no JavaScript
npm run js-watch

# Observar CSS e JS simultaneamente
npm run watch

# Gerar assets individuais
npm run css          # Gerar todo CSS
npm run css-all      # Gerar CSS principal
npm run css-admin    # Gerar CSS do admin
npm run js           # Gerar todo JavaScript
```

### Dependências PHP
```bash
# Instalar dependências do Composer para o tema
cd bxwp/wp-content/themes/bx-wp-theme/includes/composer
composer install
```

### Desenvolvimento Local
```bash
# O time utiliza LocalWP como ambiente de desenvolvimento
# Certifique-se de que o LocalWP está rodando e o site 'obastidor' está ativo
# Site local disponível em: http://obastidor.test

# Ferramentas disponíveis quando o LocalWP está rodando:
# - AdminNeo (gerenciador de BD): http://localhost:10010/?server=&username=root&db=local
# - Mailpit (captura emails): http://localhost:10000/

# Inicializar ambiente LocalWP (recomendado)
# No macOS, o script está localizado em:
# /Users/<USER>/Library/Application\ Support/Local/ssh-entry/<id-do-site>.sh
# O ID do site pode ser encontrado em sites.json na pasta base do LocalWP
# /Users/<USER>/Library/Application\ Support/Local/sites.json
source "/Users/<USER>/Library/Application Support/Local/ssh-entry/obastidor.sh"

# O script configura:
# - Variáveis de ambiente corretas
# - Versão apropriada do PHP
# - wp-cli disponível
# - Outras configurações específicas do LocalWP

# Nota: Assumir que o usuário já iniciou o LocalWP e ativou o site
# O docker-compose.yml existe mas não é utilizado pela equipe
```

### Testes
```bash
# Executar testes e2e do Cypress
npx cypress open
```

### Registros de Decisões Arquiteturais (ADRs)
```bash
# Criar novo ADR
npm run adr "Título da decisão"
```

## Arquitetura do Código

### Estrutura do Tema WordPress
O tema personalizado segue uma arquitetura modular localizada em `bxwp/wp-content/themes/bx-wp-theme/`:

- **`assets/`** - Recursos do frontend organizados por tipo:
  - `scripts/` - Arquivos JavaScript (fontes TypeScript)
  - `tailwind/` - Fontes CSS para compilação Tailwind
  - `css/` - CSS minificado gerado (ignorado pelo git)
  - `js/` - JavaScript minificado gerado (ignorado pelo git)
  - `icons/` - Ícones SVG e imagens
  - `fonts/` - Fontes web

- **`includes/`** - Lógica de negócio PHP organizada por responsabilidade:
  - `entities/` - Custom post types, taxonomias e entidades de negócio
  - `generic/` - Classes utilitárias reutilizáveis
  - `global/` - Configuração e setup do tema
  - `hooks/` - Hooks de ação/filtro do WordPress
  - `services/` - Integrações com APIs externas

- **`modules/`** - Recursos auto-contidos com CSS/JS dedicados
- **`pages/`** - Arquivos de template do WordPress
- **`components/`** - Componentes de template PHP reutilizáveis

### Sistema de Entidades
Entidades customizadas implementadas como classes PHP com namespacing:
- `BX_Obastidor\News\` - Gerenciamento de notícias com taxonomia de destaque
- `BX_Obastidor\AdBanner\` - Sistema de banners publicitários
- `BX_Obastidor\Newsletter\` - Gerenciamento de assinatura de newsletter
- `BX_Obastidor\Author\` - Funcionalidade estendida de autores

### Build de Assets
- **JavaScript**: Usa `@wordpress/scripts` com webpack, suporta TypeScript
- **CSS**: Tailwind CSS com configuração customizada e organização por componentes
- **Alvos de build**: `all` (frontend), `admin` (wp-admin), `block-editor` (Gutenberg)

### Configuração de Ambientes
Setup multi-ambiente com arquivos PHP específicos por ambiente:
- `env-local.php` - Desenvolvimento local
- `env-development.php` - Servidor de desenvolvimento
- `env-staging.php` - Ambiente de homologação
- `env-production.php` - Ambiente de produção

### Pipeline de Deploy
Deploy automatizado usando Gitea Actions:
- **Branches**: `main` → produção, `stg` → homologação
- **Processo**: Build de assets → Download do WordPress → Instalação de plugins → Build da imagem Docker → Deploy no ECS
- **Registry**: AWS ECR com repositórios específicos por ambiente
- **Deploy**: Substituição rolling sem downtime no AWS ECS

### Esquema do Banco de Dados
- Taxonomia customizada: `featured-news-order` para níveis de destaque de notícias
- Meta fields customizados: `bx_obastidor_exclusive_content` para conteúdo premium
- Sistema de tracking de visualizações via endpoints REST API

### Plugins Obrigatórios
- **BX Essentials** (Obrigatório) - Funcionalidade core do tema
- **ACF Pro** (Obrigatório) - Campos customizados e páginas de opções
- **Co-Authors Plus** (Opcional) - Suporte a múltiplos autores
- **Yoast SEO** (Opcional) - Gerenciamento de SEO

### Extensões da REST API
Endpoints customizados em `/wp-json/bx-obastidor/v1/`:
- `/view-counter` - Tracking de visualizações de posts com validação de nonce

### Stack Frontend
- **Alpine.js** - Framework JavaScript reativo
- **Tailwind CSS** - Framework CSS utility-first
- **TypeScript** - Desenvolvimento JavaScript com tipagem
- **Cookie Consent** - Conformidade com GDPR

## Notas de Desenvolvimento

### Convenção de Namespace
Todas as classes PHP customizadas usam o namespace `BX_Obastidor\` com autoloading PSR-4.

### Organização de Assets
JavaScript e CSS são organizados por escopo (all/admin/block-editor) e compilados separadamente para carregamento otimizado.

### Integração WordPress
O tema utiliza extensivamente os hooks do WordPress e segue os padrões de codificação WordPress para hierarquia de templates e nomenclatura de funções.

### Ambiente Local
A equipe utiliza LocalWP para desenvolvimento local. O site deve estar acessível em http://obastidor.test quando o LocalWP estiver rodando com o site ativado.

O LocalWP fornece ferramentas auxiliares:
- **AdminNeo**: Gerenciador de banco de dados em http://localhost:10010/
- **Mailpit**: Captura de emails PHP em http://localhost:10000/
- **Script de ambiente**: Disponível em `/Users/<USER>/Library/Application Support/Local/ssh-entry/<id-do-site>.sh` que configura PHP, wp-cli e variáveis de ambiente
- **ID do site**: Pode ser encontrado no arquivo `/Users/<USER>/Library/Application Support/Local/sites.json`

Embora exista um docker-compose.yml no repositório, ele não é utilizado pela equipe de desenvolvimento.
