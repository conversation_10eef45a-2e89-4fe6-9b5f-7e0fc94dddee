<?php

define('DISABLE_WP_CRON', true);

// define('WP_ALLOW_REPAIR', true);

define('AUTOMATIC_UPDATER_DISABLED', true);

define('GOOGLE_ANALYTICS_ID', 'G-BGBGHJL1W9');
define('UMAMI_WEBSITE_ID', '397e4922-ecef-413d-a7a1-2e9a8f181fee');

define('SMTP_HOST', getenv('AWS_SES_SMTP_HOST'));
define('SMTP_PORT', getenv('AWS_SES_SMTP_PORT'));
define('SMTP_USERNAME', getenv('AWS_SES_SMTP_USERNAME'));
define('SMTP_PASSWORD', getenv('AWS_SES_SMTP_PASSWORD'));
define('SMTP_DEFAULT_FROM_EMAIL', getenv('AWS_SES_SMTP_DEFAULT_FROM_EMAIL'));

define('AWS_REGION', getenv('AWS_REGION'));
define('AWS_EMAIL_QUEUE_TRIGGER_ARN', getenv('AWS_EMAIL_QUEUE_TRIGGER_ARN'));
define('AWS_NEWSLETTER_METRICS_GETTER_ARN', getenv('AWS_NEWSLETTER_METRICS_GETTER_ARN'));

// API Configuration
define('BX_OBASTIDOR_API_TOKEN', getenv('BX_OBASTIDOR_API_TOKEN'));
