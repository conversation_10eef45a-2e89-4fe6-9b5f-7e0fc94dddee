# Advanced Custom Fields Translations are a combination of translate.wordpress.org contributions,
# combined with user contributed strings for the PRO version.
# Translations from translate.wordpress.org take priority over translations in this file.
# translate.wordpress.org contributions are synced at the time of each release.
#
# If you would like to contribute translations, please visit
# https://translate.wordpress.org/projects/wp-plugins/advanced-custom-fields/stable/
#
# For additional ACF PRO strings, please submit a pull request over on the ACF GitHub repo at
# http://github.com/advancedcustomfields/acf using the .pot (and any existing .po) files in /lang/pro/
#
# This file is distributed under the same license as Advanced Custom Fields.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-05-19T16:45:13+00:00\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"Language: vi\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: gettext\n"
"Project-Id-Version: Advanced Custom Fields\n"

#: includes/fields/class-acf-field-select.php:471
msgid ""
"Save created options back to the \"Choices\" setting in the field definition."
msgstr ""

#: includes/fields/class-acf-field-select.php:470
msgid "Save Options"
msgstr ""

#: includes/fields/class-acf-field-select.php:448
msgid ""
"Allow content editors to create new options by typing in the Select input. "
"Multiple options can be created from a comma separated string."
msgstr ""

#: includes/fields/class-acf-field-select.php:447
msgid "Create Options"
msgstr ""

#: includes/admin/views/global/navigation.php:179
#: includes/admin/views/global/navigation.php:183
msgid "Edit ACF Field Groups"
msgstr ""

#: includes/admin/views/global/navigation.php:100
msgid "Get 4 months free on any WP Engine plan"
msgstr ""

#: src/Site_Health/Site_Health.php:522
msgid "Number of Field Groups with Blocks and Other Locations"
msgstr ""

#: src/Site_Health/Site_Health.php:517
msgid "Number of Field Groups with Multiple Block Locations"
msgstr ""

#: src/Site_Health/Site_Health.php:512
msgid "Number of Field Groups with a Single Block Location"
msgstr ""

#: src/Site_Health/Site_Health.php:481
msgid "All Location Rules"
msgstr ""

#: includes/validation.php:144
msgid "Learn more"
msgstr "Tìm hiểu thêm"

#: includes/validation.php:133
msgid ""
"ACF was unable to perform validation because the provided nonce failed "
"verification."
msgstr ""
"ACF không thể thực hiện xác thực vì nonce được cung cấp không xác minh được."

#: includes/validation.php:131
msgid ""
"ACF was unable to perform validation because no nonce was received by the "
"server."
msgstr "ACF không thể thực hiện xác thực vì máy chủ không nhận được mã nonce."

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:324
msgid "are developed and maintained by"
msgstr "được phát triển và duy trì bởi"

#: src/Site_Health/Site_Health.php:295
msgid "Update Source"
msgstr "Cập nhật nguồn"

#: includes/admin/views/acf-post-type/advanced-settings.php:850
#: includes/admin/views/acf-taxonomy/advanced-settings.php:810
msgid "By default only admin users can edit this setting."
msgstr "Mặc định chỉ có người dùng quản trị mới có thể sửa cài đặt này."

#: includes/admin/views/acf-post-type/advanced-settings.php:848
#: includes/admin/views/acf-taxonomy/advanced-settings.php:808
msgid "By default only super admin users can edit this setting."
msgstr "Mặc định chỉ có người dùng siêu quản trị mới có thể sửa cài đặt này."

#: includes/admin/views/acf-field-group/field.php:322
msgid "Close and Add Field"
msgstr "Đóng và thêm trường"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:804
msgid ""
"A PHP function name to be called to handle the content of a meta box on your "
"taxonomy. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""
"Một tên hàm PHP sẽ được gọi để xử lý nội dung của một meta box trên taxonomy "
"của bạn. Để đảm bảo an toàn, callback này sẽ được thực thi trong một ngữ "
"cảnh đặc biệt mà không có quyền truy cập vào bất kỳ superglobal nào như "
"$_POST hoặc $_GET."

#: includes/admin/views/acf-post-type/advanced-settings.php:842
msgid ""
"A PHP function name to be called when setting up the meta boxes for the edit "
"screen. For security, this callback will be executed in a special context "
"without access to any superglobals like $_POST or $_GET."
msgstr ""
"Một tên hàm PHP sẽ được gọi khi thiết lập các hộp meta cho trang sửa. Để đảm "
"bảo an toàn, hàm gọi lại này sẽ được thực thi trong một ngữ cảnh đặc biệt mà "
"không có quyền truy cập vào bất kỳ siêu toàn cục nào như $_POST hoặc $_GET."

#: src/Site_Health/Site_Health.php:296
msgid "wordpress.org"
msgstr "wordpress.org"

#: includes/fields/class-acf-field.php:359
msgid "Allow Access to Value in Editor UI"
msgstr "Cho phép truy cập giá trị trong giao diện trình chỉnh sửa"

#: includes/fields/class-acf-field.php:341
msgid "Learn more."
msgstr "Xem thêm."

#. translators: %s A "Learn More" link to documentation explaining the setting
#. further.
#: includes/fields/class-acf-field.php:340
msgid ""
"Allow content editors to access and display the field value in the editor UI "
"using Block Bindings or the ACF Shortcode. %s"
msgstr ""
"Cho phép người chỉnh sửa nội dung truy cập và hiển thị giá trị của trường "
"trong giao diện trình chỉnh sửa bằng cách sử dụng Block Bindings hoặc ACF "
"Shortcode. %s"

#: src/Blocks/Bindings.php:67
msgid ""
"The requested ACF field type does not support output in Block Bindings or "
"the ACF shortcode."
msgstr ""
"Loại trường ACF được yêu cầu không hỗ trợ xuất trong Block Bindings hoặc ACF "
"Shortcode."

#: includes/api/api-template.php:1085 src/Blocks/Bindings.php:75
msgid ""
"The requested ACF field is not allowed to be output in bindings or the ACF "
"Shortcode."
msgstr ""
"Trường ACF được yêu cầu không được phép xuất trong bindings hoặc ACF "
"Shortcode."

#: includes/api/api-template.php:1077
msgid ""
"The requested ACF field type does not support output in bindings or the ACF "
"Shortcode."
msgstr ""
"Loại trường ACF được yêu cầu không hỗ trợ xuất trong bindings hoặc ACF "
"Shortcode."

#: includes/api/api-template.php:1054
msgid "[The ACF shortcode cannot display fields from non-public posts]"
msgstr ""
"[Shortcode ACF không thể hiển thị các trường từ các bài viết không công khai]"

#: includes/api/api-template.php:1011
msgid "[The ACF shortcode is disabled on this site]"
msgstr "[Shortcode ACF đã bị tắt trên trang web này]"

#: includes/fields/class-acf-field-icon_picker.php:476
msgid "Businessman Icon"
msgstr "Biểu tượng doanh nhân"

#: includes/fields/class-acf-field-icon_picker.php:468
msgid "Forums Icon"
msgstr "Biểu tượng diễn đàn"

#: includes/fields/class-acf-field-icon_picker.php:747
msgid "YouTube Icon"
msgstr "Biểu tượng YouTube"

#: includes/fields/class-acf-field-icon_picker.php:746
msgid "Yes (alt) Icon"
msgstr "Biểu tượng có (thay thế)"

#: includes/fields/class-acf-field-icon_picker.php:744
msgid "Xing Icon"
msgstr "Biểu tượng Xing"

#: includes/fields/class-acf-field-icon_picker.php:743
msgid "WordPress (alt) Icon"
msgstr "Biểu tượng WordPress (thay thế)"

#: includes/fields/class-acf-field-icon_picker.php:741
msgid "WhatsApp Icon"
msgstr "Biểu tượng WhatsApp"

#: includes/fields/class-acf-field-icon_picker.php:740
msgid "Write Blog Icon"
msgstr "Biểu tượng viết Blog"

#: includes/fields/class-acf-field-icon_picker.php:739
msgid "Widgets Menus Icon"
msgstr "Biểu tượng Menu tiện ích"

#: includes/fields/class-acf-field-icon_picker.php:738
msgid "View Site Icon"
msgstr "Xem biểu tượng trang web"

#: includes/fields/class-acf-field-icon_picker.php:737
msgid "Learn More Icon"
msgstr "Biểu tượng tìm hiểu thêm"

#: includes/fields/class-acf-field-icon_picker.php:735
msgid "Add Page Icon"
msgstr "Thêm biểu tượng trang"

#: includes/fields/class-acf-field-icon_picker.php:732
msgid "Video (alt3) Icon"
msgstr "Biểu tượng Video (alt3)"

#: includes/fields/class-acf-field-icon_picker.php:731
msgid "Video (alt2) Icon"
msgstr "Biểu tượng Video (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:730
msgid "Video (alt) Icon"
msgstr "Biểu tượng video (thay thế)"

#: includes/fields/class-acf-field-icon_picker.php:727
msgid "Update (alt) Icon"
msgstr "Cập nhật biểu tượng (thay thế)"

#: includes/fields/class-acf-field-icon_picker.php:724
msgid "Universal Access (alt) Icon"
msgstr "Biểu tượng truy cập toàn diện (alt)"

#: includes/fields/class-acf-field-icon_picker.php:721
msgid "Twitter (alt) Icon"
msgstr "Biểu tượng Twitter (thay thế)"

#: includes/fields/class-acf-field-icon_picker.php:719
msgid "Twitch Icon"
msgstr "Biểu tượng Twitch"

#: includes/fields/class-acf-field-icon_picker.php:716
msgid "Tide Icon"
msgstr "Biểu tượng Tide"

#: includes/fields/class-acf-field-icon_picker.php:715
msgid "Tickets (alt) Icon"
msgstr "Biểu tượng Tickets (thay thế)"

#: includes/fields/class-acf-field-icon_picker.php:711
msgid "Text Page Icon"
msgstr "Biểu tượng trang văn bản"

#: includes/fields/class-acf-field-icon_picker.php:705
msgid "Table Row Delete Icon"
msgstr "Biểu tượng xoá hàng trong bảng"

#: includes/fields/class-acf-field-icon_picker.php:704
msgid "Table Row Before Icon"
msgstr "Biểu tượng trước hàng trong bảng"

#: includes/fields/class-acf-field-icon_picker.php:703
msgid "Table Row After Icon"
msgstr "Biểu tượng sau hàng trong bảng"

#: includes/fields/class-acf-field-icon_picker.php:702
msgid "Table Col Delete Icon"
msgstr "Biểu tượng xoá cột trong bảng"

#: includes/fields/class-acf-field-icon_picker.php:701
msgid "Table Col Before Icon"
msgstr "Biểu tượng thêm cột trước"

#: includes/fields/class-acf-field-icon_picker.php:700
msgid "Table Col After Icon"
msgstr "Biểu tượng thêm cột sau"

#: includes/fields/class-acf-field-icon_picker.php:699
msgid "Superhero (alt) Icon"
msgstr "Biểu tượng siêu anh hùng (thay thế)"

#: includes/fields/class-acf-field-icon_picker.php:698
msgid "Superhero Icon"
msgstr "Biểu tượng siêu anh hùng"

#: includes/fields/class-acf-field-icon_picker.php:692
msgid "Spotify Icon"
msgstr "Biểu tượng Spotify"

#: includes/fields/class-acf-field-icon_picker.php:686
msgid "Shortcode Icon"
msgstr "Biểu tượng mã ngắn (shortcode)"

#: includes/fields/class-acf-field-icon_picker.php:685
msgid "Shield (alt) Icon"
msgstr "Biểu tượng khiên (thay thế)"

#: includes/fields/class-acf-field-icon_picker.php:683
msgid "Share (alt2) Icon"
msgstr "Biểu tượng chia sẻ (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:682
msgid "Share (alt) Icon"
msgstr "Biểu tượng chia sẻ (thay thế)"

#: includes/fields/class-acf-field-icon_picker.php:677
msgid "Saved Icon"
msgstr "Biểu tượng đã lưu"

#: includes/fields/class-acf-field-icon_picker.php:676
msgid "RSS Icon"
msgstr "Biểu tượng RSS"

#: includes/fields/class-acf-field-icon_picker.php:675
msgid "REST API Icon"
msgstr "Biểu tượng REST API"

#: includes/fields/class-acf-field-icon_picker.php:674
msgid "Remove Icon"
msgstr "Xóa biểu tượng"

#: includes/fields/class-acf-field-icon_picker.php:672
msgid "Reddit Icon"
msgstr "Biểu tượng Reddit"

#: includes/fields/class-acf-field-icon_picker.php:669
msgid "Privacy Icon"
msgstr "Biểu tượng quyền riêng tư"

#: includes/fields/class-acf-field-icon_picker.php:668
msgid "Printer Icon"
msgstr "Biểu tượng máy in"

#: includes/fields/class-acf-field-icon_picker.php:664
msgid "Podio Icon"
msgstr "Biểu tượng Podio"

#: includes/fields/class-acf-field-icon_picker.php:663
msgid "Plus (alt2) Icon"
msgstr "Biểu tượng dấu cộng (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:662
msgid "Plus (alt) Icon"
msgstr "Biểu tượng dấu cộng (thay thế)"

#: includes/fields/class-acf-field-icon_picker.php:660
msgid "Plugins Checked Icon"
msgstr "Biểu tượng đã kiểm tra plugin"

#: includes/fields/class-acf-field-icon_picker.php:657
msgid "Pinterest Icon"
msgstr "Biểu tượng Pinterest"

#: includes/fields/class-acf-field-icon_picker.php:655
msgid "Pets Icon"
msgstr "Biểu tượng thú cưng"

#: includes/fields/class-acf-field-icon_picker.php:653
msgid "PDF Icon"
msgstr "Biểu tượng PDF"

#: includes/fields/class-acf-field-icon_picker.php:651
msgid "Palm Tree Icon"
msgstr "Biểu tượng cây cọ"

#: includes/fields/class-acf-field-icon_picker.php:650
msgid "Open Folder Icon"
msgstr "Biểu tượng mở thư mục"

#: includes/fields/class-acf-field-icon_picker.php:649
msgid "No (alt) Icon"
msgstr "Không có biểu tượng (thay thế)"

#: includes/fields/class-acf-field-icon_picker.php:644
msgid "Money (alt) Icon"
msgstr "Biểu tượng tiền (thay thế)"

#: includes/fields/class-acf-field-icon_picker.php:639
msgid "Menu (alt3) Icon"
msgstr "Biểu tượng Menu (alt3)"

#: includes/fields/class-acf-field-icon_picker.php:638
msgid "Menu (alt2) Icon"
msgstr "Biểu tượng Menu (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:637
msgid "Menu (alt) Icon"
msgstr "Biểu tượng Menu (alt)"

#: includes/fields/class-acf-field-icon_picker.php:632
msgid "Spreadsheet Icon"
msgstr "Biểu tượng bảng tính"

#: includes/fields/class-acf-field-icon_picker.php:631
msgid "Interactive Icon"
msgstr "Biểu tượng tương tác"

#: includes/fields/class-acf-field-icon_picker.php:630
msgid "Document Icon"
msgstr "Biểu tượng tài liệu"

#: includes/fields/class-acf-field-icon_picker.php:629
msgid "Default Icon"
msgstr "Biểu tượng mặc định"

#: includes/fields/class-acf-field-icon_picker.php:623
msgid "Location (alt) Icon"
msgstr "Biểu tượng vị trí (thay thế)"

#: includes/fields/class-acf-field-icon_picker.php:620
msgid "LinkedIn Icon"
msgstr "Biểu tượng LinkedIn"

#: includes/fields/class-acf-field-icon_picker.php:615
msgid "Instagram Icon"
msgstr "Biểu tượng Instagram"

#: includes/fields/class-acf-field-icon_picker.php:614
msgid "Insert Before Icon"
msgstr "Chèn trước biểu tượng"

#: includes/fields/class-acf-field-icon_picker.php:613
msgid "Insert After Icon"
msgstr "Chèn sau biểu tượng"

#: includes/fields/class-acf-field-icon_picker.php:612
msgid "Insert Icon"
msgstr "Thêm Icon"

#: includes/fields/class-acf-field-icon_picker.php:611
msgid "Info Outline Icon"
msgstr "Biểu tượng đường viền thông tin"

#: includes/fields/class-acf-field-icon_picker.php:608
msgid "Images (alt2) Icon"
msgstr "Biểu tượng ảnh (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:607
msgid "Images (alt) Icon"
msgstr "Biểu tượng ảnh (alt)"

#: includes/fields/class-acf-field-icon_picker.php:606
msgid "Rotate Right Icon"
msgstr "Biểu tượng xoay phải"

#: includes/fields/class-acf-field-icon_picker.php:605
msgid "Rotate Left Icon"
msgstr "Biểu tượng xoay trái"

#: includes/fields/class-acf-field-icon_picker.php:604
msgid "Rotate Icon"
msgstr "Xoay biểu tượng"

#: includes/fields/class-acf-field-icon_picker.php:603
msgid "Flip Vertical Icon"
msgstr "Biểu tượng lật dọc"

#: includes/fields/class-acf-field-icon_picker.php:602
msgid "Flip Horizontal Icon"
msgstr "Biểu tượng lật ngang"

#: includes/fields/class-acf-field-icon_picker.php:600
msgid "Crop Icon"
msgstr "Biểu tượng cắt"

#: includes/fields/class-acf-field-icon_picker.php:599
msgid "ID (alt) Icon"
msgstr "Biểu tượng ID (thay thế)"

#: includes/fields/class-acf-field-icon_picker.php:597
msgid "HTML Icon"
msgstr "Biểu tượng HTML"

#: includes/fields/class-acf-field-icon_picker.php:596
msgid "Hourglass Icon"
msgstr "Biểu tượng đồng hồ cát"

#: includes/fields/class-acf-field-icon_picker.php:593
msgid "Heading Icon"
msgstr "Biểu tượng tiêu đề"

#: includes/fields/class-acf-field-icon_picker.php:589
msgid "Google Icon"
msgstr "Biểu tượng Google"

#: includes/fields/class-acf-field-icon_picker.php:588
msgid "Games Icon"
msgstr "Biểu tượng trò chơi"

#: includes/fields/class-acf-field-icon_picker.php:587
msgid "Fullscreen Exit (alt) Icon"
msgstr "Biểu tượng thoát toàn màn hình (alt)"

#: includes/fields/class-acf-field-icon_picker.php:586
msgid "Fullscreen (alt) Icon"
msgstr "Biểu tượng toàn màn hình (alt)"

#: includes/fields/class-acf-field-icon_picker.php:583
msgid "Status Icon"
msgstr "Biểu tượng trạng thái"

#: includes/fields/class-acf-field-icon_picker.php:581
msgid "Image Icon"
msgstr "Biểu tượng ảnh"

#: includes/fields/class-acf-field-icon_picker.php:580
msgid "Gallery Icon"
msgstr "Biểu tượng album ảnh"

#: includes/fields/class-acf-field-icon_picker.php:579
msgid "Chat Icon"
msgstr "Biểu tượng trò chuyện"

#: includes/fields/class-acf-field-icon_picker.php:578
#: includes/fields/class-acf-field-icon_picker.php:627
msgid "Audio Icon"
msgstr "Biểu tượng âm thanh"

#: includes/fields/class-acf-field-icon_picker.php:577
msgid "Aside Icon"
msgstr "Biểu tượng Aside"

#: includes/fields/class-acf-field-icon_picker.php:576
msgid "Food Icon"
msgstr "Biểu tượng ẩm thực"

#: includes/fields/class-acf-field-icon_picker.php:569
msgid "Exit Icon"
msgstr "Biểu tượng thoát"

#: includes/fields/class-acf-field-icon_picker.php:568
msgid "Excerpt View Icon"
msgstr "Biểu tượng xem tóm tắt"

#: includes/fields/class-acf-field-icon_picker.php:567
msgid "Embed Video Icon"
msgstr "Biểu tượng nhúng video"

#: includes/fields/class-acf-field-icon_picker.php:566
msgid "Embed Post Icon"
msgstr "Biểu tượng nhúng bài viết"

#: includes/fields/class-acf-field-icon_picker.php:565
msgid "Embed Photo Icon"
msgstr "Biểu tượng nhúng ảnh"

#: includes/fields/class-acf-field-icon_picker.php:564
msgid "Embed Generic Icon"
msgstr "Biểu tượng nhúng chung"

#: includes/fields/class-acf-field-icon_picker.php:563
msgid "Embed Audio Icon"
msgstr "Biểu tượng nhúng âm thanh"

#: includes/fields/class-acf-field-icon_picker.php:562
msgid "Email (alt2) Icon"
msgstr "Biểu tượng Email (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:559
msgid "Ellipsis Icon"
msgstr "Biểu tượng dấu ba chấm"

#: includes/fields/class-acf-field-icon_picker.php:555
msgid "Unordered List Icon"
msgstr "Biểu tượng danh sách không thứ tự"

#: includes/fields/class-acf-field-icon_picker.php:550
msgid "RTL Icon"
msgstr "Biểu tượng RTL"

#: includes/fields/class-acf-field-icon_picker.php:543
msgid "Ordered List RTL Icon"
msgstr "Biểu tượng danh sách thứ tự RTL"

#: includes/fields/class-acf-field-icon_picker.php:542
msgid "Ordered List Icon"
msgstr "Biểu tượng danh sách có thứ tự"

#: includes/fields/class-acf-field-icon_picker.php:541
msgid "LTR Icon"
msgstr "Biểu tượng LTR"

#: includes/fields/class-acf-field-icon_picker.php:533
msgid "Custom Character Icon"
msgstr "Biểu tượng ký tự tùy chỉnh"

#: includes/fields/class-acf-field-icon_picker.php:525
msgid "Edit Page Icon"
msgstr "Sửa biểu tượng trang"

#: includes/fields/class-acf-field-icon_picker.php:524
msgid "Edit Large Icon"
msgstr "Sửa biểu tượng lớn"

#: includes/fields/class-acf-field-icon_picker.php:522
msgid "Drumstick Icon"
msgstr "Biểu tượng dùi trống"

#: includes/fields/class-acf-field-icon_picker.php:518
msgid "Database View Icon"
msgstr "Biểu tượng xem cơ sở dữ liệu"

#: includes/fields/class-acf-field-icon_picker.php:517
msgid "Database Remove Icon"
msgstr "Biểu tượng gỡ bỏ cơ sở dữ liệu"

#: includes/fields/class-acf-field-icon_picker.php:516
msgid "Database Import Icon"
msgstr "Biểu tượng nhập cơ sở dữ liệu"

#: includes/fields/class-acf-field-icon_picker.php:515
msgid "Database Export Icon"
msgstr "Biểu tượng xuất cơ sở dữ liệu"

#: includes/fields/class-acf-field-icon_picker.php:514
msgid "Database Add Icon"
msgstr "Biểu tượng thêm cơ sở dữ liệu"

#: includes/fields/class-acf-field-icon_picker.php:513
msgid "Database Icon"
msgstr "Biểu tượng cơ sở dữ liệu"

#: includes/fields/class-acf-field-icon_picker.php:511
msgid "Cover Image Icon"
msgstr "Biểu tượng ảnh bìa"

#: includes/fields/class-acf-field-icon_picker.php:510
msgid "Volume On Icon"
msgstr "Biểu tượng bật âm lượng"

#: includes/fields/class-acf-field-icon_picker.php:509
msgid "Volume Off Icon"
msgstr "Biểu tượng tắt âm lượng"

#: includes/fields/class-acf-field-icon_picker.php:508
msgid "Skip Forward Icon"
msgstr "Biểu tượng bỏ qua chuyển tiếp"

#: includes/fields/class-acf-field-icon_picker.php:507
msgid "Skip Back Icon"
msgstr "Biểu tượng quay lại"

#: includes/fields/class-acf-field-icon_picker.php:506
msgid "Repeat Icon"
msgstr "Biểu tượng lặp lại"

#: includes/fields/class-acf-field-icon_picker.php:505
msgid "Play Icon"
msgstr "Biểu tượng Play"

#: includes/fields/class-acf-field-icon_picker.php:504
msgid "Pause Icon"
msgstr "Biểu tượng tạm dừng"

#: includes/fields/class-acf-field-icon_picker.php:503
msgid "Forward Icon"
msgstr "Biểu tượng chuyển tiếp"

#: includes/fields/class-acf-field-icon_picker.php:502
msgid "Back Icon"
msgstr "Biểu tượng quay lại"

#: includes/fields/class-acf-field-icon_picker.php:501
msgid "Columns Icon"
msgstr "Biểu tượng cột"

#: includes/fields/class-acf-field-icon_picker.php:500
msgid "Color Picker Icon"
msgstr "Biểu tượng chọn màu"

#: includes/fields/class-acf-field-icon_picker.php:499
msgid "Coffee Icon"
msgstr "Biểu tượng cà phê"

#: includes/fields/class-acf-field-icon_picker.php:498
msgid "Code Standards Icon"
msgstr "Biểu tượng tiêu chuẩn mã"

#: includes/fields/class-acf-field-icon_picker.php:497
msgid "Cloud Upload Icon"
msgstr "Biểu tượng tải lên đám mây"

#: includes/fields/class-acf-field-icon_picker.php:496
msgid "Cloud Saved Icon"
msgstr "Biểu tượng lưu trên đám mây"

#: includes/fields/class-acf-field-icon_picker.php:485
msgid "Car Icon"
msgstr "Biểu tượng xe hơi"

#: includes/fields/class-acf-field-icon_picker.php:484
msgid "Camera (alt) Icon"
msgstr "Biểu tượng máy ảnh (thay thế)"

#: includes/fields/class-acf-field-icon_picker.php:480
msgid "Calculator Icon"
msgstr "Biểu tượng máy tính (calculator)"

#: includes/fields/class-acf-field-icon_picker.php:479
msgid "Button Icon"
msgstr "Biểu tượng nút"

#: includes/fields/class-acf-field-icon_picker.php:477
msgid "Businessperson Icon"
msgstr "Biểu tượng doanh nhân"

#: includes/fields/class-acf-field-icon_picker.php:474
msgid "Tracking Icon"
msgstr "Biểu tượng theo dõi"

#: includes/fields/class-acf-field-icon_picker.php:473
msgid "Topics Icon"
msgstr "Biểu tượng chủ đề"

#: includes/fields/class-acf-field-icon_picker.php:472
msgid "Replies Icon"
msgstr "Biểu tượng trả lời"

#: includes/fields/class-acf-field-icon_picker.php:471
msgid "PM Icon"
msgstr "Biểu tượng PM"

#: includes/fields/class-acf-field-icon_picker.php:469
msgid "Friends Icon"
msgstr "Biểu tượng bạn bè"

#: includes/fields/class-acf-field-icon_picker.php:467
msgid "Community Icon"
msgstr "Biểu tượng cộng đồng"

#: includes/fields/class-acf-field-icon_picker.php:466
msgid "BuddyPress Icon"
msgstr "Biểu tượng BuddyPress"

#: includes/fields/class-acf-field-icon_picker.php:465
msgid "bbPress Icon"
msgstr "Biểu tượng BbPress"

#: includes/fields/class-acf-field-icon_picker.php:464
msgid "Activity Icon"
msgstr "Biểu tượng hoạt động"

#: includes/fields/class-acf-field-icon_picker.php:463
msgid "Book (alt) Icon"
msgstr "Biểu tượng sách (thay thế)"

#: includes/fields/class-acf-field-icon_picker.php:461
msgid "Block Default Icon"
msgstr "Biểu tượng khối mặc định"

#: includes/fields/class-acf-field-icon_picker.php:460
msgid "Bell Icon"
msgstr "Biểu tượng chuông"

#: includes/fields/class-acf-field-icon_picker.php:459
msgid "Beer Icon"
msgstr "Biểu tượng bia"

#: includes/fields/class-acf-field-icon_picker.php:458
msgid "Bank Icon"
msgstr "Biểu tượng ngân hàng"

#: includes/fields/class-acf-field-icon_picker.php:454
msgid "Arrow Up (alt2) Icon"
msgstr "Biểu tượng mũi tên lên (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:453
msgid "Arrow Up (alt) Icon"
msgstr "Biểu tượng mũi tên lên (alt)"

#: includes/fields/class-acf-field-icon_picker.php:451
msgid "Arrow Right (alt2) Icon"
msgstr "Biểu tượng mũi tên phải (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:450
msgid "Arrow Right (alt) Icon"
msgstr "Biểu tượng mũi tên sang phải (thay thế)"

#: includes/fields/class-acf-field-icon_picker.php:448
msgid "Arrow Left (alt2) Icon"
msgstr "Biểu tượng mũi tên trái (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:447
msgid "Arrow Left (alt) Icon"
msgstr "Biểu tượng mũi tên trái (alt)"

#: includes/fields/class-acf-field-icon_picker.php:445
msgid "Arrow Down (alt2) Icon"
msgstr "Biểu tượng mũi tên xuống (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:444
msgid "Arrow Down (alt) Icon"
msgstr "Biểu tượng mũi tên xuống (alt)"

#: includes/fields/class-acf-field-icon_picker.php:440
msgid "Amazon Icon"
msgstr "Biểu tượng Amazon"

#: includes/fields/class-acf-field-icon_picker.php:439
msgid "Align Wide Icon"
msgstr "Biểu tượng căn rộng"

#: includes/fields/class-acf-field-icon_picker.php:437
msgid "Align Pull Right Icon"
msgstr "Biểu tượng căn phải"

#: includes/fields/class-acf-field-icon_picker.php:436
msgid "Align Pull Left Icon"
msgstr "Căn chỉnh biểu tượng kéo sang trái"

#: includes/fields/class-acf-field-icon_picker.php:433
msgid "Align Full Width Icon"
msgstr "Biểu tượng căn chỉnh toàn chiều rộng"

#: includes/fields/class-acf-field-icon_picker.php:430
msgid "Airplane Icon"
msgstr "Biểu tượng máy bay"

#: includes/fields/class-acf-field-icon_picker.php:427
msgid "Site (alt3) Icon"
msgstr "Biểu tượng trang web (alt3)"

#: includes/fields/class-acf-field-icon_picker.php:426
msgid "Site (alt2) Icon"
msgstr "Biểu tượng trang web (alt2)"

#: includes/fields/class-acf-field-icon_picker.php:425
msgid "Site (alt) Icon"
msgstr "Biểu tượng trang web (thay thế)"

#: includes/admin/views/options-page-preview.php:26
msgid "Upgrade to ACF PRO to create options pages in just a few clicks"
msgstr ""
"Cập nhật lên ACF pro để tạo các Trang cài đặt chỉ trong vài cú nhấp chuột"

#: includes/ajax/class-acf-ajax-query-users.php:24
msgid "Invalid request args."
msgstr "Yêu cầu không hợp lệ của Args."

#: includes/ajax/class-acf-ajax-check-screen.php:37
#: includes/ajax/class-acf-ajax-local-json-diff.php:37
#: includes/ajax/class-acf-ajax-query-users.php:33
#: includes/ajax/class-acf-ajax-upgrade.php:24
#: includes/ajax/class-acf-ajax-user-setting.php:38
msgid "Sorry, you do not have permission to do that."
msgstr "Xin lỗi, bạn không được phép làm điều đó."

#: src/Site_Health/Site_Health.php:720
msgid "Blocks Using Post Meta"
msgstr "Các bài viết sử dụng Post Meta"

#: includes/admin/views/acf-field-group/pro-features.php:25
#: includes/admin/views/acf-field-group/pro-features.php:27
#: includes/admin/views/global/header.php:27
msgid "ACF PRO logo"
msgstr "Lời bài hát: Acf Pro Logo"

#: includes/admin/views/acf-field-group/field.php:37
msgid "ACF PRO Logo"
msgstr "Lời bài hát: Acf Pro Logo"

#. translators: %s - field/param name
#: includes/fields/class-acf-field-icon_picker.php:813
msgid "%s requires a valid attachment ID when type is set to media_library."
msgstr "%s yêu cầu ID đính kèm hợp lệ khi loại được đặt thành media_library."

#. translators: %s - field name
#: includes/fields/class-acf-field-icon_picker.php:797
msgid "%s is a required property of acf."
msgstr "%s là thuộc tính bắt buộc của acf."

#: includes/fields/class-acf-field-icon_picker.php:773
msgid "The value of icon to save."
msgstr "Giá trị của biểu tượng cần lưu."

#: includes/fields/class-acf-field-icon_picker.php:767
msgid "The type of icon to save."
msgstr "Loại biểu tượng để lưu."

#: includes/fields/class-acf-field-icon_picker.php:745
msgid "Yes Icon"
msgstr "Biểu tượng có"

#: includes/fields/class-acf-field-icon_picker.php:742
msgid "WordPress Icon"
msgstr "Biểu tượng WordPress"

#: includes/fields/class-acf-field-icon_picker.php:734
msgid "Warning Icon"
msgstr "Biểu tượng cảnh báo"

#: includes/fields/class-acf-field-icon_picker.php:733
msgid "Visibility Icon"
msgstr "Biểu tượng hiển thị"

#: includes/fields/class-acf-field-icon_picker.php:729
msgid "Vault Icon"
msgstr "Biểu tượng két sắt"

#: includes/fields/class-acf-field-icon_picker.php:728
msgid "Upload Icon"
msgstr "Biểu tượng tải lên"

#: includes/fields/class-acf-field-icon_picker.php:726
msgid "Update Icon"
msgstr "Biểu tượng cập nhật"

#: includes/fields/class-acf-field-icon_picker.php:725
msgid "Unlock Icon"
msgstr "Biểu tượng mở khóa"

#: includes/fields/class-acf-field-icon_picker.php:723
msgid "Universal Access Icon"
msgstr "Biểu tượng truy cập toàn cầu"

#: includes/fields/class-acf-field-icon_picker.php:722
msgid "Undo Icon"
msgstr "Biểu tượng hoàn tác"

#: includes/fields/class-acf-field-icon_picker.php:720
msgid "Twitter Icon"
msgstr "Biểu tượng Twitter"

#: includes/fields/class-acf-field-icon_picker.php:718
msgid "Trash Icon"
msgstr "Biểu tượng thùng rác"

#: includes/fields/class-acf-field-icon_picker.php:717
msgid "Translation Icon"
msgstr "Biểu tượng dịch"

#: includes/fields/class-acf-field-icon_picker.php:714
msgid "Tickets Icon"
msgstr "Biểu tượng vé Ticket"

#: includes/fields/class-acf-field-icon_picker.php:713
msgid "Thumbs Up Icon"
msgstr "Biểu tượng thích"

#: includes/fields/class-acf-field-icon_picker.php:712
msgid "Thumbs Down Icon"
msgstr "Biểu tượng ngón tay cái chỉ xuống"

#: includes/fields/class-acf-field-icon_picker.php:633
#: includes/fields/class-acf-field-icon_picker.php:710
msgid "Text Icon"
msgstr "Biểu tượng văn bản"

#: includes/fields/class-acf-field-icon_picker.php:709
msgid "Testimonial Icon"
msgstr "Biểu tượng lời chứng thực"

#: includes/fields/class-acf-field-icon_picker.php:708
msgid "Tagcloud Icon"
msgstr "Biểu tượng mây thẻ"

#: includes/fields/class-acf-field-icon_picker.php:707
msgid "Tag Icon"
msgstr "Biểu tượng thẻ"

#: includes/fields/class-acf-field-icon_picker.php:706
msgid "Tablet Icon"
msgstr "Biểu tượng máy tính bảng"

#: includes/fields/class-acf-field-icon_picker.php:697
msgid "Store Icon"
msgstr "Biểu tượng cửa hàng"

#: includes/fields/class-acf-field-icon_picker.php:696
msgid "Sticky Icon"
msgstr "Biểu tượng dính (sticky)"

#: includes/fields/class-acf-field-icon_picker.php:695
msgid "Star Half Icon"
msgstr "Biểu tượng nửa ngôi sao"

#: includes/fields/class-acf-field-icon_picker.php:694
msgid "Star Filled Icon"
msgstr "Biểu tượng đầy sao"

#: includes/fields/class-acf-field-icon_picker.php:693
msgid "Star Empty Icon"
msgstr "Biểu tượng ngôi sao trống"

#: includes/fields/class-acf-field-icon_picker.php:691
msgid "Sos Icon"
msgstr "Biểu tượng SOS"

#: includes/fields/class-acf-field-icon_picker.php:690
msgid "Sort Icon"
msgstr "Biểu tượng sắp xếp"

#: includes/fields/class-acf-field-icon_picker.php:689
msgid "Smiley Icon"
msgstr "Biểu tượng mặt cười"

#: includes/fields/class-acf-field-icon_picker.php:688
msgid "Smartphone Icon"
msgstr "Biểu tượng điện thoại thông minh"

#: includes/fields/class-acf-field-icon_picker.php:687
msgid "Slides Icon"
msgstr "Biểu tượng Slides"

#: includes/fields/class-acf-field-icon_picker.php:684
msgid "Shield Icon"
msgstr "Biểu tượng khiên"

#: includes/fields/class-acf-field-icon_picker.php:681
msgid "Share Icon"
msgstr "Biểu tượng chia sẻ"

#: includes/fields/class-acf-field-icon_picker.php:680
msgid "Search Icon"
msgstr "Biểu tượng tìm kiếm"

#: includes/fields/class-acf-field-icon_picker.php:679
msgid "Screen Options Icon"
msgstr "Biểu tượng tuỳ chọn trang"

#: includes/fields/class-acf-field-icon_picker.php:678
msgid "Schedule Icon"
msgstr "Biểu tượng lịch trình"

#: includes/fields/class-acf-field-icon_picker.php:673
msgid "Redo Icon"
msgstr "Biểu tượng làm lại"

#: includes/fields/class-acf-field-icon_picker.php:671
msgid "Randomize Icon"
msgstr "Biểu tượng ngẫu nhiên"

#: includes/fields/class-acf-field-icon_picker.php:670
msgid "Products Icon"
msgstr "Biểu tượng sản phẩm"

#: includes/fields/class-acf-field-icon_picker.php:667
msgid "Pressthis Icon"
msgstr "Nhấn vào biểu tượng này"

#: includes/fields/class-acf-field-icon_picker.php:666
msgid "Post Status Icon"
msgstr "Biểu tượng trạng thái bài viết"

#: includes/fields/class-acf-field-icon_picker.php:665
msgid "Portfolio Icon"
msgstr "Biểu tượng danh mục đầu tư"

#: includes/fields/class-acf-field-icon_picker.php:661
msgid "Plus Icon"
msgstr "Biểu tượng dấu cộng"

#: includes/fields/class-acf-field-icon_picker.php:659
msgid "Playlist Video Icon"
msgstr "Biểu tượng danh sách video"

#: includes/fields/class-acf-field-icon_picker.php:658
msgid "Playlist Audio Icon"
msgstr "Biểu tượng danh sách phát âm thanh"

#: includes/fields/class-acf-field-icon_picker.php:656
msgid "Phone Icon"
msgstr "Biểu tượng điện thoại"

#: includes/fields/class-acf-field-icon_picker.php:654
msgid "Performance Icon"
msgstr "Biểu tượng hiệu suất"

#: includes/fields/class-acf-field-icon_picker.php:652
msgid "Paperclip Icon"
msgstr "Biểu tượng kẹp giấy"

#: includes/fields/class-acf-field-icon_picker.php:648
msgid "No Icon"
msgstr "Không có biểu tượng"

#: includes/fields/class-acf-field-icon_picker.php:647
msgid "Networking Icon"
msgstr "Biểu tượng mạng"

#: includes/fields/class-acf-field-icon_picker.php:646
msgid "Nametag Icon"
msgstr "Biểu tượng thẻ tên"

#: includes/fields/class-acf-field-icon_picker.php:645
msgid "Move Icon"
msgstr "Biểu tượng di chuyển"

#: includes/fields/class-acf-field-icon_picker.php:643
msgid "Money Icon"
msgstr "Biểu tượng tiền"

#: includes/fields/class-acf-field-icon_picker.php:642
msgid "Minus Icon"
msgstr "Biểu tượng trừ"

#: includes/fields/class-acf-field-icon_picker.php:641
msgid "Migrate Icon"
msgstr "Biểu tượng di chuyển"

#: includes/fields/class-acf-field-icon_picker.php:640
msgid "Microphone Icon"
msgstr "Biểu tượng Microphone"

#: includes/fields/class-acf-field-icon_picker.php:635
msgid "Megaphone Icon"
msgstr "Biểu tượng loa phóng thanh"

#: includes/fields/class-acf-field-icon_picker.php:625
msgid "Marker Icon"
msgstr "Biểu tượng đánh dấu"

#: includes/fields/class-acf-field-icon_picker.php:624
msgid "Lock Icon"
msgstr "Biểu tượng khóa"

#: includes/fields/class-acf-field-icon_picker.php:622
msgid "Location Icon"
msgstr "Biểu tượng vị trí"

#: includes/fields/class-acf-field-icon_picker.php:621
msgid "List View Icon"
msgstr "Biểu tượng xem danh sách"

#: includes/fields/class-acf-field-icon_picker.php:619
msgid "Lightbulb Icon"
msgstr "Biểu tượng bóng đèn"

#: includes/fields/class-acf-field-icon_picker.php:618
msgid "Left Right Icon"
msgstr "Biểu tượng trái phải"

#: includes/fields/class-acf-field-icon_picker.php:617
msgid "Layout Icon"
msgstr "Biểu tượng bố cục"

#: includes/fields/class-acf-field-icon_picker.php:616
msgid "Laptop Icon"
msgstr "Biểu tượng Laptop"

#: includes/fields/class-acf-field-icon_picker.php:610
msgid "Info Icon"
msgstr "Biểu tượng thông tin"

#: includes/fields/class-acf-field-icon_picker.php:609
msgid "Index Card Icon"
msgstr "Biểu tượng thẻ chỉ mục"

#: includes/fields/class-acf-field-icon_picker.php:598
msgid "ID Icon"
msgstr "Biểu tượng ID"

#: includes/fields/class-acf-field-icon_picker.php:595
msgid "Hidden Icon"
msgstr "Biểu tượng ẩn"

#: includes/fields/class-acf-field-icon_picker.php:594
msgid "Heart Icon"
msgstr "Biểu tượng trái tim"

#: includes/fields/class-acf-field-icon_picker.php:592
msgid "Hammer Icon"
msgstr "Biểu tượng búa"

#: includes/fields/class-acf-field-icon_picker.php:470
#: includes/fields/class-acf-field-icon_picker.php:591
msgid "Groups Icon"
msgstr "Biểu tượng nhóm"

#: includes/fields/class-acf-field-icon_picker.php:590
msgid "Grid View Icon"
msgstr "Biểu tượng xem lưới"

#: includes/fields/class-acf-field-icon_picker.php:585
msgid "Forms Icon"
msgstr "Biểu tượng biểu mẫu"

#: includes/fields/class-acf-field-icon_picker.php:575
msgid "Flag Icon"
msgstr "Biểu tượng lá cờ"

#: includes/fields/class-acf-field-icon_picker.php:574
#: includes/fields/class-acf-field-icon_picker.php:601
msgid "Filter Icon"
msgstr "Biểu tượng bộ lọc"

#: includes/fields/class-acf-field-icon_picker.php:573
msgid "Feedback Icon"
msgstr "Biểu tượng phản hồi"

#: includes/fields/class-acf-field-icon_picker.php:572
msgid "Facebook (alt) Icon"
msgstr "Biểu tượng Facebook (thay thế)"

#: includes/fields/class-acf-field-icon_picker.php:571
msgid "Facebook Icon"
msgstr "Biểu tượng Facebook"

#: includes/fields/class-acf-field-icon_picker.php:570
msgid "External Icon"
msgstr "Biểu tượng bên ngoài"

#: includes/fields/class-acf-field-icon_picker.php:561
msgid "Email (alt) Icon"
msgstr "Biểu tượng Email (thay thế)"

#: includes/fields/class-acf-field-icon_picker.php:560
msgid "Email Icon"
msgstr "Biểu tượng Email"

#: includes/fields/class-acf-field-icon_picker.php:558
#: includes/fields/class-acf-field-icon_picker.php:584
#: includes/fields/class-acf-field-icon_picker.php:634
msgid "Video Icon"
msgstr "Biểu tượng video"

#: includes/fields/class-acf-field-icon_picker.php:557
msgid "Unlink Icon"
msgstr "Biểu tượng hủy liên kết"

#: includes/fields/class-acf-field-icon_picker.php:556
msgid "Underline Icon"
msgstr "Biểu tượng gạch dưới"

#: includes/fields/class-acf-field-icon_picker.php:554
msgid "Text Color Icon"
msgstr "Biểu tượng màu văn bản"

#: includes/fields/class-acf-field-icon_picker.php:553
msgid "Table Icon"
msgstr "Biểu tượng bảng"

#: includes/fields/class-acf-field-icon_picker.php:552
msgid "Strikethrough Icon"
msgstr "Biểu tượng gạch ngang"

#: includes/fields/class-acf-field-icon_picker.php:551
msgid "Spellcheck Icon"
msgstr "Biểu tượng kiểm tra chính tả"

#: includes/fields/class-acf-field-icon_picker.php:549
msgid "Remove Formatting Icon"
msgstr "Biểu tượng gỡ bỏ định dạng"

#: includes/fields/class-acf-field-icon_picker.php:548
#: includes/fields/class-acf-field-icon_picker.php:582
msgid "Quote Icon"
msgstr "Biểu tượng trích dẫn"

#: includes/fields/class-acf-field-icon_picker.php:547
msgid "Paste Word Icon"
msgstr "Biểu tượng dán từ word"

#: includes/fields/class-acf-field-icon_picker.php:546
msgid "Paste Text Icon"
msgstr "Biểu tượng dán văn bản"

#: includes/fields/class-acf-field-icon_picker.php:545
msgid "Paragraph Icon"
msgstr "Biểu tượng đoạn văn"

#: includes/fields/class-acf-field-icon_picker.php:544
msgid "Outdent Icon"
msgstr "Biểu tượng thụt lề trái"

#: includes/fields/class-acf-field-icon_picker.php:540
msgid "Kitchen Sink Icon"
msgstr "Biểu tượng bồn rửa chén"

#: includes/fields/class-acf-field-icon_picker.php:539
msgid "Justify Icon"
msgstr "Biểu tượng căn chỉnh đều"

#: includes/fields/class-acf-field-icon_picker.php:538
msgid "Italic Icon"
msgstr "Biểu tượng in nghiêng"

#: includes/fields/class-acf-field-icon_picker.php:537
msgid "Insert More Icon"
msgstr "Chèn thêm biểu tượng"

#: includes/fields/class-acf-field-icon_picker.php:536
msgid "Indent Icon"
msgstr "Biểu tượng thụt lề"

#: includes/fields/class-acf-field-icon_picker.php:535
msgid "Help Icon"
msgstr "Biểu tượng trợ giúp"

#: includes/fields/class-acf-field-icon_picker.php:534
msgid "Expand Icon"
msgstr "Mở rộng biểu tượng"

#: includes/fields/class-acf-field-icon_picker.php:532
msgid "Contract Icon"
msgstr "Biểu tượng hợp đồng"

#: includes/fields/class-acf-field-icon_picker.php:531
#: includes/fields/class-acf-field-icon_picker.php:628
msgid "Code Icon"
msgstr "Biểu tượng mã Code"

#: includes/fields/class-acf-field-icon_picker.php:530
msgid "Break Icon"
msgstr "Biểu tượng nghỉ"

#: includes/fields/class-acf-field-icon_picker.php:529
msgid "Bold Icon"
msgstr "Biểu tượng đậm"

#: includes/fields/class-acf-field-icon_picker.php:523
msgid "Edit Icon"
msgstr "Biểu tượng sửa"

#: includes/fields/class-acf-field-icon_picker.php:521
msgid "Download Icon"
msgstr "Biểu tượng tải về"

#: includes/fields/class-acf-field-icon_picker.php:520
msgid "Dismiss Icon"
msgstr "Biểu tượng loại bỏ"

#: includes/fields/class-acf-field-icon_picker.php:519
msgid "Desktop Icon"
msgstr "Biểu tượng màn hình chính"

#: includes/fields/class-acf-field-icon_picker.php:512
msgid "Dashboard Icon"
msgstr "Biểu tượng bảng điều khiển"

#: includes/fields/class-acf-field-icon_picker.php:495
msgid "Cloud Icon"
msgstr "Biểu tượng đám mây"

#: includes/fields/class-acf-field-icon_picker.php:494
msgid "Clock Icon"
msgstr "Biểu tượng đồng hồ"

#: includes/fields/class-acf-field-icon_picker.php:493
msgid "Clipboard Icon"
msgstr "Biểu tượng bảng ghi nhớ"

#: includes/fields/class-acf-field-icon_picker.php:492
msgid "Chart Pie Icon"
msgstr "Biểu tượng biểu đồ tròn"

#: includes/fields/class-acf-field-icon_picker.php:491
msgid "Chart Line Icon"
msgstr "Biểu tượng đường biểu đồ"

#: includes/fields/class-acf-field-icon_picker.php:490
msgid "Chart Bar Icon"
msgstr "Biểu tượng biểu đồ cột"

#: includes/fields/class-acf-field-icon_picker.php:489
msgid "Chart Area Icon"
msgstr "Biểu tượng khu vực biểu đồ"

#: includes/fields/class-acf-field-icon_picker.php:488
msgid "Category Icon"
msgstr "Biểu tượng danh mục"

#: includes/fields/class-acf-field-icon_picker.php:487
msgid "Cart Icon"
msgstr "Biểu tượng giỏ hàng"

#: includes/fields/class-acf-field-icon_picker.php:486
msgid "Carrot Icon"
msgstr "Biểu tượng cà rốt"

#: includes/fields/class-acf-field-icon_picker.php:483
msgid "Camera Icon"
msgstr "Biểu tượng máy ảnh"

#: includes/fields/class-acf-field-icon_picker.php:482
msgid "Calendar (alt) Icon"
msgstr "Biểu tượng lịch (thay thế)"

#: includes/fields/class-acf-field-icon_picker.php:481
msgid "Calendar Icon"
msgstr "Biểu tượng Lịch"

#: includes/fields/class-acf-field-icon_picker.php:478
msgid "Businesswoman Icon"
msgstr "Biểu tượng nữ doanh nhân"

#: includes/fields/class-acf-field-icon_picker.php:475
msgid "Building Icon"
msgstr "Biểu tượng tòa nhà"

#: includes/fields/class-acf-field-icon_picker.php:462
msgid "Book Icon"
msgstr "Biểu tượng sách"

#: includes/fields/class-acf-field-icon_picker.php:457
msgid "Backup Icon"
msgstr "Biểu tượng sao lưu"

#: includes/fields/class-acf-field-icon_picker.php:456
msgid "Awards Icon"
msgstr "Biểu tượng giải thưởng"

#: includes/fields/class-acf-field-icon_picker.php:455
msgid "Art Icon"
msgstr "Biểu tượng nghệ thuật"

#: includes/fields/class-acf-field-icon_picker.php:452
msgid "Arrow Up Icon"
msgstr "Biểu tượng mũi tên Lên"

#: includes/fields/class-acf-field-icon_picker.php:449
msgid "Arrow Right Icon"
msgstr "Biểu tượng mũi tên phải"

#: includes/fields/class-acf-field-icon_picker.php:446
msgid "Arrow Left Icon"
msgstr "Biểu tượng mũi tên trái"

#: includes/fields/class-acf-field-icon_picker.php:443
msgid "Arrow Down Icon"
msgstr "Biểu tượng mũi tên xuống"

#: includes/fields/class-acf-field-icon_picker.php:442
#: includes/fields/class-acf-field-icon_picker.php:626
msgid "Archive Icon"
msgstr "Biểu tượng lưu trữ"

#: includes/fields/class-acf-field-icon_picker.php:441
msgid "Analytics Icon"
msgstr "Biểu tượng phân tích"

#: includes/fields/class-acf-field-icon_picker.php:438
#: includes/fields/class-acf-field-icon_picker.php:528
msgid "Align Right Icon"
msgstr "Biểu tượng căn phải"

#: includes/fields/class-acf-field-icon_picker.php:435
msgid "Align None Icon"
msgstr "Biểu tượng Căn chỉnh không"

#: includes/fields/class-acf-field-icon_picker.php:434
#: includes/fields/class-acf-field-icon_picker.php:527
msgid "Align Left Icon"
msgstr "Biểu tượng căn trái"

#: includes/fields/class-acf-field-icon_picker.php:432
#: includes/fields/class-acf-field-icon_picker.php:526
msgid "Align Center Icon"
msgstr "Biểu tượng căn giữa"

#: includes/fields/class-acf-field-icon_picker.php:431
msgid "Album Icon"
msgstr "Biểu tượng Album"

#: includes/fields/class-acf-field-icon_picker.php:429
msgid "Users Icon"
msgstr "Biểu tượng người dùng"

#: includes/fields/class-acf-field-icon_picker.php:428
msgid "Tools Icon"
msgstr "Biểu tượng công cụ"

#: includes/fields/class-acf-field-icon_picker.php:424
msgid "Site Icon"
msgstr "Biểu tượng trang web"

#: includes/fields/class-acf-field-icon_picker.php:423
msgid "Settings Icon"
msgstr "Biểu tượng cài đặt"

#: includes/fields/class-acf-field-icon_picker.php:422
msgid "Post Icon"
msgstr "Biểu tượng bài viết"

#: includes/fields/class-acf-field-icon_picker.php:421
msgid "Plugins Icon"
msgstr "Biểu tượng Plugin"

#: includes/fields/class-acf-field-icon_picker.php:420
msgid "Page Icon"
msgstr "Biểu tượng trang"

#: includes/fields/class-acf-field-icon_picker.php:419
msgid "Network Icon"
msgstr "Biểu tượng mạng"

#: includes/fields/class-acf-field-icon_picker.php:418
msgid "Multisite Icon"
msgstr "Biểu tượng nhiều trang web"

#: includes/fields/class-acf-field-icon_picker.php:417
msgid "Media Icon"
msgstr "Biểu tượng media"

#: includes/fields/class-acf-field-icon_picker.php:416
msgid "Links Icon"
msgstr "Biểu tượng liên kết"

#: includes/fields/class-acf-field-icon_picker.php:415
msgid "Home Icon"
msgstr "Biểu tượng trang chủ"

#: includes/fields/class-acf-field-icon_picker.php:413
msgid "Customizer Icon"
msgstr "Biểu tượng công cụ tùy chỉnh"

#: includes/fields/class-acf-field-icon_picker.php:412
#: includes/fields/class-acf-field-icon_picker.php:736
msgid "Comments Icon"
msgstr "Biểu tượng bình luận"

#: includes/fields/class-acf-field-icon_picker.php:411
msgid "Collapse Icon"
msgstr "Biểu tượng thu gọn"

#: includes/fields/class-acf-field-icon_picker.php:410
msgid "Appearance Icon"
msgstr "Biểu tượng giao diện"

#: includes/fields/class-acf-field-icon_picker.php:414
msgid "Generic Icon"
msgstr "Biểu tượng chung"

#: includes/fields/class-acf-field-icon_picker.php:346
msgid "Icon picker requires a value."
msgstr "Bộ chọn biểu tượng yêu cầu một giá trị."

#: includes/fields/class-acf-field-icon_picker.php:341
msgid "Icon picker requires an icon type."
msgstr "Bộ chọn biểu tượng yêu cầu loại biểu tượng."

#: includes/fields/class-acf-field-icon_picker.php:310
msgid ""
"The available icons matching your search query have been updated in the icon "
"picker below."
msgstr ""
"Các biểu tượng có sẵn phù hợp với truy vấn tìm kiếm của bạn đã được cập nhật "
"trong bộ chọn biểu tượng bên dưới."

#: includes/fields/class-acf-field-icon_picker.php:309
msgid "No results found for that search term"
msgstr "Không tìm thấy kết quả cho thời gian tìm kiếm đó"

#: includes/fields/class-acf-field-icon_picker.php:291
msgid "Array"
msgstr "Array"

#: includes/fields/class-acf-field-icon_picker.php:290
msgid "String"
msgstr "Chuỗi"

#. translators: %s - link to documentation
#: includes/fields/class-acf-field-icon_picker.php:278
msgid "Specify the return format for the icon. %s"
msgstr "Chọn định dạng trở lại cho biểu tượng. %s"

#: includes/fields/class-acf-field-icon_picker.php:263
msgid "Select where content editors can choose the icon from."
msgstr "Chọn nơi các trình chỉnh sửa nội dung có thể chọn biểu tượng từ."

#: includes/fields/class-acf-field-icon_picker.php:224
msgid "The URL to the icon you'd like to use, or svg as Data URI"
msgstr "URL cho biểu tượng bạn muốn sử dụng, hoặc Svg như dữ liệu Uri"

#: includes/fields/class-acf-field-icon_picker.php:207
msgid "Browse Media Library"
msgstr "Duyệt thư viện Media"

#: includes/fields/class-acf-field-icon_picker.php:198
msgid "The currently selected image preview"
msgstr "Xem trước hình ảnh hiện đang được chọn"

#: includes/fields/class-acf-field-icon_picker.php:189
msgid "Click to change the icon in the Media Library"
msgstr "Click để thay đổi biểu tượng trong thư viện Media"

#: includes/fields/class-acf-field-icon_picker.php:84
msgid "Search icons..."
msgstr "Biểu tượng tìm kiếm..."

#: includes/fields/class-acf-field-icon_picker.php:53
msgid "Media Library"
msgstr "Thư viện media"

#: includes/fields/class-acf-field-icon_picker.php:49
msgid "Dashicons"
msgstr "Dashicons"

#: includes/fields/class-acf-field-icon_picker.php:26
msgid ""
"An interactive UI for selecting an icon. Select from Dashicons, the media "
"library, or a standalone URL input."
msgstr ""
"Một UI tương tác để chọn một biểu tượng. Chọn từ Dashicons, thư viện phương "
"tiện Media, hoặc nhập URL độc lập."

#: includes/fields/class-acf-field-icon_picker.php:23
msgid "Icon Picker"
msgstr "Bộ chọn biểu tượng"

#: src/Site_Health/Site_Health.php:781
msgid "JSON Load Paths"
msgstr "Json tải đường"

#: src/Site_Health/Site_Health.php:775
msgid "JSON Save Paths"
msgstr "Đường dẫn lưu JSON"

#: src/Site_Health/Site_Health.php:766
msgid "Registered ACF Forms"
msgstr "Các mẫu ACF đăng ký"

#: src/Site_Health/Site_Health.php:760
msgid "Shortcode Enabled"
msgstr "Shortcode được kích hoạt"

#: src/Site_Health/Site_Health.php:752
msgid "Field Settings Tabs Enabled"
msgstr "Bảng cài đặt trường được bật"

#: src/Site_Health/Site_Health.php:744
msgid "Field Type Modal Enabled"
msgstr "Chất loại hộp cửa sổ được kích hoạt"

#: src/Site_Health/Site_Health.php:736
msgid "Admin UI Enabled"
msgstr "Giao diện quản trị đã bật"

#: src/Site_Health/Site_Health.php:727
msgid "Block Preloading Enabled"
msgstr "Tải trước khối đã bật"

#: src/Site_Health/Site_Health.php:715
msgid "Blocks Per ACF Block Version"
msgstr "Các khối theo phiên bản khối ACF"

#: src/Site_Health/Site_Health.php:710
msgid "Blocks Per API Version"
msgstr "Khóa theo phiên bản API"

#: src/Site_Health/Site_Health.php:683
msgid "Registered ACF Blocks"
msgstr "Các khối ACF đã đăng ký"

#: src/Site_Health/Site_Health.php:677
msgid "Light"
msgstr "Sáng"

#: src/Site_Health/Site_Health.php:677
msgid "Standard"
msgstr "Tiêu chuẩn"

#: src/Site_Health/Site_Health.php:676
msgid "REST API Format"
msgstr "Khởi động API"

#: src/Site_Health/Site_Health.php:668
msgid "Registered Options Pages (PHP)"
msgstr "Trang cài đặt đăng ký (PHP)"

#: src/Site_Health/Site_Health.php:654
msgid "Registered Options Pages (JSON)"
msgstr "Trang cài đặt đăng ký (JSON)"

#: src/Site_Health/Site_Health.php:649
msgid "Registered Options Pages (UI)"
msgstr "Trang cài đặt đăng ký (UI)"

#: src/Site_Health/Site_Health.php:619
msgid "Options Pages UI Enabled"
msgstr "Giao diện trang cài đặt đã bật"

#: src/Site_Health/Site_Health.php:611
msgid "Registered Taxonomies (JSON)"
msgstr "Phân loại đã đăng ký (JSON)"

#: src/Site_Health/Site_Health.php:599
msgid "Registered Taxonomies (UI)"
msgstr "Phân loại đã đăng ký (UI)"

#: src/Site_Health/Site_Health.php:587
msgid "Registered Post Types (JSON)"
msgstr "Loại nội dung đã đăng ký (JSON)"

#: src/Site_Health/Site_Health.php:575
msgid "Registered Post Types (UI)"
msgstr "Loại nội dung đã đăng ký (UI)"

#: src/Site_Health/Site_Health.php:562
msgid "Post Types and Taxonomies Enabled"
msgstr "Loại nội dung và phân loại đã được bật"

#: src/Site_Health/Site_Health.php:555
msgid "Number of Third Party Fields by Field Type"
msgstr "Số trường bên thứ ba theo loại trường"

#: src/Site_Health/Site_Health.php:550
msgid "Number of Fields by Field Type"
msgstr "Số trường theo loại trường"

#: src/Site_Health/Site_Health.php:449
msgid "Field Groups Enabled for GraphQL"
msgstr "Nhóm trường đã được kích hoạt cho GraphQL"

#: src/Site_Health/Site_Health.php:436
msgid "Field Groups Enabled for REST API"
msgstr "Nhóm trường đã được kích hoạt cho REST API"

#: src/Site_Health/Site_Health.php:424
msgid "Registered Field Groups (JSON)"
msgstr "Nhóm trường đã đăng ký (JSON)"

#: src/Site_Health/Site_Health.php:412
msgid "Registered Field Groups (PHP)"
msgstr "Nhóm trường đã đăng ký (PHP)"

#: src/Site_Health/Site_Health.php:400
msgid "Registered Field Groups (UI)"
msgstr "Nhóm trường đã đăng ký (UI)"

#: src/Site_Health/Site_Health.php:388
msgid "Active Plugins"
msgstr "Plugin hoạt động"

#: src/Site_Health/Site_Health.php:362
msgid "Parent Theme"
msgstr "Giao diện cha"

#: src/Site_Health/Site_Health.php:351
msgid "Active Theme"
msgstr "Giao diện hoạt động"

#: src/Site_Health/Site_Health.php:342
msgid "Is Multisite"
msgstr "Là nhiều trang"

#: src/Site_Health/Site_Health.php:337
msgid "MySQL Version"
msgstr "Phiên bản MySQL"

#: src/Site_Health/Site_Health.php:332
msgid "WordPress Version"
msgstr "Phiên bản WordPress"

#: src/Site_Health/Site_Health.php:325
msgid "Subscription Expiry Date"
msgstr "Đăng ký ngày hết hạn"

#: src/Site_Health/Site_Health.php:317
msgid "License Status"
msgstr "Trạng thái bản quyền"

#: src/Site_Health/Site_Health.php:312
msgid "License Type"
msgstr "Loại bản quyền"

#: src/Site_Health/Site_Health.php:307
msgid "Licensed URL"
msgstr "URL được cấp phép"

#: src/Site_Health/Site_Health.php:301
msgid "License Activated"
msgstr "Bản quyền được kích hoạt"

#: src/Site_Health/Site_Health.php:290
msgid "Free"
msgstr "Miễn phí"

#: src/Site_Health/Site_Health.php:289
msgid "Plugin Type"
msgstr "Plugin loại"

#: src/Site_Health/Site_Health.php:284
msgid "Plugin Version"
msgstr "Phiên bản plugin"

#: src/Site_Health/Site_Health.php:255
msgid ""
"This section contains debug information about your ACF configuration which "
"can be useful to provide to support."
msgstr ""
"Phần này chứa thông tin gỡ lỗi về cấu hình ACF của bạn, có thể hữu ích để "
"cung cấp cho bộ phận hỗ trợ."

#: includes/assets.php:373
msgid "An ACF Block on this page requires attention before you can save."
msgstr "Một khối ACF trên trang này cần được chú ý trước khi bạn có thể lưu."

#. translators: %s - The clear log button opening HTML tag. %s - The closing
#. HTML tag.
#: includes/admin/views/escaped-html-notice.php:63
msgid ""
"This data is logged as we detect values that have been changed during "
"output. %1$sClear log and dismiss%2$s after escaping the values in your "
"code. The notice will reappear if we detect changed values again."
msgstr ""
"Dữ liệu này được ghi lại khi chúng tôi phát hiện các giá trị đã bị thay đổi "
"trong quá trình xuất. %1$sXóa nhật ký và đóng thông báo%2$s sau khi bạn đã "
"thoát các giá trị trong mã của mình. Thông báo sẽ xuất hiện lại nếu chúng "
"tôi phát hiện các giá trị bị thay đổi lần nữa."

#: includes/admin/views/escaped-html-notice.php:25
msgid "Dismiss permanently"
msgstr "Xóa vĩnh viễn"

#: includes/admin/views/acf-field-group/field.php:220
msgid "Instructions for content editors. Shown when submitting data."
msgstr ""
"Hướng dẫn dành cho người trình chỉnh sửa nội dung. Hiển thị khi gửi dữ liệu."

#: includes/admin/post-types/admin-field-group.php:143
msgid "Has no term selected"
msgstr "Không có thời hạn được chọn"

#: includes/admin/post-types/admin-field-group.php:142
msgid "Has any term selected"
msgstr "Có bất kỳ mục phân loại nào được chọn"

#: includes/admin/post-types/admin-field-group.php:141
msgid "Terms do not contain"
msgstr "Các mục phân loại không chứa"

#: includes/admin/post-types/admin-field-group.php:140
msgid "Terms contain"
msgstr "Mục phân loại chứa"

#: includes/admin/post-types/admin-field-group.php:139
msgid "Term is not equal to"
msgstr "Thời gian không tương đương với"

#: includes/admin/post-types/admin-field-group.php:138
msgid "Term is equal to"
msgstr "Thời gian tương đương với"

#: includes/admin/post-types/admin-field-group.php:137
msgid "Has no user selected"
msgstr "Không có người dùng được chọn"

#: includes/admin/post-types/admin-field-group.php:136
msgid "Has any user selected"
msgstr "Có người dùng đã chọn"

#: includes/admin/post-types/admin-field-group.php:135
msgid "Users do not contain"
msgstr "Người dùng không chứa"

#: includes/admin/post-types/admin-field-group.php:134
msgid "Users contain"
msgstr "Người dùng có chứa"

#: includes/admin/post-types/admin-field-group.php:133
msgid "User is not equal to"
msgstr "Người dùng không bình đẳng với"

#: includes/admin/post-types/admin-field-group.php:132
msgid "User is equal to"
msgstr "Người dùng cũng giống như"

#: includes/admin/post-types/admin-field-group.php:131
msgid "Has no page selected"
msgstr "Không có trang được chọn"

#: includes/admin/post-types/admin-field-group.php:130
msgid "Has any page selected"
msgstr "Có bất kỳ trang nào được chọn"

#: includes/admin/post-types/admin-field-group.php:129
msgid "Pages do not contain"
msgstr "Các trang không chứa"

#: includes/admin/post-types/admin-field-group.php:128
msgid "Pages contain"
msgstr "Các trang chứa"

#: includes/admin/post-types/admin-field-group.php:127
msgid "Page is not equal to"
msgstr "Trang không tương đương với"

#: includes/admin/post-types/admin-field-group.php:126
msgid "Page is equal to"
msgstr "Trang này tương đương với"

#: includes/admin/post-types/admin-field-group.php:125
msgid "Has no relationship selected"
msgstr "Không có mối quan hệ được chọn"

#: includes/admin/post-types/admin-field-group.php:124
msgid "Has any relationship selected"
msgstr "Có bất kỳ mối quan hệ nào được chọn"

#: includes/admin/post-types/admin-field-group.php:123
msgid "Has no post selected"
msgstr "Không có bài viết được chọn"

#: includes/admin/post-types/admin-field-group.php:122
msgid "Has any post selected"
msgstr "Có bất kỳ bài viết nào được chọn"

#: includes/admin/post-types/admin-field-group.php:121
msgid "Posts do not contain"
msgstr "Các bài viết không chứa"

#: includes/admin/post-types/admin-field-group.php:120
msgid "Posts contain"
msgstr "Bài viết chứa"

#: includes/admin/post-types/admin-field-group.php:119
msgid "Post is not equal to"
msgstr "Bài viết không bằng với"

#: includes/admin/post-types/admin-field-group.php:118
msgid "Post is equal to"
msgstr "Bài viết tương đương với"

#: includes/admin/post-types/admin-field-group.php:117
msgid "Relationships do not contain"
msgstr "Mối quan hệ không chứa"

#: includes/admin/post-types/admin-field-group.php:116
msgid "Relationships contain"
msgstr "Mối quan hệ bao gồm"

#: includes/admin/post-types/admin-field-group.php:115
msgid "Relationship is not equal to"
msgstr "Mối quan hệ không tương đương với"

#: includes/admin/post-types/admin-field-group.php:114
msgid "Relationship is equal to"
msgstr "Mối quan hệ tương đương với"

#: src/Blocks/Bindings.php:38
msgctxt "The core ACF block binding source name for fields on the current page"
msgid "ACF Fields"
msgstr "Các trường ACF"

#: includes/admin/views/browse-fields-modal.php:14
msgid "ACF PRO Feature"
msgstr "Tính năng ACF PRO"

#: includes/admin/views/browse-fields-modal.php:10
msgid "Renew PRO to Unlock"
msgstr "Gia hạn PRO để mở khóa"

#: includes/admin/views/browse-fields-modal.php:8
msgid "Renew PRO License"
msgstr "Gia hạn giấy phép PRO"

#: includes/admin/views/acf-field-group/field.php:41
msgid "PRO fields cannot be edited without an active license."
msgstr "Không thể chỉnh sửa các trường PRO mà không có giấy phép hoạt động."

#: includes/admin/admin-internal-post-type-list.php:232
msgid ""
"Please activate your ACF PRO license to edit field groups assigned to an ACF "
"Block."
msgstr ""
"Vui lòng kích hoạt giấy phép ACF PRO của bạn để chỉnh sửa các nhóm trường "
"được gán cho một Khối ACF."

#: includes/admin/admin-internal-post-type-list.php:231
msgid "Please activate your ACF PRO license to edit this options page."
msgstr ""
"Vui lòng kích hoạt giấy phép ACF PRO của bạn để chỉnh sửa Trang cài đặt này."

#: includes/api/api-template.php:385 includes/api/api-template.php:439
msgid ""
"Returning escaped HTML values is only possible when format_value is also "
"true. The field values have not been returned for security."
msgstr ""
"Chỉ có thể trả lại các giá trị HTML đã thoát khi format_value cũng là đúng. "
"Các giá trị trường chưa được trả lại vì lý do bảo mật."

#: includes/api/api-template.php:46 includes/api/api-template.php:251
#: includes/api/api-template.php:947
msgid ""
"Returning an escaped HTML value is only possible when format_value is also "
"true. The field value has not been returned for security."
msgstr ""
"Chỉ có thể trả lại một giá trị HTML đã thoát khi format_value cũng là đúng. "
"Giá trị trường chưa được trả lại vì lý do bảo mật."

#. translators: %1$s - name of the ACF plugin. %2$s - Link to documentation.
#: includes/admin/views/escaped-html-notice.php:32
msgid ""
"%1$s ACF now automatically escapes unsafe HTML when rendered by "
"<code>the_field</code> or the ACF shortcode. We've detected the output of "
"some of your fields has been modified by this change, but this may not be a "
"breaking change. %2$s."
msgstr ""
"%1$s ACF giờ đây tự động thoát HTML không an toàn khi được hiển thị bởi "
"<code>the_field</code> hoặc mã ngắn ACF. Chúng tôi đã phát hiện đầu ra của "
"một số trường của bạn đã được sửa đổi bởi thay đổi này, nhưng đây có thể "
"không phải là một thay đổi đột ngột. %2$s."

#: includes/admin/views/escaped-html-notice.php:27
msgid "Please contact your site administrator or developer for more details."
msgstr ""
"Vui lòng liên hệ với quản trị viên hoặc nhà phát triển trang web của bạn để "
"biết thêm chi tiết."

#: includes/admin/views/escaped-html-notice.php:5
msgid "Learn&nbsp;more"
msgstr "Tìm hiểu thêm"

#: includes/admin/admin.php:63
msgid "Hide&nbsp;details"
msgstr "Ẩn&nbsp;chi tiết"

#: includes/admin/admin.php:62 includes/admin/views/escaped-html-notice.php:24
msgid "Show&nbsp;details"
msgstr "Hiển thị&nbsp;chi tiết"

#. translators: %1$s - The selector used  %2$s The field name  3%$s The parent
#. function name
#: includes/admin/views/escaped-html-notice.php:49
msgid "%1$s (%2$s) - rendered via %3$s"
msgstr "%1$s (%2$s) - được hiển thị qua %3$s"

#: includes/admin/views/global/navigation.php:229
msgid "Renew ACF PRO License"
msgstr "Gia hạn bản quyền ACF PRO"

#: includes/admin/views/acf-field-group/pro-features.php:17
msgid "Renew License"
msgstr "Gia hạn bản quyền"

#: includes/admin/views/acf-field-group/pro-features.php:14
msgid "Manage License"
msgstr "Quản lý bản quyền"

#: includes/admin/views/acf-field-group/options.php:102
msgid "'High' position not supported in the Block Editor"
msgstr "'Vị trí cao' không được hỗ trợ trong Trình chỉnh sửa Khối"

#: includes/admin/views/options-page-preview.php:30
msgid "Upgrade to ACF PRO"
msgstr "Nâng cấp lên ACF PRO"

#. translators: %s URL to ACF options pages documentation
#: includes/admin/views/options-page-preview.php:7
msgid ""
"ACF <a href=\"%s\" target=\"_blank\">options pages</a> are custom admin "
"pages for managing global settings via fields. You can create multiple pages "
"and sub-pages."
msgstr ""
"Trang cài đặt ACF <a href=\"%s\" target=\"_blank\"></a> là các trang quản "
"trị tùy chỉnh để quản lý cài đặt toàn cầu thông qua các trường. Bạn có thể "
"tạo nhiều trang và trang con."

#: includes/admin/views/global/header.php:35
msgid "Add Options Page"
msgstr "Thêm trang cài đặt"

#: includes/admin/views/acf-post-type/advanced-settings.php:708
msgid "In the editor used as the placeholder of the title."
msgstr "Trong trình chỉnh sửa được sử dụng như là văn bản gọi ý của tiêu đề."

#: includes/admin/views/acf-post-type/advanced-settings.php:707
msgid "Title Placeholder"
msgstr "Văn bản gợi ý cho tiêu đề"

#: includes/admin/views/global/navigation.php:97
msgid "4 Months Free"
msgstr "Miễn phí 4 tháng"

#. translators: %s - A singular label for a post type or taxonomy.
#: includes/admin/views/global/form-top.php:59
msgid "(Duplicated from %s)"
msgstr "(Đã sao chép từ %s)"

#: includes/admin/tools/class-acf-admin-tool-export.php:289
msgid "Select Options Pages"
msgstr "Chọn trang cài đặt"

#: includes/admin/post-types/admin-taxonomy.php:107
msgid "Duplicate taxonomy"
msgstr "Sao chép phân loại"

#: includes/admin/post-types/admin-post-type.php:106
#: includes/admin/post-types/admin-taxonomy.php:106
msgid "Create taxonomy"
msgstr "Tạo phân loại"

#: includes/admin/post-types/admin-post-type.php:105
msgid "Duplicate post type"
msgstr "Sao chép loại nội dung"

#: includes/admin/post-types/admin-post-type.php:104
#: includes/admin/post-types/admin-taxonomy.php:108
msgid "Create post type"
msgstr "Tạo loại nội dung"

#: includes/admin/post-types/admin-post-type.php:103
#: includes/admin/post-types/admin-taxonomy.php:105
msgid "Link field groups"
msgstr "Liên kết nhóm trường"

#: includes/admin/post-types/admin-post-type.php:102
#: includes/admin/post-types/admin-taxonomy.php:104
msgid "Add fields"
msgstr "Thêm trường"

#: includes/admin/post-types/admin-field-group.php:147
msgid "This Field"
msgstr "Trường này"

#: includes/admin/admin.php:361
msgid "ACF PRO"
msgstr "ACF PRO"

#: includes/admin/admin.php:359
msgid "Feedback"
msgstr "Phản hồi"

#: includes/admin/admin.php:357
msgid "Support"
msgstr "Hỗ trợ"

#. translators: This text is prepended by a link to ACF's website, and appended
#. by a link to WP Engine's website.
#: includes/admin/admin.php:332
msgid "is developed and maintained by"
msgstr "được phát triển và duy trì bởi"

#. translators: %s - either "post type" or "taxonomy"
#: includes/admin/admin-internal-post-type.php:313
msgid "Add this %s to the location rules of the selected field groups."
msgstr "Thêm %s này vào quy tắc vị trí của các nhóm trường đã chọn."

#. translators: %s the URL to ACF's bidirectional relationship documentation
#: includes/acf-bidirectional-functions.php:272
msgid ""
"Enabling the bidirectional setting allows you to update a value in the "
"target fields for each value selected for this field, adding or removing the "
"Post ID, Taxonomy ID or User ID of the item being updated. For more "
"information, please read the <a href=\"%s\" target=\"_blank\">documentation</"
"a>."
msgstr ""
"Kích hoạt cài đặt hai chiều cho phép bạn cập nhật một giá trị trong các "
"trường mục tiêu cho mỗi giá trị được chọn cho trường này, thêm hoặc xóa ID "
"bài viết, ID phân loại hoặc ID người dùng của mục đang được cập nhật. Để "
"biết thêm thông tin, vui lòng đọc <a href=\"%s\" target=\"_blank\">tài liệu</"
"a>."

#: includes/acf-bidirectional-functions.php:248
msgid ""
"Select field(s) to store the reference back to the item being updated. You "
"may select this field. Target fields must be compatible with where this "
"field is being displayed. For example, if this field is displayed on a "
"Taxonomy, your target field should be of type Taxonomy"
msgstr ""
"Chọn trường để lưu trữ tham chiếu trở lại mục đang được cập nhật. Bạn có thể "
"chọn trường này. Các trường mục tiêu phải tương thích với nơi trường này "
"được hiển thị. Ví dụ, nếu trường này được hiển thị trên một Phân loại, "
"trường mục tiêu của bạn nên là loại Phân loại"

#: includes/acf-bidirectional-functions.php:247
msgid "Target Field"
msgstr "Trường mục tiêu"

#: includes/acf-bidirectional-functions.php:221
msgid "Update a field on the selected values, referencing back to this ID"
msgstr ""
"Cập nhật một trường trên các giá trị đã chọn, tham chiếu trở lại ID này"

#: includes/acf-bidirectional-functions.php:220
msgid "Bidirectional"
msgstr "Hai chiều"

#. translators: %s A field type name, such as "Relationship"
#: includes/acf-bidirectional-functions.php:193
msgid "%s Field"
msgstr "Trường %s"

#: includes/fields/class-acf-field-page_link.php:498
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-select.php:378
#: includes/fields/class-acf-field-user.php:111
msgid "Select Multiple"
msgstr "Chọn nhiều"

#: includes/admin/views/global/navigation.php:241
msgid "WP Engine logo"
msgstr "Logo WP Engine"

#: includes/admin/views/acf-taxonomy/basic-settings.php:58
msgid "Lower case letters, underscores and dashes only, Max 32 characters."
msgstr ""
"Chỉ sử dụng chữ cái thường, dấu gạch dưới và dấu gạch ngang, tối đa 32 ký tự."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1156
msgid "The capability name for assigning terms of this taxonomy."
msgstr "Tên khả năng để gán các mục phân loại của phân loại này."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1155
msgid "Assign Terms Capability"
msgstr "Khả năng gán mục phân loại"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1139
msgid "The capability name for deleting terms of this taxonomy."
msgstr "Tên khả năng để xóa các mục phân loại của phân loại này."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1138
msgid "Delete Terms Capability"
msgstr "Khả năng xóa mục phân loại"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1122
msgid "The capability name for editing terms of this taxonomy."
msgstr "Tên khả năng để chỉnh sửa các mục phân loại của phân loại này."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1121
msgid "Edit Terms Capability"
msgstr "Khả năng chỉnh sửa mục phân loại"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1105
msgid "The capability name for managing terms of this taxonomy."
msgstr "Tên khả năng để quản lý các mục phân loại của phân loại này."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1104
msgid "Manage Terms Capability"
msgstr "Khả năng quản lý mục phân loại"

#: includes/admin/views/acf-post-type/advanced-settings.php:929
msgid ""
"Sets whether posts should be excluded from search results and taxonomy "
"archive pages."
msgstr ""
"Đặt xem các bài viết có nên được loại khỏi kết quả tìm kiếm và trang lưu trữ "
"phân loại hay không."

#: includes/admin/views/acf-field-group/pro-features.php:78
msgid "More Tools from WP Engine"
msgstr "Thêm công cụ từ WP Engine"

#. translators: %s - WP Engine logo
#: includes/admin/views/acf-field-group/pro-features.php:73
msgid "Built for those that build with WordPress, by the team at %s"
msgstr "Được tạo cho những người xây dựng với WordPress, bởi đội ngũ %s"

#: includes/admin/views/acf-field-group/pro-features.php:6
msgid "View Pricing & Upgrade"
msgstr "Xem giá & Nâng cấp"

#: includes/admin/views/acf-field-group/pro-features.php:3
#: includes/admin/views/options-page-preview.php:29
#: includes/fields/class-acf-field-icon_picker.php:273
msgid "Learn More"
msgstr "Tìm hiểu thêm"

#: includes/admin/views/acf-field-group/pro-features.php:28
msgid ""
"Speed up your workflow and develop better websites with features like ACF "
"Blocks and Options Pages, and sophisticated field types like Repeater, "
"Flexible Content, Clone, and Gallery."
msgstr ""
"Tăng tốc độ công việc và phát triển các trang web tốt hơn với các tính năng "
"như Khối ACF và Trang cài đặt, và Các loại trường phức tạp như Lặp lại, Nội "
"dung linh hoạt, Tạo bản sao và Album ảnh."

#: includes/admin/views/acf-field-group/pro-features.php:2
msgid "Unlock Advanced Features and Build Even More with ACF PRO"
msgstr "Mở khóa các tính năng nâng cao và xây dựng thêm nhiều hơn với ACF PRO"

#. translators: %s - singular label of post type/taxonomy, i.e. "Movie"/"Genre"
#: includes/admin/views/global/form-top.php:19
msgid "%s fields"
msgstr "Các trường %s"

#: includes/admin/post-types/admin-taxonomies.php:267
msgid "No terms"
msgstr "Không có mục phân loại"

#: includes/admin/post-types/admin-taxonomies.php:240
msgid "No post types"
msgstr "Không có loại nội dung"

#: includes/admin/post-types/admin-post-types.php:264
msgid "No posts"
msgstr "Không có bài viết"

#: includes/admin/post-types/admin-post-types.php:238
msgid "No taxonomies"
msgstr "Không có phân loại"

#: includes/admin/post-types/admin-post-types.php:183
#: includes/admin/post-types/admin-taxonomies.php:182
msgid "No field groups"
msgstr "Không có nhóm trường"

#: includes/admin/post-types/admin-field-groups.php:255
msgid "No fields"
msgstr "Không có trường"

#: includes/admin/post-types/admin-field-groups.php:128
#: includes/admin/post-types/admin-post-types.php:147
#: includes/admin/post-types/admin-taxonomies.php:146
msgid "No description"
msgstr "Không có mô tả"

#: includes/fields/class-acf-field-page_link.php:465
#: includes/fields/class-acf-field-post_object.php:374
#: includes/fields/class-acf-field-relationship.php:573
msgid "Any post status"
msgstr "Bất kỳ trạng thái bài viết nào"

#: includes/post-types/class-acf-taxonomy.php:288
msgid ""
"This taxonomy key is already in use by another taxonomy registered outside "
"of ACF and cannot be used."
msgstr ""
"Khóa phân loại này đã được sử dụng bởi một phân loại khác đã đăng ký bên "
"ngoài ACF và không thể sử dụng."

#: includes/post-types/class-acf-taxonomy.php:284
msgid ""
"This taxonomy key is already in use by another taxonomy in ACF and cannot be "
"used."
msgstr ""
"Khóa phân loại này đã được sử dụng bởi một phân loại khác trong ACF và không "
"thể sử dụng."

#: includes/post-types/class-acf-taxonomy.php:256
msgid ""
"The taxonomy key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"Khóa phân loại chỉ được chứa các ký tự chữ và số viết thường, dấu gạch dưới "
"hoặc dấu gạch ngang."

#: includes/post-types/class-acf-taxonomy.php:251
msgid "The taxonomy key must be under 32 characters."
msgstr "Khóa phân loại phải dưới 32 ký tự."

#: includes/post-types/class-acf-taxonomy.php:99
msgid "No Taxonomies found in Trash"
msgstr "Không tìm thấy phân loại nào trong thùng rác"

#: includes/post-types/class-acf-taxonomy.php:98
msgid "No Taxonomies found"
msgstr "Không tìm thấy phân loại"

#: includes/post-types/class-acf-taxonomy.php:97
msgid "Search Taxonomies"
msgstr "Tìm kiếm phân loại"

#: includes/post-types/class-acf-taxonomy.php:96
msgid "View Taxonomy"
msgstr "Xem phân loại"

#: includes/post-types/class-acf-taxonomy.php:95
msgid "New Taxonomy"
msgstr "Phân loại mới"

#: includes/post-types/class-acf-taxonomy.php:94
msgid "Edit Taxonomy"
msgstr "Chỉnh sửa phân loại"

#: includes/post-types/class-acf-taxonomy.php:93
msgid "Add New Taxonomy"
msgstr "Thêm phân loại mới"

#: includes/post-types/class-acf-post-type.php:100
msgid "No Post Types found in Trash"
msgstr "Không tìm thấy loại nội dung trong thùng rác"

#: includes/post-types/class-acf-post-type.php:99
msgid "No Post Types found"
msgstr "Không tìm thấy loại nội dung"

#: includes/post-types/class-acf-post-type.php:98
msgid "Search Post Types"
msgstr "Tìm kiếm loại nội dung"

#: includes/post-types/class-acf-post-type.php:97
msgid "View Post Type"
msgstr "Xem loại nội dung"

#: includes/post-types/class-acf-post-type.php:96
msgid "New Post Type"
msgstr "Loại nội dung mới"

#: includes/post-types/class-acf-post-type.php:95
msgid "Edit Post Type"
msgstr "Chỉnh sửa loại nội dung"

#: includes/post-types/class-acf-post-type.php:94
msgid "Add New Post Type"
msgstr "Thêm loại nội dung mới"

#: includes/post-types/class-acf-post-type.php:366
msgid ""
"This post type key is already in use by another post type registered outside "
"of ACF and cannot be used."
msgstr ""
"Khóa loại nội dung này đã được sử dụng bởi một loại nội dung khác đã được "
"đăng ký bên ngoài ACF và không thể sử dụng."

#: includes/post-types/class-acf-post-type.php:361
msgid ""
"This post type key is already in use by another post type in ACF and cannot "
"be used."
msgstr ""
"Khóa loại nội dung này đã được sử dụng bởi một loại nội dung khác trong ACF "
"và không thể sử dụng."

#. translators: %s a link to WordPress.org's Reserved Terms page
#: includes/post-types/class-acf-post-type.php:339
#: includes/post-types/class-acf-taxonomy.php:262
msgid ""
"This field must not be a WordPress <a href=\"%s\" target=\"_blank\">reserved "
"term</a>."
msgstr ""
"Trường này không được là một <a href=\"%s\" target=\"_blank\">mục phân loại "
"dành riêng</a> của WordPress."

#: includes/post-types/class-acf-post-type.php:333
msgid ""
"The post type key must only contain lower case alphanumeric characters, "
"underscores or dashes."
msgstr ""
"Khóa loại nội dung chỉ được chứa các ký tự chữ và số viết thường, dấu gạch "
"dưới hoặc dấu gạch ngang."

#: includes/post-types/class-acf-post-type.php:328
msgid "The post type key must be under 20 characters."
msgstr "Khóa loại nội dung phải dưới 20 ký tự."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid "We do not recommend using this field in ACF Blocks."
msgstr "Chúng tôi không khuyến nghị sử dụng trường này trong ACF Blocks."

#: includes/fields/class-acf-field-wysiwyg.php:24
msgid ""
"Displays the WordPress WYSIWYG editor as seen in Posts and Pages allowing "
"for a rich text-editing experience that also allows for multimedia content."
msgstr ""
"Hiển thị trình soạn thảo WYSIWYG của WordPress như được thấy trong Bài viết "
"và Trang cho phép trải nghiệm chỉnh sửa văn bản phong phú cũng như cho phép "
"nội dung đa phương tiện."

#: includes/fields/class-acf-field-wysiwyg.php:22
msgid "WYSIWYG Editor"
msgstr "Trình soạn thảo trực quan"

#: includes/fields/class-acf-field-user.php:17
msgid ""
"Allows the selection of one or more users which can be used to create "
"relationships between data objects."
msgstr ""
"Cho phép chọn một hoặc nhiều người dùng có thể được sử dụng để tạo mối quan "
"hệ giữa các đối tượng dữ liệu."

#: includes/fields/class-acf-field-url.php:20
msgid "A text input specifically designed for storing web addresses."
msgstr "Một đầu vào văn bản được thiết kế đặc biệt để lưu trữ địa chỉ web."

#: includes/fields/class-acf-field-icon_picker.php:56
#: includes/fields/class-acf-field-url.php:19
msgid "URL"
msgstr "URL"

#: includes/fields/class-acf-field-true_false.php:24
msgid ""
"A toggle that allows you to pick a value of 1 or 0 (on or off, true or "
"false, etc). Can be presented as a stylized switch or checkbox."
msgstr ""
"Một công tắc cho phép bạn chọn một giá trị 1 hoặc 0 (bật hoặc tắt, đúng hoặc "
"sai, v.v.). Có thể được trình bày dưới dạng một công tắc hoặc hộp kiểm có "
"kiểu."

#: includes/fields/class-acf-field-time_picker.php:24
msgid ""
"An interactive UI for picking a time. The time format can be customized "
"using the field settings."
msgstr ""
"Giao diện người dùng tương tác để chọn thời gian. Định dạng thời gian có thể "
"được tùy chỉnh bằng cách sử dụng cài đặt trường."

#: includes/fields/class-acf-field-textarea.php:23
msgid "A basic textarea input for storing paragraphs of text."
msgstr "Một ô nhập liệu dạng văn bản cơ bản để lưu trữ các đoạn văn bản."

#: includes/fields/class-acf-field-text.php:23
msgid "A basic text input, useful for storing single string values."
msgstr "Một đầu vào văn bản cơ bản, hữu ích để lưu trữ các giá trị chuỗi đơn."

#: includes/fields/class-acf-field-taxonomy.php:22
msgid ""
"Allows the selection of one or more taxonomy terms based on the criteria and "
"options specified in the fields settings."
msgstr ""
"Cho phép chọn một hoặc nhiều mục phân loại phân loại dựa trên tiêu chí và "
"tùy chọn được chỉ định trong cài đặt trường."

#: includes/fields/class-acf-field-tab.php:25
msgid ""
"Allows you to group fields into tabbed sections in the edit screen. Useful "
"for keeping fields organized and structured."
msgstr ""
"Cho phép bạn nhóm các trường vào các phần có tab trong màn hình chỉnh sửa. "
"Hữu ích để giữ cho các trường được tổ chức và có cấu trúc."

#: includes/fields/class-acf-field-select.php:18
msgid "A dropdown list with a selection of choices that you specify."
msgstr "Một danh sách thả xuống với một lựa chọn các lựa chọn mà bạn chỉ định."

#: includes/fields/class-acf-field-relationship.php:19
msgid ""
"A dual-column interface to select one or more posts, pages, or custom post "
"type items to create a relationship with the item that you're currently "
"editing. Includes options to search and filter."
msgstr ""
"Giao diện hai cột để chọn một hoặc nhiều bài viết, trang hoặc mục loại nội "
"dung tùy chỉnh để tạo mối quan hệ với mục bạn đang chỉnh sửa. Bao gồm các "
"tùy chọn để tìm kiếm và lọc."

#: includes/fields/class-acf-field-range.php:23
msgid ""
"An input for selecting a numerical value within a specified range using a "
"range slider element."
msgstr ""
"Một đầu vào để chọn một giá trị số trong một phạm vi đã chỉ định bằng cách "
"sử dụng một phần tử thanh trượt phạm vi."

#: includes/fields/class-acf-field-radio.php:24
msgid ""
"A group of radio button inputs that allows the user to make a single "
"selection from values that you specify."
msgstr ""
"Một nhóm các đầu vào nút radio cho phép người dùng thực hiện một lựa chọn "
"duy nhất từ các giá trị mà bạn chỉ định."

#: includes/fields/class-acf-field-post_object.php:17
msgid ""
"An interactive and customizable UI for picking one or many posts, pages or "
"post type items with the option to search. "
msgstr ""
"Giao diện người dùng tương tác và tùy chỉnh để chọn một hoặc nhiều bài viết, "
"trang hoặc mục loại nội dung với tùy chọn tìm kiếm. "

#: includes/fields/class-acf-field-password.php:23
msgid "An input for providing a password using a masked field."
msgstr ""
"Một đầu vào để cung cấp mật khẩu bằng cách sử dụng một trường đã được che."

#: includes/fields/class-acf-field-page_link.php:457
#: includes/fields/class-acf-field-post_object.php:366
#: includes/fields/class-acf-field-relationship.php:565
msgid "Filter by Post Status"
msgstr "Lọc theo Trạng thái Bài viết"

#: includes/fields/class-acf-field-page_link.php:24
msgid ""
"An interactive dropdown to select one or more posts, pages, custom post type "
"items or archive URLs, with the option to search."
msgstr ""
"Một danh sách thả xuống tương tác để chọn một hoặc nhiều bài viết, trang, "
"mục loại nội dung tùy chỉnh hoặc URL lưu trữ, với tùy chọn tìm kiếm."

#: includes/fields/class-acf-field-oembed.php:24
msgid ""
"An interactive component for embedding videos, images, tweets, audio and "
"other content by making use of the native WordPress oEmbed functionality."
msgstr ""
"Một thành phần tương tác để nhúng video, hình ảnh, tweet, âm thanh và nội "
"dung khác bằng cách sử dụng chức năng oEmbed gốc của WordPress."

#: includes/fields/class-acf-field-number.php:23
msgid "An input limited to numerical values."
msgstr "Một đầu vào giới hạn cho các giá trị số."

#: includes/fields/class-acf-field-message.php:25
msgid ""
"Used to display a message to editors alongside other fields. Useful for "
"providing additional context or instructions around your fields."
msgstr ""
"Được sử dụng để hiển thị một thông điệp cho các trình chỉnh sửa cùng với các "
"trường khác. Hữu ích để cung cấp ngữ cảnh hoặc hướng dẫn bổ sung xung quanh "
"các trường của bạn."

#: includes/fields/class-acf-field-link.php:24
msgid ""
"Allows you to specify a link and its properties such as title and target "
"using the WordPress native link picker."
msgstr ""
"Cho phép bạn chỉ định một liên kết và các thuộc tính của nó như tiêu đề và "
"mục tiêu bằng cách sử dụng công cụ chọn liên kết gốc của WordPress."

#: includes/fields/class-acf-field-image.php:24
msgid "Uses the native WordPress media picker to upload, or choose images."
msgstr ""
"Sử dụng công cụ chọn phương tiện gốc của WordPress để tải lên hoặc chọn hình "
"ảnh."

#: includes/fields/class-acf-field-group.php:24
msgid ""
"Provides a way to structure fields into groups to better organize the data "
"and the edit screen."
msgstr ""
"Cung cấp một cách để cấu trúc các trường thành các nhóm để tổ chức dữ liệu "
"và màn hình chỉnh sửa tốt hơn."

#: includes/fields/class-acf-field-google-map.php:24
msgid ""
"An interactive UI for selecting a location using Google Maps. Requires a "
"Google Maps API key and additional configuration to display correctly."
msgstr ""
"Giao diện người dùng tương tác để chọn một vị trí bằng cách sử dụng Google "
"Maps. Yêu cầu một khóa API Google Maps và cấu hình bổ sung để hiển thị chính "
"xác."

#: includes/fields/class-acf-field-file.php:24
msgid "Uses the native WordPress media picker to upload, or choose files."
msgstr ""
"Sử dụng công cụ chọn phương tiện gốc của WordPress để tải lên hoặc chọn tệp."

#: includes/fields/class-acf-field-email.php:23
msgid "A text input specifically designed for storing email addresses."
msgstr "Một đầu vào văn bản được thiết kế đặc biệt để lưu trữ địa chỉ email."

#: includes/fields/class-acf-field-date_time_picker.php:24
msgid ""
"An interactive UI for picking a date and time. The date return format can be "
"customized using the field settings."
msgstr ""
"Giao diện người dùng tương tác để chọn ngày và giờ. Định dạng trả về ngày có "
"thể được tùy chỉnh bằng cách sử dụng cài đặt trường."

#: includes/fields/class-acf-field-date_picker.php:24
msgid ""
"An interactive UI for picking a date. The date return format can be "
"customized using the field settings."
msgstr ""
"Giao diện người dùng tương tác để chọn ngày. Định dạng trả về ngày có thể "
"được tùy chỉnh bằng cách sử dụng cài đặt trường."

#: includes/fields/class-acf-field-color_picker.php:24
msgid "An interactive UI for selecting a color, or specifying a Hex value."
msgstr "Giao diện người dùng tương tác để chọn màu hoặc chỉ định giá trị Hex."

#: includes/fields/class-acf-field-checkbox.php:24
msgid ""
"A group of checkbox inputs that allow the user to select one, or multiple "
"values that you specify."
msgstr ""
"Một nhóm các đầu vào hộp kiểm cho phép người dùng chọn một hoặc nhiều giá "
"trị mà bạn chỉ định."

#: includes/fields/class-acf-field-button-group.php:25
msgid ""
"A group of buttons with values that you specify, users can choose one option "
"from the values provided."
msgstr ""
"Một nhóm các nút với các giá trị mà bạn chỉ định, người dùng có thể chọn một "
"tùy chọn từ các giá trị được cung cấp."

#: includes/fields/class-acf-field-accordion.php:26
msgid ""
"Allows you to group and organize custom fields into collapsable panels that "
"are shown while editing content. Useful for keeping large datasets tidy."
msgstr ""
"Cho phép bạn nhóm và tổ chức các trường tùy chỉnh vào các bảng có thể thu "
"gọn được hiển thị trong khi chỉnh sửa nội dung. Hữu ích để giữ cho các tập "
"dữ liệu lớn gọn gàng."

#: includes/fields.php:449
msgid ""
"This provides a solution for repeating content such as slides, team members, "
"and call-to-action tiles, by acting as a parent to a set of subfields which "
"can be repeated again and again."
msgstr ""
"Điều này cung cấp một giải pháp để lặp lại nội dung như slide, thành viên "
"nhóm và ô kêu gọi hành động, bằng cách hoạt động như một cha cho một tập hợp "
"các trường con có thể được lặp lại đi lặp lại."

#: includes/fields.php:439
msgid ""
"This provides an interactive interface for managing a collection of "
"attachments. Most settings are similar to the Image field type. Additional "
"settings allow you to specify where new attachments are added in the gallery "
"and the minimum/maximum number of attachments allowed."
msgstr ""
"Điều này cung cấp một giao diện tương tác để quản lý một bộ sưu tập các tệp "
"đính kèm. Hầu hết các cài đặt tương tự như loại trường Hình ảnh. Cài đặt bổ "
"sung cho phép bạn chỉ định nơi thêm tệp đính kèm mới trong thư viện và số "
"lượng tệp đính kèm tối thiểu / tối đa được cho phép."

#: includes/fields.php:429
msgid ""
"This provides a simple, structured, layout-based editor. The Flexible "
"Content field allows you to define, create and manage content with total "
"control by using layouts and subfields to design the available blocks."
msgstr ""
"Điều này cung cấp một trình soạn thảo dựa trên bố cục, có cấu trúc, đơn "
"giản. Trường nội dung linh hoạt cho phép bạn định nghĩa, tạo và quản lý nội "
"dung với quyền kiểm soát tuyệt đối bằng cách sử dụng bố cục và trường con để "
"thiết kế các khối có sẵn."

#: includes/fields.php:419
msgid ""
"This allows you to select and display existing fields. It does not duplicate "
"any fields in the database, but loads and displays the selected fields at "
"run-time. The Clone field can either replace itself with the selected fields "
"or display the selected fields as a group of subfields."
msgstr ""
"Điều này cho phép bạn chọn và hiển thị các trường hiện có. Nó không sao chép "
"bất kỳ trường nào trong cơ sở dữ liệu, nhưng tải và hiển thị các trường đã "
"chọn tại thời gian chạy. Trường Clone có thể thay thế chính nó bằng các "
"trường đã chọn hoặc hiển thị các trường đã chọn dưới dạng một nhóm trường "
"con."

#: includes/fields.php:416
msgctxt "noun"
msgid "Clone"
msgstr "Sao chép"

#: includes/admin/views/global/navigation.php:86 includes/fields.php:331
#: src/Site_Health/Site_Health.php:290
msgid "PRO"
msgstr "PRO"

#: includes/fields.php:329 includes/fields.php:386
msgid "Advanced"
msgstr "Nâng cao"

#: includes/ajax/class-acf-ajax-local-json-diff.php:90
msgid "JSON (newer)"
msgstr "JSON (mới hơn)"

#: includes/ajax/class-acf-ajax-local-json-diff.php:86
msgid "Original"
msgstr "Gốc"

#: includes/ajax/class-acf-ajax-local-json-diff.php:60
msgid "Invalid post ID."
msgstr "ID bài viết không hợp lệ."

#: includes/ajax/class-acf-ajax-local-json-diff.php:52
msgid "Invalid post type selected for review."
msgstr "Loại nội dung được chọn để xem xét không hợp lệ."

#: includes/admin/views/global/navigation.php:192
msgid "More"
msgstr "Xem thêm"

#: includes/admin/views/browse-fields-modal.php:96
msgid "Tutorial"
msgstr "Hướng dẫn"

#: includes/admin/views/browse-fields-modal.php:73
msgid "Select Field"
msgstr "Chọn trường"

#. translators: %s: A link to the popular fields used in ACF
#: includes/admin/views/browse-fields-modal.php:60
msgid "Try a different search term or browse %s"
msgstr "Thử một từ khóa tìm kiếm khác hoặc duyệt %s"

#: includes/admin/views/browse-fields-modal.php:57
msgid "Popular fields"
msgstr "Các trường phổ biến"

#. translators: %s: The invalid search term
#: includes/admin/views/browse-fields-modal.php:50
#: includes/fields/class-acf-field-icon_picker.php:97
msgid "No search results for '%s'"
msgstr "Không có kết quả tìm kiếm cho '%s'"

#: includes/admin/views/browse-fields-modal.php:23
msgid "Search fields..."
msgstr "Tìm kiếm trường..."

#: includes/admin/views/browse-fields-modal.php:21
msgid "Select Field Type"
msgstr "Chọn loại trường"

#: includes/admin/views/browse-fields-modal.php:4
msgid "Popular"
msgstr "Phổ biến"

#: includes/admin/views/acf-taxonomy/list-empty.php:15
msgid "Add Taxonomy"
msgstr "Thêm phân loại"

#: includes/admin/views/acf-taxonomy/list-empty.php:14
msgid "Create custom taxonomies to classify post type content"
msgstr "Tạo phân loại tùy chỉnh để phân loại nội dung loại nội dung"

#: includes/admin/views/acf-taxonomy/list-empty.php:13
msgid "Add Your First Taxonomy"
msgstr "Thêm phân loại đầu tiên của bạn"

#: includes/admin/views/acf-taxonomy/basic-settings.php:122
msgid "Hierarchical taxonomies can have descendants (like categories)."
msgstr "Phân loại theo hệ thống có thể có các mục con (như các danh mục)."

#: includes/admin/views/acf-taxonomy/basic-settings.php:107
msgid "Makes a taxonomy visible on the frontend and in the admin dashboard."
msgstr ""
"Hiển thị phân loại trên giao diện người dùng và trên bảng điều khiển quản "
"trị."

#: includes/admin/views/acf-taxonomy/basic-settings.php:91
msgid "One or many post types that can be classified with this taxonomy."
msgstr "Một hoặc nhiều loại nội dung có thể được phân loại bằng phân loại này."

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:60
msgid "genre"
msgstr "thể loại"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:42
msgid "Genre"
msgstr "Thể loại"

#. translators: example taxonomy
#: includes/admin/views/acf-taxonomy/basic-settings.php:25
msgid "Genres"
msgstr "Các thể loại"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1231
msgid ""
"Optional custom controller to use instead of `WP_REST_Terms_Controller `."
msgstr ""
"Bộ điều khiển tùy chỉnh tùy chọn để sử dụng thay vì "
"`WP_REST_Terms_Controller `."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1175
msgid "Expose this post type in the REST API."
msgstr "Tiết lộ loại nội dung này trong REST API."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1075
msgid "Customize the query variable name"
msgstr "Tùy chỉnh tên biến truy vấn"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1048
msgid ""
"Terms can be accessed using the non-pretty permalink, e.g., {query_var}"
"={term_slug}."
msgstr ""
"Các mục phân loại có thể được truy cập bằng cách sử dụng đường dẫn cố định "
"không đẹp, ví dụ, {query_var}={term_slug}."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:1001
msgid "Parent-child terms in URLs for hierarchical taxonomies."
msgstr "Các mục phân loại cha-con trong URL cho các phân loại theo hệ thống."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:961
msgid "Customize the slug used in the URL"
msgstr "Tùy chỉnh đường dẫn cố định được sử dụng trong URL"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:944
msgid "Permalinks for this taxonomy are disabled."
msgstr "Liên kết cố định cho phân loại này đã bị vô hiệu hóa."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-taxonomy/advanced-settings.php:941
msgid ""
"Rewrite the URL using the taxonomy key as the slug. Your permalink structure "
"will be"
msgstr ""
"Thay đổi URL bằng cách sử dụng khóa phân loại làm đường dẫn cố định. Cấu "
"trúc đường dẫn cố định của bạn sẽ là"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:933
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1050
#: includes/admin/views/acf-taxonomy/basic-settings.php:57
msgid "Taxonomy Key"
msgstr "Liên kết phân loại"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:931
msgid "Select the type of permalink to use for this taxonomy."
msgstr "Chọn loại liên kết cố định để sử dụng cho phân loại này."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:916
msgid "Display a column for the taxonomy on post type listing screens."
msgstr "Hiển thị một cột cho phân loại trên màn hình liệt kê loại nội dung."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:915
msgid "Show Admin Column"
msgstr "Hiển thị cột quản trị"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:902
msgid "Show the taxonomy in the quick/bulk edit panel."
msgstr "Hiển thị phân loại trong bảng chỉnh sửa nhanh / hàng loạt."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:901
msgid "Quick Edit"
msgstr "Chỉnh sửa nhanh"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:888
msgid "List the taxonomy in the Tag Cloud Widget controls."
msgstr "Liệt kê phân loại trong các điều khiển Widget mây thẻ."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:887
msgid "Tag Cloud"
msgstr "Mây thẻ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:842
msgid ""
"A PHP function name to be called for sanitizing taxonomy data saved from a "
"meta box."
msgstr ""
"Tên hàm PHP sẽ được gọi để làm sạch dữ liệu phân loại được lưu từ một hộp "
"meta."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:841
msgid "Meta Box Sanitization Callback"
msgstr "Hàm Gọi lại Làm sạch Hộp Meta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:821
msgid "Register Meta Box Callback"
msgstr "Đăng ký Hàm Gọi lại Hộp Meta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:752
msgid "No Meta Box"
msgstr "Không có Hộp Meta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:751
msgid "Custom Meta Box"
msgstr "Hộp Meta tùy chỉnh"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:768
msgid ""
"Controls the meta box on the content editor screen. By default, the "
"Categories meta box is shown for hierarchical taxonomies, and the Tags meta "
"box is shown for non-hierarchical taxonomies."
msgstr ""
"Điều khiển hộp meta trên màn hình trình chỉnh sửa nội dung. Theo mặc định, "
"hộp meta danh mục được hiển thị cho các phân loại theo hệ thống, và hộp meta "
"Thẻ được hiển thị cho các phân loại không theo hệ thống."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:767
msgid "Meta Box"
msgstr "Hộp Meta"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:746
#: includes/admin/views/acf-taxonomy/advanced-settings.php:773
msgid "Categories Meta Box"
msgstr "Hộp Meta danh mục"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:745
#: includes/admin/views/acf-taxonomy/advanced-settings.php:772
msgid "Tags Meta Box"
msgstr "Hộp Meta Thẻ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:704
msgid "A link to a tag"
msgstr "Một liên kết đến một thẻ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:703
msgid "Describes a navigation link block variation used in the block editor."
msgstr ""
"Mô tả một biến thể khối liên kết điều hướng được sử dụng trong trình chỉnh "
"sửa khối."

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:698
msgid "A link to a %s"
msgstr "Một liên kết đến một %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:683
msgid "Tag Link"
msgstr "Liên kết Thẻ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:682
msgid ""
"Assigns a title for navigation link block variation used in the block editor."
msgstr ""
"Gán một tiêu đề cho biến thể khối liên kết điều hướng được sử dụng trong "
"trình chỉnh sửa khối."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:663
msgid "← Go to tags"
msgstr "← Đi đến thẻ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:662
msgid ""
"Assigns the text used to link back to the main index after updating a term."
msgstr ""
"Gán văn bản được sử dụng để liên kết trở lại chỉ mục chính sau khi cập nhật "
"một mục phân loại."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:661
msgid "Back To Items"
msgstr "Quay lại mục"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:657
msgid "← Go to %s"
msgstr "← Quay lại %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:642
msgid "Tags list"
msgstr "Danh sách thẻ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:641
msgid "Assigns text to the table hidden heading."
msgstr "Gán văn bản cho tiêu đề bảng ẩn."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:622
msgid "Tags list navigation"
msgstr "Danh sách điều hướng thẻ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:621
msgid "Assigns text to the table pagination hidden heading."
msgstr "Gán văn bản cho tiêu đề ẩn của phân trang bảng."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:597
msgid "Filter by category"
msgstr "Lọc theo danh mục"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:596
msgid "Assigns text to the filter button in the posts lists table."
msgstr "Gán văn bản cho nút lọc trong bảng danh sách bài viết."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:595
msgid "Filter By Item"
msgstr "Lọc theo mục"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:591
msgid "Filter by %s"
msgstr "Lọc theo %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:575
#: includes/admin/views/acf-taxonomy/advanced-settings.php:576
msgid ""
"The description is not prominent by default; however, some themes may show "
"it."
msgstr ""
"Mô tả không nổi bật theo mặc định; tuy nhiên, một số chủ đề có thể hiển thị "
"nó."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:574
msgid "Describes the Description field on the Edit Tags screen."
msgstr "Thông tin về trường mô tả trên màn hình chỉnh sửa thẻ."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:573
msgid "Description Field Description"
msgstr "Thông tin về trường mô tả"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:554
#: includes/admin/views/acf-taxonomy/advanced-settings.php:555
msgid ""
"Assign a parent term to create a hierarchy. The term Jazz, for example, "
"would be the parent of Bebop and Big Band"
msgstr ""
"Gán một mục phân loại cha để tạo ra một hệ thống phân cấp. Thuật ngữ Jazz, "
"ví dụ, sẽ là cha của Bebop và Big Band"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:553
msgid "Describes the Parent field on the Edit Tags screen."
msgstr "Mô tả trường cha trên màn hình chỉnh sửa thẻ."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:552
msgid "Parent Field Description"
msgstr "Mô tả trường cha"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:538
#: includes/admin/views/acf-taxonomy/advanced-settings.php:539
msgid ""
"The \"slug\" is the URL-friendly version of the name. It is usually all "
"lower case and contains only letters, numbers, and hyphens."
msgstr ""
"\"Đường dẫn cố định\" là phiên bản thân thiện với URL của tên. Nó thường là "
"tất cả chữ thường và chỉ chứa các chữ cái, số và dấu gạch ngang."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:537
msgid "Describes the Slug field on the Edit Tags screen."
msgstr "Mô tả trường đường dẫn cố định trên màn hình chỉnh sửa thẻ."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:536
msgid "Slug Field Description"
msgstr "Mô tả trường đường dẫn cố định"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:522
#: includes/admin/views/acf-taxonomy/advanced-settings.php:523
msgid "The name is how it appears on your site"
msgstr "Tên là cách nó xuất hiện trên trang web của bạn"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:521
msgid "Describes the Name field on the Edit Tags screen."
msgstr "Mô tả trường tên trên màn hình chỉnh sửa thẻ."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:520
msgid "Name Field Description"
msgstr "Mô tả trường Tên"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:507
msgid "No tags"
msgstr "Không có thẻ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:506
msgid ""
"Assigns the text displayed in the posts and media list tables when no tags "
"or categories are available."
msgstr ""
"Gán văn bản hiển thị trong bảng danh sách bài viết và phương tiện khi không "
"có thẻ hoặc danh mục nào có sẵn."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:505
msgid "No Terms"
msgstr "Không có mục phân loại"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:501
msgid "No %s"
msgstr "Không có %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:486
msgid "No tags found"
msgstr "Không tìm thấy thẻ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:485
msgid ""
"Assigns the text displayed when clicking the 'choose from most used' text in "
"the taxonomy meta box when no tags are available, and assigns the text used "
"in the terms list table when there are no items for a taxonomy."
msgstr ""
"Gán văn bản hiển thị khi nhấp vào văn bản 'chọn từ những thẻ được sử dụng "
"nhiều nhất' trong hộp meta phân loại khi không có thẻ nào có sẵn, và gán văn "
"bản được sử dụng trong bảng danh sách mục phân loại khi không có mục nào cho "
"một phân loại."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:484
msgid "Not Found"
msgstr "Không tìm thấy"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:463
msgid "Assigns text to the Title field of the Most Used tab."
msgstr "Gán văn bản cho trường Tiêu đề của tab Được sử dụng nhiều nhất."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:462
#: includes/admin/views/acf-taxonomy/advanced-settings.php:464
#: includes/admin/views/acf-taxonomy/advanced-settings.php:465
msgid "Most Used"
msgstr "Được sử dụng nhiều nhất"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:444
msgid "Choose from the most used tags"
msgstr "Chọn từ những thẻ được sử dụng nhiều nhất"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:443
msgid ""
"Assigns the 'choose from most used' text used in the meta box when "
"JavaScript is disabled. Only used on non-hierarchical taxonomies."
msgstr ""
"Gán văn bản 'chọn từ những thẻ được sử dụng nhiều nhất' được sử dụng trong "
"hộp meta khi JavaScript bị vô hiệu hóa. Chỉ được sử dụng trên các phân loại "
"không phân cấp."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:442
msgid "Choose From Most Used"
msgstr "Chọn từ những thẻ được sử dụng nhiều nhất"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:438
msgid "Choose from the most used %s"
msgstr "Chọn từ những %s được sử dụng nhiều nhất"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:418
msgid "Add or remove tags"
msgstr "Thêm hoặc xóa thẻ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:417
msgid ""
"Assigns the add or remove items text used in the meta box when JavaScript is "
"disabled. Only used on non-hierarchical taxonomies"
msgstr ""
"Gán văn bản thêm hoặc xóa mục được sử dụng trong hộp meta khi JavaScript bị "
"vô hiệu hóa. Chỉ được sử dụng trên các phân loại không phân cấp."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:416
msgid "Add Or Remove Items"
msgstr "Thêm hoặc xóa mục"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:412
msgid "Add or remove %s"
msgstr "Thêm hoặc xóa %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:392
msgid "Separate tags with commas"
msgstr "Tách các thẻ bằng dấu phẩy"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:391
msgid ""
"Assigns the separate item with commas text used in the taxonomy meta box. "
"Only used on non-hierarchical taxonomies."
msgstr ""
"Gán văn bản tách mục bằng dấu phẩy được sử dụng trong hộp meta phân loại. "
"Chỉ được sử dụng trên các phân loại không phân cấp."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:390
msgid "Separate Items With Commas"
msgstr "Tách các mục bằng dấu phẩy"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:386
msgid "Separate %s with commas"
msgstr "Tách %s bằng dấu phẩy"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:366
msgid "Popular Tags"
msgstr "Thẻ phổ biến"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:365
msgid "Assigns popular items text. Only used for non-hierarchical taxonomies."
msgstr ""
"Gán văn bản mục phổ biến. Chỉ được sử dụng cho các phân loại không phân cấp."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:364
msgid "Popular Items"
msgstr "Mục phổ biến"

#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:361
msgid "Popular %s"
msgstr "%s phổ biến"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:347
msgid "Search Tags"
msgstr "Tìm kiếm thẻ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:346
msgid "Assigns search items text."
msgstr "Gán văn bản tìm kiếm mục."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:323
msgid "Parent Category:"
msgstr "Danh mục cha:"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:322
msgid "Assigns parent item text, but with a colon (:) added to the end."
msgstr "Gán văn bản mục cha, nhưng với dấu hai chấm (:) được thêm vào cuối."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:321
msgid "Parent Item With Colon"
msgstr "Mục cha với dấu hai chấm"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:298
msgid "Parent Category"
msgstr "Danh mục cha"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:297
msgid "Assigns parent item text. Only used on hierarchical taxonomies."
msgstr "Gán văn bản mục cha. Chỉ được sử dụng trên các phân loại phân cấp."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:296
msgid "Parent Item"
msgstr "Mục cha"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:293
msgid "Parent %s"
msgstr "%s cha"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:278
msgid "New Tag Name"
msgstr "Tên thẻ mới"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:277
msgid "Assigns the new item name text."
msgstr "Gán văn bản tên mục mới."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:276
msgid "New Item Name"
msgstr "Tên mục mới"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:273
msgid "New %s Name"
msgstr "Tên mới %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:258
msgid "Add New Tag"
msgstr "Thêm thẻ mới"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:257
msgid "Assigns the add new item text."
msgstr "Gán văn bản thêm mục mới."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:238
msgid "Update Tag"
msgstr "Cập nhật thẻ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:237
msgid "Assigns the update item text."
msgstr "Gán văn bản cập nhật mục."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:236
msgid "Update Item"
msgstr "Cập nhật mục"

#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-taxonomy/advanced-settings.php:233
msgid "Update %s"
msgstr "Cập nhật %s"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:218
msgid "View Tag"
msgstr "Xem thẻ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:217
msgid "In the admin bar to view term during editing."
msgstr "Trong thanh quản trị để xem mục phân loại trong quá trình chỉnh sửa."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:198
msgid "Edit Tag"
msgstr "Chỉnh sửa thẻ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:197
msgid "At the top of the editor screen when editing a term."
msgstr "Ở đầu màn hình trình chỉnh sửa khi sửa một mục phân loại."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:178
msgid "All Tags"
msgstr "Tất cả thẻ"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:177
msgid "Assigns the all items text."
msgstr "Gán văn bản tất cả mục."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:158
msgid "Assigns the menu name text."
msgstr "Gán văn bản tên menu."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:157
msgid "Menu Label"
msgstr "Nhãn menu"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:131
msgid "Active taxonomies are enabled and registered with WordPress."
msgstr "Các phân loại đang hoạt động được kích hoạt và đăng ký với WordPress."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:115
msgid "A descriptive summary of the taxonomy."
msgstr "Một tóm tắt mô tả về phân loại."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:95
msgid "A descriptive summary of the term."
msgstr "Một tóm tắt mô tả về mục phân loại."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:94
msgid "Term Description"
msgstr "Mô tả mục phân loại"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:76
msgid "Single word, no spaces. Underscores and dashes allowed."
msgstr ""
"Một từ, không có khoảng trắng. Cho phép dấu gạch dưới và dấu gạch ngang."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:75
msgid "Term Slug"
msgstr "Đường dẫn cố định mục phân loại"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:56
msgid "The name of the default term."
msgstr "Tên của mục phân loại mặc định."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:55
msgid "Term Name"
msgstr "Tên mục phân loại"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:41
msgid ""
"Create a term for the taxonomy that cannot be deleted. It will not be "
"selected for posts by default."
msgstr ""
"Tạo một mục cho phân loại không thể bị xóa. Nó sẽ không được chọn cho bài "
"viết theo mặc định."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:40
msgid "Default Term"
msgstr "Mục phân loại mặc định"

#: includes/admin/views/acf-taxonomy/advanced-settings.php:28
msgid ""
"Whether terms in this taxonomy should be sorted in the order they are "
"provided to `wp_set_object_terms()`."
msgstr ""
"Có nên sắp xếp các mục phân loại trong phân loại này theo thứ tự chúng được "
"cung cấp cho `wp_set_object_terms()` hay không."

#: includes/admin/views/acf-taxonomy/advanced-settings.php:27
msgid "Sort Terms"
msgstr "Sắp xếp mục phân loại"

#: includes/admin/views/acf-post-type/list-empty.php:14
msgid "Add Post Type"
msgstr "Thêm loại nội dung"

#: includes/admin/views/acf-post-type/list-empty.php:13
msgid ""
"Expand the functionality of WordPress beyond standard posts and pages with "
"custom post types."
msgstr ""
"Mở rộng chức năng của WordPress vượt ra khỏi bài viết và trang tiêu chuẩn "
"với các loại nội dung tùy chỉnh."

#: includes/admin/views/acf-post-type/list-empty.php:12
msgid "Add Your First Post Type"
msgstr "Thêm loại nội dung đầu tiên của bạn"

#: includes/admin/views/acf-post-type/basic-settings.php:136
#: includes/admin/views/acf-taxonomy/basic-settings.php:135
msgid "I know what I'm doing, show me all the options."
msgstr "Tôi biết tôi đang làm gì, hãy cho tôi xem tất cả các tùy chọn."

#: includes/admin/views/acf-post-type/basic-settings.php:135
#: includes/admin/views/acf-taxonomy/basic-settings.php:134
msgid "Advanced Configuration"
msgstr "Cấu hình nâng cao"

#: includes/admin/views/acf-post-type/basic-settings.php:123
msgid "Hierarchical post types can have descendants (like pages)."
msgstr "Tùy chọn này giúp loại nội dung có thể tạo các mục con (như trang)."

#: includes/admin/views/acf-post-type/basic-settings.php:122
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1000
#: includes/admin/views/acf-taxonomy/basic-settings.php:121
msgid "Hierarchical"
msgstr "Phân cấp"

#: includes/admin/views/acf-post-type/basic-settings.php:107
msgid "Visible on the frontend and in the admin dashboard."
msgstr "Hiển thị trên giao diện người dùng và trên bảng điều khiển quản trị."

#: includes/admin/views/acf-post-type/basic-settings.php:106
#: includes/admin/views/acf-taxonomy/basic-settings.php:106
msgid "Public"
msgstr "Công khai"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:59
msgid "movie"
msgstr "phim"

#: includes/admin/views/acf-post-type/basic-settings.php:57
msgid "Lower case letters, underscores and dashes only, Max 20 characters."
msgstr ""
"Chỉ sử dụng chữ cái thường, dấu gạch dưới và dấu gạch ngang, tối đa 20 ký tự."

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:41
msgid "Movie"
msgstr "Phim"

#: includes/admin/views/acf-post-type/basic-settings.php:39
#: includes/admin/views/acf-taxonomy/basic-settings.php:40
msgid "Singular Label"
msgstr "Tên số ít"

#. translators: example post type
#: includes/admin/views/acf-post-type/basic-settings.php:24
msgid "Movies"
msgstr "Phim"

#: includes/admin/views/acf-post-type/basic-settings.php:22
#: includes/admin/views/acf-taxonomy/basic-settings.php:23
msgid "Plural Label"
msgstr "Tên số nhiều"

#: includes/admin/views/acf-post-type/advanced-settings.php:1313
msgid ""
"Optional custom controller to use instead of `WP_REST_Posts_Controller`."
msgstr ""
"Tùy chọn sử dụng bộ điều khiển tùy chỉnh thay vì `WP_REST_Posts_Controller`."

#: includes/admin/views/acf-post-type/advanced-settings.php:1312
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1230
msgid "Controller Class"
msgstr "Lớp điều khiển"

#: includes/admin/views/acf-post-type/advanced-settings.php:1294
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1211
msgid "The namespace part of the REST API URL."
msgstr "Phần không gian tên của URL REST API."

#: includes/admin/views/acf-post-type/advanced-settings.php:1293
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1210
msgid "Namespace Route"
msgstr "Namespace Route"

#: includes/admin/views/acf-post-type/advanced-settings.php:1275
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1192
msgid "The base URL for the post type REST API URLs."
msgstr "URL cơ sở cho các URL API REST của loại nội dung."

#: includes/admin/views/acf-post-type/advanced-settings.php:1274
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1191
msgid "Base URL"
msgstr "Base URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1260
msgid ""
"Exposes this post type in the REST API. Required to use the block editor."
msgstr ""
"Tiết lộ loại nội dung này trong API REST. Yêu cầu sử dụng trình soạn thảo "
"khối."

#: includes/admin/views/acf-post-type/advanced-settings.php:1259
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1174
msgid "Show In REST API"
msgstr "Hiển thị trong REST API"

#: includes/admin/views/acf-post-type/advanced-settings.php:1238
msgid "Customize the query variable name."
msgstr "Tùy chỉnh tên biến truy vấn."

#: includes/admin/views/acf-post-type/advanced-settings.php:1237
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1074
msgid "Query Variable"
msgstr "Biến truy Vấn"

#: includes/admin/views/acf-post-type/advanced-settings.php:1215
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1052
msgid "No Query Variable Support"
msgstr "Không hỗ trợ biến truy vấn"

#: includes/admin/views/acf-post-type/advanced-settings.php:1214
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1051
msgid "Custom Query Variable"
msgstr "Biến truy vấn tùy chỉnh"

#: includes/admin/views/acf-post-type/advanced-settings.php:1211
msgid ""
"Items can be accessed using the non-pretty permalink, eg. {post_type}"
"={post_slug}."
msgstr ""
"Các mục có thể được truy cập bằng cách sử dụng đường dẫn cố định không đẹp, "
"ví dụ: {post_type}={post_slug}."

#: includes/admin/views/acf-post-type/advanced-settings.php:1210
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1047
msgid "Query Variable Support"
msgstr "Hỗ trợ biến truy vấn"

#: includes/admin/views/acf-post-type/advanced-settings.php:1185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1023
msgid "URLs for an item and items can be accessed with a query string."
msgstr ""
"URL cho một mục và các mục có thể được truy cập bằng một chuỗi truy vấn."

#: includes/admin/views/acf-post-type/advanced-settings.php:1184
#: includes/admin/views/acf-taxonomy/advanced-settings.php:1022
msgid "Publicly Queryable"
msgstr "Có thể truy vấn công khai"

#: includes/admin/views/acf-post-type/advanced-settings.php:1163
msgid "Custom slug for the Archive URL."
msgstr "Đường dẫn cố định tùy chỉnh cho URL trang lưu trữ."

#: includes/admin/views/acf-post-type/advanced-settings.php:1162
msgid "Archive Slug"
msgstr "Slug trang lưu trữ"

#: includes/admin/views/acf-post-type/advanced-settings.php:1149
msgid ""
"Has an item archive that can be customized with an archive template file in "
"your theme."
msgstr ""
"Có một kho lưu trữ mục có thể được tùy chỉnh với một tệp mẫu lưu trữ trong "
"giao diện của bạn."

#: includes/admin/views/acf-post-type/advanced-settings.php:1148
msgid "Archive"
msgstr "Trang lưu trữ"

#: includes/admin/views/acf-post-type/advanced-settings.php:1128
msgid "Pagination support for the items URLs such as the archives."
msgstr "Hỗ trợ phân trang cho các URL mục như lưu trữ."

#: includes/admin/views/acf-post-type/advanced-settings.php:1127
msgid "Pagination"
msgstr "Phân trang"

#: includes/admin/views/acf-post-type/advanced-settings.php:1110
msgid "RSS feed URL for the post type items."
msgstr "URL nguồn cấp dữ liệu RSS cho các mục loại nội dung."

#: includes/admin/views/acf-post-type/advanced-settings.php:1109
msgid "Feed URL"
msgstr "URL nguồn cấp dữ liệu"

#: includes/admin/views/acf-post-type/advanced-settings.php:1091
#: includes/admin/views/acf-taxonomy/advanced-settings.php:981
msgid ""
"Alters the permalink structure to add the `WP_Rewrite::$front` prefix to "
"URLs."
msgstr ""
"Thay đổi cấu trúc liên kết cố định để thêm tiền tố `WP_Rewrite::$front` vào "
"URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1090
#: includes/admin/views/acf-taxonomy/advanced-settings.php:980
msgid "Front URL Prefix"
msgstr "Tiền tố URL phía trước"

#: includes/admin/views/acf-post-type/advanced-settings.php:1071
msgid "Customize the slug used in the URL."
msgstr "Tùy chỉnh đường dẫn cố định được sử dụng trong URL."

#: includes/admin/views/acf-post-type/advanced-settings.php:1070
#: includes/admin/views/acf-taxonomy/advanced-settings.php:960
msgid "URL Slug"
msgstr "Đường dẫn cố định URL"

#: includes/admin/views/acf-post-type/advanced-settings.php:1054
msgid "Permalinks for this post type are disabled."
msgstr "Liên kết cố định cho loại nội dung này đã bị vô hiệu hóa."

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1053
#: includes/admin/views/acf-taxonomy/advanced-settings.php:943
msgid ""
"Rewrite the URL using a custom slug defined in the input below. Your "
"permalink structure will be"
msgstr ""
"Thay đổi URL bằng cách sử dụng đường dẫn cố định tùy chỉnh được định nghĩa "
"trong đầu vào dưới đây. Cấu trúc đường dẫn cố định của bạn sẽ là"

#: includes/admin/views/acf-post-type/advanced-settings.php:1045
#: includes/admin/views/acf-taxonomy/advanced-settings.php:935
msgid "No Permalink (prevent URL rewriting)"
msgstr "Không có liên kết cố định (ngăn chặn việc viết lại URL)"

#: includes/admin/views/acf-post-type/advanced-settings.php:1044
#: includes/admin/views/acf-taxonomy/advanced-settings.php:934
msgid "Custom Permalink"
msgstr "Liên kết tĩnh tùy chỉnh"

#: includes/admin/views/acf-post-type/advanced-settings.php:1043
#: includes/admin/views/acf-post-type/advanced-settings.php:1213
#: includes/admin/views/acf-post-type/basic-settings.php:56
msgid "Post Type Key"
msgstr "Khóa loại nội dung"

#. translators: this string will be appended with the new permalink structure.
#: includes/admin/views/acf-post-type/advanced-settings.php:1041
#: includes/admin/views/acf-post-type/advanced-settings.php:1051
msgid ""
"Rewrite the URL using the post type key as the slug. Your permalink "
"structure will be"
msgstr ""
"Thay đổi URL bằng cách sử dụng khóa loại nội dung làm đường dẫn cố định. Cấu "
"trúc liên kết cố định của bạn sẽ là"

#: includes/admin/views/acf-post-type/advanced-settings.php:1039
#: includes/admin/views/acf-taxonomy/advanced-settings.php:930
msgid "Permalink Rewrite"
msgstr "Thay đổi liên kết cố định"

#: includes/admin/views/acf-post-type/advanced-settings.php:1025
msgid "Delete items by a user when that user is deleted."
msgstr "Xóa các mục của một người dùng khi người dùng đó bị xóa."

#: includes/admin/views/acf-post-type/advanced-settings.php:1024
msgid "Delete With User"
msgstr "Xóa với người dùng"

#: includes/admin/views/acf-post-type/advanced-settings.php:1010
msgid "Allow the post type to be exported from 'Tools' > 'Export'."
msgstr "Cho phép loại nội dung được xuất từ 'Công cụ' > 'Xuất'."

#: includes/admin/views/acf-post-type/advanced-settings.php:1009
msgid "Can Export"
msgstr "Có thể xuất"

#: includes/admin/views/acf-post-type/advanced-settings.php:978
msgid "Optionally provide a plural to be used in capabilities."
msgstr "Tùy chọn cung cấp một số nhiều để sử dụng trong khả năng."

#: includes/admin/views/acf-post-type/advanced-settings.php:977
msgid "Plural Capability Name"
msgstr "Tên khả năng số nhiều"

#: includes/admin/views/acf-post-type/advanced-settings.php:959
msgid "Choose another post type to base the capabilities for this post type."
msgstr ""
"Chọn một loại nội dung khác để cơ sở các khả năng cho loại nội dung này."

#: includes/admin/views/acf-post-type/advanced-settings.php:958
msgid "Singular Capability Name"
msgstr "Tên khả năng số ít"

#: includes/admin/views/acf-post-type/advanced-settings.php:944
msgid ""
"By default the capabilities of the post type will inherit the 'Post' "
"capability names, eg. edit_post, delete_posts. Enable to use post type "
"specific capabilities, eg. edit_{singular}, delete_{plural}."
msgstr ""
"Theo mặc định, các khả năng của loại nội dung sẽ kế thừa tên khả năng 'Bài "
"viết', ví dụ: edit_post, delete_posts. Kích hoạt để sử dụng khả năng cụ thể "
"của loại nội dung, ví dụ: edit_{singular}, delete_{plural}."

#: includes/admin/views/acf-post-type/advanced-settings.php:943
msgid "Rename Capabilities"
msgstr "Đổi tên quyền người dùng"

#: includes/admin/views/acf-post-type/advanced-settings.php:928
msgid "Exclude From Search"
msgstr "Loại trừ khỏi tìm kiếm"

#: includes/admin/views/acf-post-type/advanced-settings.php:915
#: includes/admin/views/acf-taxonomy/advanced-settings.php:874
msgid ""
"Allow items to be added to menus in the 'Appearance' > 'Menus' screen. Must "
"be turned on in 'Screen options'."
msgstr ""
"Cho phép các mục được thêm vào menu trong màn hình 'Giao diện' > 'Menu'. "
"Phải được bật trong 'Tùy chọn màn hình'."

#: includes/admin/views/acf-post-type/advanced-settings.php:914
#: includes/admin/views/acf-taxonomy/advanced-settings.php:873
msgid "Appearance Menus Support"
msgstr "Hỗ trợ menu giao diện"

#: includes/admin/views/acf-post-type/advanced-settings.php:896
msgid "Appears as an item in the 'New' menu in the admin bar."
msgstr "Xuất hiện như một mục trong menu 'Mới' trên thanh quản trị."

#: includes/admin/views/acf-post-type/advanced-settings.php:895
msgid "Show In Admin Bar"
msgstr "Hiển thị trong thanh quản trị"

#: includes/admin/views/acf-post-type/advanced-settings.php:861
msgid "Custom Meta Box Callback"
msgstr "Hàm gọi lại hộp meta tùy chỉnh"

#: includes/admin/views/acf-post-type/advanced-settings.php:822
#: includes/fields/class-acf-field-icon_picker.php:636
msgid "Menu Icon"
msgstr "Biểu tượng menu"

#: includes/admin/views/acf-post-type/advanced-settings.php:778
msgid "The position in the sidebar menu in the admin dashboard."
msgstr "Vị trí trong menu thanh bên trên bảng điều khiển quản trị."

#: includes/admin/views/acf-post-type/advanced-settings.php:777
msgid "Menu Position"
msgstr "Vị trí menu"

#: includes/admin/views/acf-post-type/advanced-settings.php:759
msgid ""
"By default the post type will get a new top level item in the admin menu. If "
"an existing top level item is supplied here, the post type will be added as "
"a submenu item under it."
msgstr ""
"Theo mặc định, loại nội dung sẽ nhận được một mục cấp cao mới trong menu "
"quản trị. Nếu một mục cấp cao hiện có được cung cấp ở đây, loại bài viết sẽ "
"được thêm dưới dạng mục con dưới nó."

#: includes/admin/views/acf-post-type/advanced-settings.php:758
msgid "Admin Menu Parent"
msgstr "Menu quản trị cấp trên"

#: includes/admin/views/acf-post-type/advanced-settings.php:739
#: includes/admin/views/acf-taxonomy/advanced-settings.php:734
msgid "Admin editor navigation in the sidebar menu."
msgstr "Điều hướng trình chỉnh sửa của quản trị trong menu thanh bên."

#: includes/admin/views/acf-post-type/advanced-settings.php:738
#: includes/admin/views/acf-taxonomy/advanced-settings.php:733
msgid "Show In Admin Menu"
msgstr "Hiển thị trong menu quản trị"

#: includes/admin/views/acf-post-type/advanced-settings.php:725
#: includes/admin/views/acf-taxonomy/advanced-settings.php:719
msgid "Items can be edited and managed in the admin dashboard."
msgstr ""
"Các mục có thể được chỉnh sửa và quản lý trong bảng điều khiển quản trị."

#: includes/admin/views/acf-post-type/advanced-settings.php:724
#: includes/admin/views/acf-taxonomy/advanced-settings.php:718
msgid "Show In UI"
msgstr "Hiển thị trong giao diện người dùng"

#: includes/admin/views/acf-post-type/advanced-settings.php:694
msgid "A link to a post."
msgstr "Một liên kết đến một bài viết."

#: includes/admin/views/acf-post-type/advanced-settings.php:693
msgid "Description for a navigation link block variation."
msgstr "Mô tả cho biến thể khối liên kết điều hướng."

#: includes/admin/views/acf-post-type/advanced-settings.php:692
#: includes/admin/views/acf-taxonomy/advanced-settings.php:702
msgid "Item Link Description"
msgstr "Mô tả liên kết mục"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:688
msgid "A link to a %s."
msgstr "Một liên kết đến %s."

#: includes/admin/views/acf-post-type/advanced-settings.php:673
msgid "Post Link"
msgstr "Liên kết bài viết"

#: includes/admin/views/acf-post-type/advanced-settings.php:672
msgid "Title for a navigation link block variation."
msgstr "Tiêu đề cho biến thể khối liên kết điều hướng."

#: includes/admin/views/acf-post-type/advanced-settings.php:671
#: includes/admin/views/acf-taxonomy/advanced-settings.php:681
msgid "Item Link"
msgstr "Liên kết mục"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:668
#: includes/admin/views/acf-taxonomy/advanced-settings.php:678
msgid "%s Link"
msgstr "Liên kết %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:653
msgid "Post updated."
msgstr "Bài viết đã được cập nhật."

#: includes/admin/views/acf-post-type/advanced-settings.php:652
msgid "In the editor notice after an item is updated."
msgstr "Trong thông báo trình soạn thảo sau khi một mục được cập nhật."

#: includes/admin/views/acf-post-type/advanced-settings.php:651
msgid "Item Updated"
msgstr "Mục đã được cập nhật"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:648
msgid "%s updated."
msgstr "%s đã được cập nhật."

#: includes/admin/views/acf-post-type/advanced-settings.php:633
msgid "Post scheduled."
msgstr "Bài viết đã được lên lịch."

#: includes/admin/views/acf-post-type/advanced-settings.php:632
msgid "In the editor notice after scheduling an item."
msgstr "Trong thông báo trình soạn thảo sau khi lên lịch một mục."

#: includes/admin/views/acf-post-type/advanced-settings.php:631
msgid "Item Scheduled"
msgstr "Mục đã được lên lịch"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:628
msgid "%s scheduled."
msgstr "%s đã được lên lịch."

#: includes/admin/views/acf-post-type/advanced-settings.php:613
msgid "Post reverted to draft."
msgstr "Bài viết đã được chuyển về nháp."

#: includes/admin/views/acf-post-type/advanced-settings.php:612
msgid "In the editor notice after reverting an item to draft."
msgstr "Trong thông báo trình soạn thảo sau khi chuyển một mục về nháp."

#: includes/admin/views/acf-post-type/advanced-settings.php:611
msgid "Item Reverted To Draft"
msgstr "Mục đã được chuyển về nháp"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:608
msgid "%s reverted to draft."
msgstr "%s đã được chuyển về nháp."

#: includes/admin/views/acf-post-type/advanced-settings.php:593
msgid "Post published privately."
msgstr "Bài viết đã được xuất bản riêng tư."

#: includes/admin/views/acf-post-type/advanced-settings.php:592
msgid "In the editor notice after publishing a private item."
msgstr "Trong thông báo trình soạn thảo sau khi xuất bản một mục riêng tư."

#: includes/admin/views/acf-post-type/advanced-settings.php:591
msgid "Item Published Privately"
msgstr "Mục đã được xuất bản riêng tư"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:588
msgid "%s published privately."
msgstr "%s đã được xuất bản riêng tư."

#: includes/admin/views/acf-post-type/advanced-settings.php:573
msgid "Post published."
msgstr "Bài viết đã được xuất bản."

#: includes/admin/views/acf-post-type/advanced-settings.php:572
msgid "In the editor notice after publishing an item."
msgstr "Trong thông báo trình soạn thảo sau khi xuất bản một mục."

#: includes/admin/views/acf-post-type/advanced-settings.php:571
msgid "Item Published"
msgstr "Mục đã được xuất bản"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:568
msgid "%s published."
msgstr "%s đã được xuất bản."

#: includes/admin/views/acf-post-type/advanced-settings.php:553
msgid "Posts list"
msgstr "Danh sách bài viết"

#: includes/admin/views/acf-post-type/advanced-settings.php:552
msgid "Used by screen readers for the items list on the post type list screen."
msgstr ""
"Được sử dụng bởi máy đọc màn hình cho danh sách mục trên màn hình danh sách "
"loại nội dung."

#: includes/admin/views/acf-post-type/advanced-settings.php:551
#: includes/admin/views/acf-taxonomy/advanced-settings.php:640
msgid "Items List"
msgstr "Danh sách mục"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:548
#: includes/admin/views/acf-taxonomy/advanced-settings.php:637
msgid "%s list"
msgstr "Danh sách %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:533
msgid "Posts list navigation"
msgstr "Điều hướng danh sách bài viết"

#: includes/admin/views/acf-post-type/advanced-settings.php:532
msgid ""
"Used by screen readers for the filter list pagination on the post type list "
"screen."
msgstr ""
"Được sử dụng bởi máy đọc màn hình cho phân trang danh sách bộ lọc trên màn "
"hình danh sách loại nội dung."

#: includes/admin/views/acf-post-type/advanced-settings.php:531
#: includes/admin/views/acf-taxonomy/advanced-settings.php:620
msgid "Items List Navigation"
msgstr "Điều hướng danh sách mục"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:528
#: includes/admin/views/acf-taxonomy/advanced-settings.php:617
msgid "%s list navigation"
msgstr "Điều hướng danh sách %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:512
msgid "Filter posts by date"
msgstr "Lọc bài viết theo ngày"

#: includes/admin/views/acf-post-type/advanced-settings.php:511
msgid ""
"Used by screen readers for the filter by date heading on the post type list "
"screen."
msgstr ""
"Được sử dụng bởi máy đọc màn hình cho tiêu đề lọc theo ngày trên màn hình "
"danh sách loại nội dung."

#: includes/admin/views/acf-post-type/advanced-settings.php:510
msgid "Filter Items By Date"
msgstr "Lọc mục theo ngày"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:506
msgid "Filter %s by date"
msgstr "Lọc %s theo ngày"

#: includes/admin/views/acf-post-type/advanced-settings.php:491
msgid "Filter posts list"
msgstr "Lọc danh sách bài viết"

#: includes/admin/views/acf-post-type/advanced-settings.php:490
msgid ""
"Used by screen readers for the filter links heading on the post type list "
"screen."
msgstr ""
"Được sử dụng bởi máy đọc màn hình cho tiêu đề liên kết bộ lọc trên màn hình "
"danh sách loại nội dung."

#: includes/admin/views/acf-post-type/advanced-settings.php:489
msgid "Filter Items List"
msgstr "Lọc danh sách mục"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:485
msgid "Filter %s list"
msgstr "Lọc danh sách %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:469
msgid "In the media modal showing all media uploaded to this item."
msgstr ""
"Trong modal phương tiện hiển thị tất cả phương tiện đã tải lên cho mục này."

#: includes/admin/views/acf-post-type/advanced-settings.php:468
msgid "Uploaded To This Item"
msgstr "Đã tải lên mục này"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:464
msgid "Uploaded to this %s"
msgstr "Đã tải lên %s này"

#: includes/admin/views/acf-post-type/advanced-settings.php:449
msgid "Insert into post"
msgstr "Chèn vào bài viết"

#: includes/admin/views/acf-post-type/advanced-settings.php:448
msgid "As the button label when adding media to content."
msgstr "Như nhãn nút khi thêm phương tiện vào nội dung."

#: includes/admin/views/acf-post-type/advanced-settings.php:447
msgid "Insert Into Media Button"
msgstr "Chèn vào nút Media"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:443
msgid "Insert into %s"
msgstr "Chèn vào %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:428
msgid "Use as featured image"
msgstr "Sử dụng làm hình ảnh nổi bật"

#: includes/admin/views/acf-post-type/advanced-settings.php:427
msgid ""
"As the button label for selecting to use an image as the featured image."
msgstr "Như nhãn nút để chọn sử dụng hình ảnh làm hình ảnh nổi bật."

#: includes/admin/views/acf-post-type/advanced-settings.php:426
msgid "Use Featured Image"
msgstr "Sử dụng hình ảnh nổi bật"

#: includes/admin/views/acf-post-type/advanced-settings.php:413
msgid "Remove featured image"
msgstr "Xóa hình ảnh nổi bật"

#: includes/admin/views/acf-post-type/advanced-settings.php:412
msgid "As the button label when removing the featured image."
msgstr "Như nhãn nút khi xóa hình ảnh nổi bật."

#: includes/admin/views/acf-post-type/advanced-settings.php:411
msgid "Remove Featured Image"
msgstr "Xóa hình ảnh nổi bật"

#: includes/admin/views/acf-post-type/advanced-settings.php:398
msgid "Set featured image"
msgstr "Đặt hình ảnh nổi bật"

#: includes/admin/views/acf-post-type/advanced-settings.php:397
msgid "As the button label when setting the featured image."
msgstr "Như nhãn nút khi đặt hình ảnh nổi bật."

#: includes/admin/views/acf-post-type/advanced-settings.php:396
msgid "Set Featured Image"
msgstr "Đặt hình ảnh nổi bật"

#: includes/admin/views/acf-post-type/advanced-settings.php:383
msgid "Featured image"
msgstr "Hình ảnh nổi bật"

#: includes/admin/views/acf-post-type/advanced-settings.php:382
msgid "In the editor used for the title of the featured image meta box."
msgstr ""
"Trong trình soạn thảo được sử dụng cho tiêu đề của hộp meta hình ảnh nổi bật."

#: includes/admin/views/acf-post-type/advanced-settings.php:381
msgid "Featured Image Meta Box"
msgstr "Hộp meta hình ảnh nổi bật"

#: includes/admin/views/acf-post-type/advanced-settings.php:368
msgid "Post Attributes"
msgstr "Thuộc tính bài viết"

#: includes/admin/views/acf-post-type/advanced-settings.php:367
msgid "In the editor used for the title of the post attributes meta box."
msgstr ""
"Trong trình soạn thảo được sử dụng cho tiêu đề của hộp meta thuộc tính bài "
"viết."

#: includes/admin/views/acf-post-type/advanced-settings.php:366
msgid "Attributes Meta Box"
msgstr "Hộp meta thuộc tính"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:363
msgid "%s Attributes"
msgstr "Thuộc tính %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:348
msgid "Post Archives"
msgstr "Lưu trữ bài viết"

#: includes/admin/views/acf-post-type/advanced-settings.php:347
msgid ""
"Adds 'Post Type Archive' items with this label to the list of posts shown "
"when adding items to an existing menu in a CPT with archives enabled. Only "
"appears when editing menus in 'Live Preview' mode and a custom archive slug "
"has been provided."
msgstr ""
"Thêm các mục 'Lưu trữ loại nội dung' với nhãn này vào danh sách bài viết "
"hiển thị khi thêm mục vào menu hiện tại trong CPT có kích hoạt lưu trữ. Chỉ "
"xuất hiện khi chỉnh sửa menu trong chế độ 'Xem trước trực tiếp' và đã cung "
"cấp đường dẫn cố định lưu trữ tùy chỉnh."

#: includes/admin/views/acf-post-type/advanced-settings.php:346
msgid "Archives Nav Menu"
msgstr "Menu điều hướng trang lưu trữ"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:343
msgid "%s Archives"
msgstr "%s Trang lưu trữ"

#: includes/admin/views/acf-post-type/advanced-settings.php:328
msgid "No posts found in Trash"
msgstr "Không tìm thấy bài viết nào trong thùng rác"

#: includes/admin/views/acf-post-type/advanced-settings.php:327
msgid ""
"At the top of the post type list screen when there are no posts in the trash."
msgstr ""
"Ở đầu màn hình danh sách loại nội dung khi không có bài viết nào trong thùng "
"rác."

#: includes/admin/views/acf-post-type/advanced-settings.php:326
msgid "No Items Found in Trash"
msgstr "Không tìm thấy mục nào trong thùng rác"

#. translators: %s Plural form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:322
msgid "No %s found in Trash"
msgstr "Không tìm thấy %s trong thùng rác"

#: includes/admin/views/acf-post-type/advanced-settings.php:307
msgid "No posts found"
msgstr "Không tìm thấy bài viết nào"

#: includes/admin/views/acf-post-type/advanced-settings.php:306
msgid ""
"At the top of the post type list screen when there are no posts to display."
msgstr ""
"Ở đầu màn hình danh sách loại nội dung khi không có bài viết nào để hiển thị."

#: includes/admin/views/acf-post-type/advanced-settings.php:305
msgid "No Items Found"
msgstr "Không tìm thấy mục nào"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:301
#: includes/admin/views/acf-taxonomy/advanced-settings.php:480
msgid "No %s found"
msgstr "Không tìm thấy %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:286
msgid "Search Posts"
msgstr "Tìm kiếm bài viết"

#: includes/admin/views/acf-post-type/advanced-settings.php:285
msgid "At the top of the items screen when searching for an item."
msgstr "Ở đầu màn hình mục khi tìm kiếm một mục."

#: includes/admin/views/acf-post-type/advanced-settings.php:284
#: includes/admin/views/acf-taxonomy/advanced-settings.php:345
msgid "Search Items"
msgstr "Tìm kiếm mục"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:281
#: includes/admin/views/acf-taxonomy/advanced-settings.php:342
msgid "Search %s"
msgstr "Tìm kiếm %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:266
msgid "Parent Page:"
msgstr "Trang cha:"

#: includes/admin/views/acf-post-type/advanced-settings.php:265
msgid "For hierarchical types in the post type list screen."
msgstr "Đối với các loại phân cấp trong màn hình danh sách loại nội dung."

#: includes/admin/views/acf-post-type/advanced-settings.php:264
msgid "Parent Item Prefix"
msgstr "Tiền tố mục cha"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:261
#: includes/admin/views/acf-taxonomy/advanced-settings.php:318
msgid "Parent %s:"
msgstr "Cha %s:"

#: includes/admin/views/acf-post-type/advanced-settings.php:246
msgid "New Post"
msgstr "Bài viết mới"

#: includes/admin/views/acf-post-type/advanced-settings.php:244
msgid "New Item"
msgstr "Mục mới"

#. translators: %s Singular form of post type name
#: includes/admin/views/acf-post-type/advanced-settings.php:241
msgid "New %s"
msgstr "%s mới"

#: includes/admin/views/acf-post-type/advanced-settings.php:206
#: includes/admin/views/acf-post-type/advanced-settings.php:226
msgid "Add New Post"
msgstr "Thêm bài viết mới"

#: includes/admin/views/acf-post-type/advanced-settings.php:205
msgid "At the top of the editor screen when adding a new item."
msgstr "Ở đầu màn hình trình chỉnh sửa khi thêm một mục mới."

#: includes/admin/views/acf-post-type/advanced-settings.php:204
#: includes/admin/views/acf-taxonomy/advanced-settings.php:256
msgid "Add New Item"
msgstr "Thêm mục mới"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:201
#: includes/admin/views/acf-post-type/advanced-settings.php:221
#: includes/admin/views/acf-taxonomy/advanced-settings.php:253
msgid "Add New %s"
msgstr "Thêm %s mới"

#: includes/admin/views/acf-post-type/advanced-settings.php:186
msgid "View Posts"
msgstr "Xem bài viết"

#: includes/admin/views/acf-post-type/advanced-settings.php:185
msgid ""
"Appears in the admin bar in the 'All Posts' view, provided the post type "
"supports archives and the home page is not an archive of that post type."
msgstr ""
"Xuất hiện trong thanh quản trị trong chế độ xem 'Tất cả bài viết', miễn là "
"loại nội dung hỗ trợ lưu trữ và trang chủ không phải là lưu trữ của loại nội "
"dung đó."

#: includes/admin/views/acf-post-type/advanced-settings.php:184
msgid "View Items"
msgstr "Xem mục"

#: includes/admin/views/acf-post-type/advanced-settings.php:166
msgid "View Post"
msgstr "Xem bài viết"

#: includes/admin/views/acf-post-type/advanced-settings.php:165
msgid "In the admin bar to view item when editing it."
msgstr "Trong thanh quản trị để xem mục khi đang chỉnh sửa nó."

#: includes/admin/views/acf-post-type/advanced-settings.php:164
#: includes/admin/views/acf-taxonomy/advanced-settings.php:216
msgid "View Item"
msgstr "Xem mục"

#. translators: %s Singular form of post type name
#. translators: %s Plural form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:161
#: includes/admin/views/acf-post-type/advanced-settings.php:181
#: includes/admin/views/acf-taxonomy/advanced-settings.php:213
msgid "View %s"
msgstr "Xem %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:146
msgid "Edit Post"
msgstr "Chỉnh sửa bài viết"

#: includes/admin/views/acf-post-type/advanced-settings.php:145
msgid "At the top of the editor screen when editing an item."
msgstr "Ở đầu màn hình trình chỉnh sửa khi sửa một mục."

#: includes/admin/views/acf-post-type/advanced-settings.php:144
#: includes/admin/views/acf-taxonomy/advanced-settings.php:196
msgid "Edit Item"
msgstr "Chỉnh sửa mục"

#. translators: %s Singular form of post type name
#. translators: %s Singular form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:141
#: includes/admin/views/acf-taxonomy/advanced-settings.php:193
msgid "Edit %s"
msgstr "Chỉnh sửa %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:126
msgid "All Posts"
msgstr "Tất cả bài viết"

#: includes/admin/views/acf-post-type/advanced-settings.php:125
#: includes/admin/views/acf-post-type/advanced-settings.php:225
#: includes/admin/views/acf-post-type/advanced-settings.php:245
msgid "In the post type submenu in the admin dashboard."
msgstr "Trong submenu loại nội dung trong bảng điều khiển quản trị."

#: includes/admin/views/acf-post-type/advanced-settings.php:124
#: includes/admin/views/acf-taxonomy/advanced-settings.php:176
msgid "All Items"
msgstr "Tất cả mục"

#. translators: %s Plural form of post type name
#. translators: %s Plural form of taxonomy name
#: includes/admin/views/acf-post-type/advanced-settings.php:121
#: includes/admin/views/acf-taxonomy/advanced-settings.php:173
msgid "All %s"
msgstr "Tất cả %s"

#: includes/admin/views/acf-post-type/advanced-settings.php:105
msgid "Admin menu name for the post type."
msgstr "Tên menu quản trị cho loại nội dung."

#: includes/admin/views/acf-post-type/advanced-settings.php:104
msgid "Menu Name"
msgstr "Tên menu"

#: includes/admin/views/acf-post-type/advanced-settings.php:90
#: includes/admin/views/acf-taxonomy/advanced-settings.php:142
msgid "Regenerate all labels using the Singular and Plural labels"
msgstr "Tạo lại tất cả các tên bằng cách sử dụng tên số ít và tên số nhiều"

#: includes/admin/views/acf-post-type/advanced-settings.php:88
#: includes/admin/views/acf-taxonomy/advanced-settings.php:140
msgid "Regenerate"
msgstr "Tạo lại"

#: includes/admin/views/acf-post-type/advanced-settings.php:79
msgid "Active post types are enabled and registered with WordPress."
msgstr ""
"Các loại nội dung đang hoạt động đã được kích hoạt và đăng ký với WordPress."

#: includes/admin/views/acf-post-type/advanced-settings.php:63
msgid "A descriptive summary of the post type."
msgstr "Một tóm tắt mô tả về loại nội dung."

#: includes/admin/views/acf-post-type/advanced-settings.php:48
msgid "Add Custom"
msgstr "Thêm tùy chỉnh"

#: includes/admin/views/acf-post-type/advanced-settings.php:42
msgid "Enable various features in the content editor."
msgstr "Kích hoạt các tính năng khác nhau trong trình chỉnh sửa nội dung."

#: includes/admin/views/acf-post-type/advanced-settings.php:31
msgid "Post Formats"
msgstr "Định dạng bài viết"

#: includes/admin/views/acf-post-type/advanced-settings.php:25
msgid "Editor"
msgstr "Trình chỉnh sửa"

#: includes/admin/views/acf-post-type/advanced-settings.php:24
msgid "Trackbacks"
msgstr "Theo dõi liên kết"

#: includes/admin/views/acf-post-type/basic-settings.php:87
msgid "Select existing taxonomies to classify items of the post type."
msgstr "Chọn các phân loại hiện có để phân loại các mục của loại nội dung."

#: includes/admin/views/acf-field-group/field.php:158
msgid "Browse Fields"
msgstr "Duyệt các trường"

#: includes/admin/tools/class-acf-admin-tool-import.php:287
msgid "Nothing to import"
msgstr "Không có gì để nhập"

#: includes/admin/tools/class-acf-admin-tool-import.php:282
msgid ". The Custom Post Type UI plugin can be deactivated."
msgstr ""
". Plugin Giao diện người dùng loại nội dung tùy chỉnh có thể được hủy kích "
"hoạt."

#. translators: %d - number of items imported from CPTUI
#: includes/admin/tools/class-acf-admin-tool-import.php:273
msgid "Imported %d item from Custom Post Type UI -"
msgid_plural "Imported %d items from Custom Post Type UI -"
msgstr[0] "Đã nhập %d mục từ giao diện người dùng loại nội dung tùy chỉnh -"

#: includes/admin/tools/class-acf-admin-tool-import.php:257
msgid "Failed to import taxonomies."
msgstr "Không thể nhập phân loại."

#: includes/admin/tools/class-acf-admin-tool-import.php:239
msgid "Failed to import post types."
msgstr "Không thể nhập loại nội dung."

#: includes/admin/tools/class-acf-admin-tool-import.php:228
msgid "Nothing from Custom Post Type UI plugin selected for import."
msgstr ""
"Không có gì từ plugin Giao diện người dùng loại nội dung tùy chỉnh được chọn "
"để nhập."

#: includes/admin/tools/class-acf-admin-tool-import.php:204
msgid "Imported 1 item"
msgid_plural "Imported %s items"
msgstr[0] "Đã nhập %s mục"

#: includes/admin/tools/class-acf-admin-tool-import.php:119
msgid ""
"Importing a Post Type or Taxonomy with the same key as one that already "
"exists will overwrite the settings for the existing Post Type or Taxonomy "
"with those of the import."
msgstr ""
"Nhập một loại nội dung hoặc Phân loại với khóa giống như một cái đã tồn tại "
"sẽ ghi đè các cài đặt cho loại nội dung hoặc Phân loại hiện tại với những "
"cái của nhập khẩu."

#: includes/admin/tools/class-acf-admin-tool-import.php:108
#: includes/admin/tools/class-acf-admin-tool-import.php:124
msgid "Import from Custom Post Type UI"
msgstr "Nhập từ Giao diện người dùng loại nội dung Tùy chỉnh"

#: includes/admin/tools/class-acf-admin-tool-export.php:354
msgid ""
"The following code can be used to register a local version of the selected "
"items. Storing field groups, post types, or taxonomies locally can provide "
"many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's "
"functions.php file or include it within an external file, then deactivate or "
"delete the items from the ACF admin."
msgstr ""
"Mã sau đây có thể được sử dụng để đăng ký một phiên bản địa phương của các "
"mục đã chọn. Lưu trữ nhóm trường, loại nội dung hoặc phân loại một cách địa "
"phương có thể mang lại nhiều lợi ích như thời gian tải nhanh hơn, kiểm soát "
"phiên bản và trường/cài đặt động. Chỉ cần sao chép và dán mã sau vào tệp "
"functions.php giao diện của bạn hoặc bao gồm nó trong một tệp bên ngoài, sau "
"đó hủy kích hoạt hoặc xóa các mục từ quản trị ACF."

#: includes/admin/tools/class-acf-admin-tool-export.php:353
msgid "Export - Generate PHP"
msgstr "Xuất - Tạo PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:330
msgid "Export"
msgstr "Xuất"

#: includes/admin/tools/class-acf-admin-tool-export.php:264
msgid "Select Taxonomies"
msgstr "Chọn Phân loại"

#: includes/admin/tools/class-acf-admin-tool-export.php:239
msgid "Select Post Types"
msgstr "Chọn loại nội dung"

#: includes/admin/tools/class-acf-admin-tool-export.php:155
msgid "Exported 1 item."
msgid_plural "Exported %s items."
msgstr[0] "Đã xuất %s mục."

#: includes/admin/post-types/admin-taxonomy.php:127
msgid "Category"
msgstr "Danh mục"

#: includes/admin/post-types/admin-taxonomy.php:125
msgid "Tag"
msgstr "Thẻ"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:82
msgid "%s taxonomy created"
msgstr "%s đã tạo phân loại"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:76
msgid "%s taxonomy updated"
msgstr "%s đã cập nhật phân loại"

#: includes/admin/post-types/admin-taxonomy.php:56
msgid "Taxonomy draft updated."
msgstr "Bản nháp phân loại đã được cập nhật."

#: includes/admin/post-types/admin-taxonomy.php:55
msgid "Taxonomy scheduled for."
msgstr "Phân loại được lên lịch cho."

#: includes/admin/post-types/admin-taxonomy.php:54
msgid "Taxonomy submitted."
msgstr "Phân loại đã được gửi."

#: includes/admin/post-types/admin-taxonomy.php:53
msgid "Taxonomy saved."
msgstr "Phân loại đã được lưu."

#: includes/admin/post-types/admin-taxonomy.php:49
msgid "Taxonomy deleted."
msgstr "Phân loại đã được xóa."

#: includes/admin/post-types/admin-taxonomy.php:48
msgid "Taxonomy updated."
msgstr "Phân loại đã được cập nhật."

#: includes/admin/post-types/admin-taxonomies.php:351
#: includes/admin/post-types/admin-taxonomy.php:153
msgid ""
"This taxonomy could not be registered because its key is in use by another "
"taxonomy registered by another plugin or theme."
msgstr ""
"Phân loại này không thể được đăng ký vì khóa của nó đang được sử dụng bởi "
"một phân loại khác được đăng ký bởi một plugin hoặc giao diện khác."

#. translators: %s number of taxonomies synchronized
#: includes/admin/post-types/admin-taxonomies.php:333
msgid "Taxonomy synchronized."
msgid_plural "%s taxonomies synchronized."
msgstr[0] "Đã đồng bộ hóa %s phân loại."

#. translators: %s number of taxonomies duplicated
#: includes/admin/post-types/admin-taxonomies.php:326
msgid "Taxonomy duplicated."
msgid_plural "%s taxonomies duplicated."
msgstr[0] "Đã nhân đôi %s phân loại."

#. translators: %s number of taxonomies deactivated
#: includes/admin/post-types/admin-taxonomies.php:319
msgid "Taxonomy deactivated."
msgid_plural "%s taxonomies deactivated."
msgstr[0] "Đã hủy kích hoạt %s phân loại."

#. translators: %s number of taxonomies activated
#: includes/admin/post-types/admin-taxonomies.php:312
msgid "Taxonomy activated."
msgid_plural "%s taxonomies activated."
msgstr[0] "Đã kích hoạt %s phân loại."

#: includes/admin/post-types/admin-taxonomies.php:113
msgid "Terms"
msgstr "Mục phân loại"

#. translators: %s number of post types synchronized
#: includes/admin/post-types/admin-post-types.php:327
msgid "Post type synchronized."
msgid_plural "%s post types synchronized."
msgstr[0] "Đã đồng bộ hóa %s loại nội dung."

#. translators: %s number of post types duplicated
#: includes/admin/post-types/admin-post-types.php:320
msgid "Post type duplicated."
msgid_plural "%s post types duplicated."
msgstr[0] "Đã nhân đôi %s loại nội dung."

#. translators: %s number of post types deactivated
#: includes/admin/post-types/admin-post-types.php:313
msgid "Post type deactivated."
msgid_plural "%s post types deactivated."
msgstr[0] "Đã hủy kích hoạt %s loại nội dung."

#. translators: %s number of post types activated
#: includes/admin/post-types/admin-post-types.php:306
msgid "Post type activated."
msgid_plural "%s post types activated."
msgstr[0] "Đã kích hoạt %s loại nội dung."

#: includes/admin/post-types/admin-post-types.php:87
#: includes/admin/post-types/admin-taxonomies.php:111
#: includes/admin/tools/class-acf-admin-tool-import.php:79
#: includes/admin/views/acf-taxonomy/basic-settings.php:82
#: includes/post-types/class-acf-post-type.php:91
msgid "Post Types"
msgstr "Loại nội dung"

#: includes/admin/post-types/admin-post-type.php:158
#: includes/admin/post-types/admin-taxonomy.php:160
msgid "Advanced Settings"
msgstr "Cài đặt nâng cao"

#: includes/admin/post-types/admin-post-type.php:157
#: includes/admin/post-types/admin-taxonomy.php:159
msgid "Basic Settings"
msgstr "Cài đặt cơ bản"

#: includes/admin/post-types/admin-post-type.php:151
#: includes/admin/post-types/admin-post-types.php:345
msgid ""
"This post type could not be registered because its key is in use by another "
"post type registered by another plugin or theme."
msgstr ""
"Loại nội dung này không thể được đăng ký vì khóa của nó đang được sử dụng "
"bởi một loại nội dung khác được đăng ký bởi một plugin hoặc giao diện khác."

#: includes/admin/post-types/admin-post-type.php:126
msgid "Pages"
msgstr "Trang"

#: includes/admin/admin-internal-post-type.php:347
msgid "Link Existing Field Groups"
msgstr "Liên kết Nhóm Trường Hiện tại"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:80
msgid "%s post type created"
msgstr "%s Đã tạo loại nội dung"

#. translators: %s taxonomy name
#: includes/admin/post-types/admin-taxonomy.php:78
msgid "Add fields to %s"
msgstr "Thêm trường vào %s"

#. translators: %s post type name
#: includes/admin/post-types/admin-post-type.php:76
msgid "%s post type updated"
msgstr "%s Đã cập nhật loại nội dung"

#: includes/admin/post-types/admin-post-type.php:56
msgid "Post type draft updated."
msgstr "Bản nháp loại nội dung đã được cập nhật."

#: includes/admin/post-types/admin-post-type.php:55
msgid "Post type scheduled for."
msgstr "Loại nội dung được lên lịch cho."

#: includes/admin/post-types/admin-post-type.php:54
msgid "Post type submitted."
msgstr "Loại nội dung đã được gửi."

#: includes/admin/post-types/admin-post-type.php:53
msgid "Post type saved."
msgstr "Loại nội dung đã được lưu."

#: includes/admin/post-types/admin-post-type.php:50
msgid "Post type updated."
msgstr "Loại nội dung đã được cập nhật."

#: includes/admin/post-types/admin-post-type.php:49
msgid "Post type deleted."
msgstr "Loại nội dung đã được xóa."

#: includes/admin/post-types/admin-field-group.php:146
msgid "Type to search..."
msgstr "Nhập để tìm kiếm..."

#: includes/admin/post-types/admin-field-group.php:101
msgid "PRO Only"
msgstr "Chỉ dành cho PRO"

#: includes/admin/post-types/admin-field-group.php:93
msgid "Field groups linked successfully."
msgstr "Nhóm trường đã được liên kết thành công."

#. translators: %s - URL to ACF tools page.
#: includes/admin/admin.php:199
msgid ""
"Import Post Types and Taxonomies registered with Custom Post Type UI and "
"manage them with ACF. <a href=\"%s\">Get Started</a>."
msgstr ""
"Nhập loại nội dung và Phân loại đã đăng ký với Giao diện người dùng loại nội "
"dung Tùy chỉnh và quản lý chúng với ACF. <a href=\"%s\">Bắt đầu</a>."

#: includes/admin/admin.php:46 includes/admin/admin.php:361
#: src/Site_Health/Site_Health.php:254
msgid "ACF"
msgstr "ACF"

#: includes/admin/admin-internal-post-type.php:314
msgid "taxonomy"
msgstr "phân loại"

#: includes/admin/admin-internal-post-type.php:314
msgid "post type"
msgstr "loại nội dung"

#: includes/admin/admin-internal-post-type.php:338
msgid "Done"
msgstr "Hoàn tất"

#: includes/admin/admin-internal-post-type.php:324
msgid "Field Group(s)"
msgstr "Nhóm trường"

#: includes/admin/admin-internal-post-type.php:323
msgid "Select one or many field groups..."
msgstr "Chọn một hoặc nhiều nhóm trường..."

#: includes/admin/admin-internal-post-type.php:322
msgid "Please select the field groups to link."
msgstr "Vui lòng chọn nhóm trường để liên kết."

#: includes/admin/admin-internal-post-type.php:280
msgid "Field group linked successfully."
msgid_plural "Field groups linked successfully."
msgstr[0] "Nhóm trường đã được liên kết thành công."

#: includes/admin/admin-internal-post-type-list.php:277
#: includes/admin/post-types/admin-post-types.php:346
#: includes/admin/post-types/admin-taxonomies.php:352
msgctxt "post status"
msgid "Registration Failed"
msgstr "Đăng ký Thất bại"

#: includes/admin/admin-internal-post-type-list.php:276
msgid ""
"This item could not be registered because its key is in use by another item "
"registered by another plugin or theme."
msgstr ""
"Mục này không thể được đăng ký vì khóa của nó đang được sử dụng bởi một mục "
"khác được đăng ký bởi một plugin hoặc giao diện khác."

#: includes/acf-internal-post-type-functions.php:509
#: includes/acf-internal-post-type-functions.php:538
msgid "REST API"
msgstr "REST API"

#: includes/acf-internal-post-type-functions.php:508
#: includes/acf-internal-post-type-functions.php:537
#: includes/acf-internal-post-type-functions.php:564
msgid "Permissions"
msgstr "Quyền"

#: includes/acf-internal-post-type-functions.php:507
#: includes/acf-internal-post-type-functions.php:536
msgid "URLs"
msgstr "URL"

#: includes/acf-internal-post-type-functions.php:506
#: includes/acf-internal-post-type-functions.php:535
#: includes/acf-internal-post-type-functions.php:562
msgid "Visibility"
msgstr "Khả năng hiển thị"

#: includes/acf-internal-post-type-functions.php:505
#: includes/acf-internal-post-type-functions.php:534
#: includes/acf-internal-post-type-functions.php:563
msgid "Labels"
msgstr "Nhãn"

#: includes/admin/post-types/admin-field-group.php:279
msgid "Field Settings Tabs"
msgstr "Thẻ thiết lập trường"

#. Author URI of the plugin
#: acf.php
msgid ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"
msgstr ""
"https://wpengine.com/?"
"utm_source=wordpress.org&utm_medium=referral&utm_campaign=plugin_directory&utm_content=advanced_custom_fields"

#: includes/api/api-template.php:1027
msgid "[ACF shortcode value disabled for preview]"
msgstr "[Giá trị shortcode ACF bị tắt xem trước]"

#: includes/admin/admin-internal-post-type.php:290
#: includes/admin/post-types/admin-field-group.php:572
msgid "Close Modal"
msgstr "Thoát hộp cửa sổ"

#: includes/admin/post-types/admin-field-group.php:92
msgid "Field moved to other group"
msgstr "Trường được chuyển đến nhóm khác"

#: includes/admin/post-types/admin-field-group.php:91
msgid "Close modal"
msgstr "Thoát hộp cửa sổ"

#: includes/fields/class-acf-field-tab.php:122
msgid "Start a new group of tabs at this tab."
msgstr "Bắt đầu một nhóm mới của các tab tại tab này."

#: includes/fields/class-acf-field-tab.php:121
msgid "New Tab Group"
msgstr "Nhóm Tab mới"

#: includes/fields/class-acf-field-select.php:421
#: includes/fields/class-acf-field-true_false.php:188
msgid "Use a stylized checkbox using select2"
msgstr "Sử dụng hộp kiểm được tạo kiểu bằng select2"

#: includes/fields/class-acf-field-radio.php:250
msgid "Save Other Choice"
msgstr "Lưu Lựa chọn khác"

#: includes/fields/class-acf-field-radio.php:239
msgid "Allow Other Choice"
msgstr "Cho phép lựa chọn khác"

#: includes/fields/class-acf-field-checkbox.php:420
msgid "Add Toggle All"
msgstr "Thêm chuyển đổi tất cả"

#: includes/fields/class-acf-field-checkbox.php:379
msgid "Save Custom Values"
msgstr "Lưu Giá trị Tùy chỉnh"

#: includes/fields/class-acf-field-checkbox.php:368
msgid "Allow Custom Values"
msgstr "Cho phép giá trị tùy chỉnh"

#: includes/fields/class-acf-field-checkbox.php:134
msgid "Checkbox custom values cannot be empty. Uncheck any empty values."
msgstr ""
"Giá trị tùy chỉnh của hộp kiểm không thể trống. Bỏ chọn bất kỳ giá trị trống "
"nào."

#: includes/admin/views/global/navigation.php:256
msgid "Updates"
msgstr "Cập nhật"

#: includes/admin/views/global/navigation.php:180
#: includes/admin/views/global/navigation.php:184
msgid "Advanced Custom Fields logo"
msgstr "Logo Advanced Custom Fields"

#: includes/admin/views/global/form-top.php:92
msgid "Save Changes"
msgstr "Lưu thay đổi"

#: includes/admin/views/global/form-top.php:79
msgid "Field Group Title"
msgstr "Tiêu đề nhóm trường"

#: includes/admin/views/acf-post-type/advanced-settings.php:709
#: includes/admin/views/global/form-top.php:3
msgid "Add title"
msgstr "Thêm tiêu đề"

#. translators: %s url to getting started guide
#: includes/admin/views/acf-field-group/list-empty.php:30
#: includes/admin/views/acf-post-type/list-empty.php:20
#: includes/admin/views/acf-taxonomy/list-empty.php:21
#: includes/admin/views/options-page-preview.php:13
msgid ""
"New to ACF? Take a look at our <a href=\"%s\" target=\"_blank\">getting "
"started guide</a>."
msgstr ""
"Mới sử dụng ACF? Hãy xem qua <a href=\"%s\" target=\"_blank\">hướng dẫn bắt "
"đầu của chúng tôi</a>."

#: includes/admin/views/acf-field-group/list-empty.php:24
msgid "Add Field Group"
msgstr "Thêm nhóm trường"

#. translators: %s url to creating a field group page
#: includes/admin/views/acf-field-group/list-empty.php:18
msgid ""
"ACF uses <a href=\"%s\" target=\"_blank\">field groups</a> to group custom "
"fields together, and then attach those fields to edit screens."
msgstr ""
"ACF sử dụng <a href=\"%s\" target=\"_blank\">nhóm trường</a> để nhóm các "
"trường tùy chỉnh lại với nhau, sau đó gắn các trường đó vào màn hình chỉnh "
"sửa."

#: includes/admin/views/acf-field-group/list-empty.php:12
msgid "Add Your First Field Group"
msgstr "Thêm nhóm trường đầu tiên của bạn"

#: includes/admin/admin-options-pages-preview.php:28
#: includes/admin/views/acf-field-group/pro-features.php:58
#: includes/admin/views/global/navigation.php:86
#: includes/admin/views/global/navigation.php:258
msgid "Options Pages"
msgstr "Trang cài đặt"

#: includes/admin/views/acf-field-group/pro-features.php:54
msgid "ACF Blocks"
msgstr "Khối ACF"

#: includes/admin/views/acf-field-group/pro-features.php:62
msgid "Gallery Field"
msgstr "Trường Album ảnh"

#: includes/admin/views/acf-field-group/pro-features.php:42
msgid "Flexible Content Field"
msgstr "Trường nội dung linh hoạt"

#: includes/admin/views/acf-field-group/pro-features.php:46
msgid "Repeater Field"
msgstr "Trường lặp lại"

#: includes/admin/views/global/navigation.php:218
msgid "Unlock Extra Features with ACF PRO"
msgstr "Mở khóa tính năng mở rộng với ACF PRO"

#: includes/admin/views/acf-field-group/options.php:267
msgid "Delete Field Group"
msgstr "Xóa nhóm trường"

#. translators: 1: Post creation date 2: Post creation time
#: includes/admin/views/acf-field-group/options.php:261
msgid "Created on %1$s at %2$s"
msgstr "Được tạo vào %1$s lúc %2$s"

#: includes/acf-field-group-functions.php:497
msgid "Group Settings"
msgstr "Cài đặt nhóm"

#: includes/acf-field-group-functions.php:495
msgid "Location Rules"
msgstr "Quy tắc vị trí"

#. translators: %s url to field types list
#: includes/admin/views/acf-field-group/fields.php:73
msgid ""
"Choose from over 30 field types. <a href=\"%s\" target=\"_blank\">Learn "
"more</a>."
msgstr ""
"Chọn từ hơn 30 loại trường. <a href=\"%s\" target=\"_blank\">Tìm hiểu thêm</"
"a>."

#: includes/admin/views/acf-field-group/fields.php:65
msgid ""
"Get started creating new custom fields for your posts, pages, custom post "
"types and other WordPress content."
msgstr ""
"Bắt đầu tạo các trường tùy chỉnh mới cho bài viết, trang, loại nội dung tùy "
"chỉnh và nội dung WordPress khác của bạn."

#: includes/admin/views/acf-field-group/fields.php:64
msgid "Add Your First Field"
msgstr "Thêm trường đầu tiên của bạn"

#. translators: A symbol (or text, if not available in your locale) meaning
#. "Order Number", in terms of positional placement.
#: includes/admin/views/acf-field-group/fields.php:43
msgid "#"
msgstr "#"

#: includes/admin/views/acf-field-group/fields.php:33
#: includes/admin/views/acf-field-group/fields.php:67
#: includes/admin/views/acf-field-group/fields.php:101
#: includes/admin/views/global/form-top.php:88
msgid "Add Field"
msgstr "Thêm trường"

#: includes/acf-field-group-functions.php:496 includes/fields.php:384
msgid "Presentation"
msgstr "Trình bày"

#: includes/fields.php:383
msgid "Validation"
msgstr "Xác thực"

#: includes/acf-internal-post-type-functions.php:504
#: includes/acf-internal-post-type-functions.php:533 includes/fields.php:382
msgid "General"
msgstr "Tổng quan"

#: includes/admin/tools/class-acf-admin-tool-import.php:67
msgid "Import JSON"
msgstr "Nhập JSON"

#: includes/admin/tools/class-acf-admin-tool-export.php:338
msgid "Export As JSON"
msgstr "Xuất JSON"

#. translators: %s number of field groups deactivated
#: includes/admin/post-types/admin-field-groups.php:360
msgid "Field group deactivated."
msgid_plural "%s field groups deactivated."
msgstr[0] "Nhóm trường %s đã bị ngừng kích hoạt."

#. translators: %s number of field groups activated
#: includes/admin/post-types/admin-field-groups.php:353
msgid "Field group activated."
msgid_plural "%s field groups activated."
msgstr[0] "Nhóm trường %s đã được kích hoạt."

#: includes/admin/admin-internal-post-type-list.php:470
#: includes/admin/admin-internal-post-type-list.php:496
msgid "Deactivate"
msgstr "Ngừng kích hoạt"

#: includes/admin/admin-internal-post-type-list.php:470
msgid "Deactivate this item"
msgstr "Ngừng kích hoạt mục này"

#: includes/admin/admin-internal-post-type-list.php:466
#: includes/admin/admin-internal-post-type-list.php:495
msgid "Activate"
msgstr "Kích hoạt"

#: includes/admin/admin-internal-post-type-list.php:466
msgid "Activate this item"
msgstr "Kích hoạt mục này"

#: includes/admin/post-types/admin-field-group.php:88
msgid "Move field group to trash?"
msgstr "Chuyển nhóm trường vào thùng rác?"

#: acf.php:520 includes/admin/admin-internal-post-type-list.php:264
#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Inactive"
msgstr "Không hoạt động"

#. Author of the plugin
#: acf.php includes/admin/views/global/navigation.php:240
msgid "WP Engine"
msgstr "WP Engine"

#: acf.php:578
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields PRO."
msgstr ""
"Advanced Custom Fields và Advanced Custom Fields PRO không nên hoạt động "
"cùng một lúc. Chúng tôi đã tự động tắt Advanced Custom Fields PRO."

#: acf.php:576
msgid ""
"Advanced Custom Fields and Advanced Custom Fields PRO should not be active "
"at the same time. We've automatically deactivated Advanced Custom Fields."
msgstr ""
"Advanced Custom Fields và Advanced Custom Fields PRO không nên hoạt động "
"cùng một lúc. Chúng tôi đã tự động tắt Advanced Custom Fields."

#: includes/fields/class-acf-field-user.php:578
msgid "%1$s must have a user with the %2$s role."
msgid_plural "%1$s must have a user with one of the following roles: %2$s"
msgstr[0] "%1$s phải có một người dùng với vai trò %2$s."

#: includes/fields/class-acf-field-user.php:569
msgid "%1$s must have a valid user ID."
msgstr "%1$s phải có một ID người dùng hợp lệ."

#: includes/fields/class-acf-field-user.php:408
msgid "Invalid request."
msgstr "Yêu cầu không hợp lệ."

#: includes/fields/class-acf-field-select.php:689
msgid "%1$s is not one of %2$s"
msgstr "%1$s không phải là một trong %2$s"

#: includes/fields/class-acf-field-post_object.php:660
msgid "%1$s must have term %2$s."
msgid_plural "%1$s must have one of the following terms: %2$s"
msgstr[0] "%1$s phải có mục phân loại %2$s."

#: includes/fields/class-acf-field-post_object.php:644
msgid "%1$s must be of post type %2$s."
msgid_plural "%1$s must be of one of the following post types: %2$s"
msgstr[0] "%1$s phải là loại nội dung %2$s."

#: includes/fields/class-acf-field-post_object.php:635
msgid "%1$s must have a valid post ID."
msgstr "%1$s phải có một ID bài viết hợp lệ."

#: includes/fields/class-acf-field-file.php:447
msgid "%s requires a valid attachment ID."
msgstr "%s yêu cầu một ID đính kèm hợp lệ."

#: includes/admin/views/acf-field-group/options.php:233
msgid "Show in REST API"
msgstr "Hiển thị trong REST API"

#: includes/fields/class-acf-field-color_picker.php:156
msgid "Enable Transparency"
msgstr "Kích hoạt tính trong suốt"

#: includes/fields/class-acf-field-color_picker.php:175
msgid "RGBA Array"
msgstr "Array RGBA"

#: includes/fields/class-acf-field-color_picker.php:92
msgid "RGBA String"
msgstr "Chuỗi RGBA"

#: includes/fields/class-acf-field-color_picker.php:91
#: includes/fields/class-acf-field-color_picker.php:174
msgid "Hex String"
msgstr "Chuỗi Hex"

#: includes/admin/views/browse-fields-modal.php:12
msgid "Upgrade to PRO"
msgstr "Nâng cấp lên PRO"

#: includes/admin/post-types/admin-field-group.php:304
#: includes/admin/post-types/admin-post-type.php:282
#: includes/admin/post-types/admin-taxonomy.php:284
msgctxt "post status"
msgid "Active"
msgstr "Hoạt động"

#: includes/fields/class-acf-field-email.php:166
msgid "'%s' is not a valid email address"
msgstr "'%s' không phải là một địa chỉ email hợp lệ"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Color value"
msgstr "Giá trị màu"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Select default color"
msgstr "Chọn màu mặc định"

#: includes/fields/class-acf-field-color_picker.php:66
msgid "Clear color"
msgstr "Xóa màu"

#: includes/acf-wp-functions.php:90
msgid "Blocks"
msgstr "Khối"

#: includes/acf-wp-functions.php:86
msgid "Options"
msgstr "Tùy chọn"

#: includes/acf-wp-functions.php:82
msgid "Users"
msgstr "Người dùng"

#: includes/acf-wp-functions.php:78
msgid "Menu items"
msgstr "Mục menu"

#: includes/acf-wp-functions.php:70
msgid "Widgets"
msgstr "Tiện ích"

#: includes/acf-wp-functions.php:62
msgid "Attachments"
msgstr "Đính kèm các tệp"

#: includes/acf-wp-functions.php:57
#: includes/admin/post-types/admin-post-types.php:112
#: includes/admin/post-types/admin-taxonomies.php:86
#: includes/admin/tools/class-acf-admin-tool-import.php:90
#: includes/admin/views/acf-post-type/basic-settings.php:86
#: includes/post-types/class-acf-taxonomy.php:90
#: includes/post-types/class-acf-taxonomy.php:91
msgid "Taxonomies"
msgstr "Phân loại"

#: includes/acf-wp-functions.php:44
#: includes/admin/post-types/admin-post-type.php:124
#: includes/admin/post-types/admin-post-types.php:114
#: includes/admin/views/acf-post-type/advanced-settings.php:106
msgid "Posts"
msgstr "Bài viết"

#: includes/ajax/class-acf-ajax-local-json-diff.php:81
msgid "Last updated: %s"
msgstr "Cập nhật lần cuối: %s"

#: includes/ajax/class-acf-ajax-local-json-diff.php:75
msgid "Sorry, this post is unavailable for diff comparison."
msgstr "Xin lỗi, bài viết này không khả dụng để so sánh diff."

#: includes/ajax/class-acf-ajax-local-json-diff.php:47
msgid "Invalid field group parameter(s)."
msgstr "Tham số nhóm trường không hợp lệ."

#: includes/admin/admin-internal-post-type-list.php:429
msgid "Awaiting save"
msgstr "Đang chờ lưu"

#: includes/admin/admin-internal-post-type-list.php:426
msgid "Saved"
msgstr "Đã lưu"

#: includes/admin/admin-internal-post-type-list.php:422
#: includes/admin/tools/class-acf-admin-tool-import.php:46
msgid "Import"
msgstr "Nhập"

#: includes/admin/admin-internal-post-type-list.php:418
msgid "Review changes"
msgstr "Xem xét thay đổi"

#: includes/admin/admin-internal-post-type-list.php:394
msgid "Located in: %s"
msgstr "Đặt tại: %s"

#: includes/admin/admin-internal-post-type-list.php:391
msgid "Located in plugin: %s"
msgstr "Đặt trong plugin: %s"

#: includes/admin/admin-internal-post-type-list.php:388
msgid "Located in theme: %s"
msgstr "Đặt trong giao diện: %s"

#: includes/admin/post-types/admin-field-groups.php:235
msgid "Various"
msgstr "Đa dạng"

#: includes/admin/admin-internal-post-type-list.php:230
#: includes/admin/admin-internal-post-type-list.php:503
msgid "Sync changes"
msgstr "Đồng bộ hóa thay đổi"

#: includes/admin/admin-internal-post-type-list.php:229
msgid "Loading diff"
msgstr "Đang tải diff"

#: includes/admin/admin-internal-post-type-list.php:228
msgid "Review local JSON changes"
msgstr "Xem xét thay đổi JSON cục bộ"

#: includes/admin/admin.php:174
msgid "Visit website"
msgstr "Truy cập trang web"

#: includes/admin/admin.php:173
msgid "View details"
msgstr "Xem chi tiết"

#: includes/admin/admin.php:172
msgid "Version %s"
msgstr "Phiên bản %s"

#: includes/admin/admin.php:171
msgid "Information"
msgstr "Thông tin"

#: includes/admin/admin.php:162
msgid ""
"<a href=\"%s\" target=\"_blank\">Help Desk</a>. The support professionals on "
"our Help Desk will assist with your more in depth, technical challenges."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Bàn Giúp đỡ</a>. Các chuyên viên hỗ trợ tại "
"Bàn Giúp đỡ của chúng tôi sẽ giúp bạn giải quyết các thách thức kỹ thuật sâu "
"hơn."

#: includes/admin/admin.php:158
msgid ""
"<a href=\"%s\" target=\"_blank\">Discussions</a>. We have an active and "
"friendly community on our Community Forums who may be able to help you "
"figure out the 'how-tos' of the ACF world."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Thảo luận</a>. Chúng tôi có một cộng đồng "
"năng động và thân thiện trên Diễn đàn Cộng đồng của chúng tôi, có thể giúp "
"bạn tìm hiểu 'cách làm' trong thế giới ACF."

#: includes/admin/admin.php:154
msgid ""
"<a href=\"%s\" target=\"_blank\">Documentation</a>. Our extensive "
"documentation contains references and guides for most situations you may "
"encounter."
msgstr ""
"<a href=\"%s\" target=\"_blank\">Tài liệu</a>. Tài liệu rộng lớn của chúng "
"tôi chứa các tài liệu tham khảo và hướng dẫn cho hầu hết các tình huống bạn "
"có thể gặp phải."

#: includes/admin/admin.php:151
msgid ""
"We are fanatical about support, and want you to get the best out of your "
"website with ACF. If you run into any difficulties, there are several places "
"you can find help:"
msgstr ""
"Chúng tôi rất cuồng nhiệt về hỗ trợ, và muốn bạn có được những điều tốt nhất "
"từ trang web của bạn với ACF. Nếu bạn gặp bất kỳ khó khăn nào, có một số nơi "
"bạn có thể tìm kiếm sự giúp đỡ:"

#: includes/admin/admin.php:148 includes/admin/admin.php:150
msgid "Help & Support"
msgstr "Giúp đỡ & Hỗ trợ"

#: includes/admin/admin.php:139
msgid ""
"Please use the Help & Support tab to get in touch should you find yourself "
"requiring assistance."
msgstr ""
"Vui lòng sử dụng tab Giúp đỡ & Hỗ trợ để liên hệ nếu bạn cần sự hỗ trợ."

#: includes/admin/admin.php:136
msgid ""
"Before creating your first Field Group, we recommend first reading our <a "
"href=\"%s\" target=\"_blank\">Getting started</a> guide to familiarize "
"yourself with the plugin's philosophy and best practises."
msgstr ""
"Trước khi tạo Nhóm Trường đầu tiên của bạn, chúng tôi khuyên bạn nên đọc "
"hướng dẫn <a href=\"%s\" target=\"_blank\">Bắt đầu</a> của chúng tôi để làm "
"quen với triết lý và các phương pháp tốt nhất của plugin."

#: includes/admin/admin.php:134
msgid ""
"The Advanced Custom Fields plugin provides a visual form builder to "
"customize WordPress edit screens with extra fields, and an intuitive API to "
"display custom field values in any theme template file."
msgstr ""
"Plugin Advanced Custom Fields cung cấp một trình xây dựng form trực quan để "
"tùy chỉnh màn hình chỉnh sửa WordPress với các trường bổ sung, và một API "
"trực quan để hiển thị giá trị trường tùy chỉnh trong bất kỳ tệp mẫu giao "
"diện nào."

#: includes/admin/admin.php:131 includes/admin/admin.php:133
msgid "Overview"
msgstr "Tổng quan"

#. translators: %s the name of the location type
#: includes/locations.php:38
msgid "Location type \"%s\" is already registered."
msgstr "Loại vị trí \"%s\" đã được đăng ký."

#. translators: %s class name for a location that could not be found
#: includes/locations.php:26
msgid "Class \"%s\" does not exist."
msgstr "Lớp \"%s\" không tồn tại."

#: includes/ajax/class-acf-ajax-query-users.php:43
#: includes/ajax/class-acf-ajax.php:157
msgid "Invalid nonce."
msgstr "Số lần không hợp lệ."

#: includes/fields/class-acf-field-user.php:400
msgid "Error loading field."
msgstr "Lỗi tải trường."

#: includes/forms/form-user.php:328
msgid "<strong>Error</strong>: %s"
msgstr "<strong>Lỗi</strong>: %s"

#: includes/locations/class-acf-location-widget.php:22
msgid "Widget"
msgstr "Tiện ích"

#: includes/locations/class-acf-location-user-role.php:24
msgid "User Role"
msgstr "Vai trò người dùng"

#: includes/locations/class-acf-location-comment.php:22
msgid "Comment"
msgstr "Bình luận"

#: includes/locations/class-acf-location-post-format.php:22
msgid "Post Format"
msgstr "Định dạng bài viết"

#: includes/locations/class-acf-location-nav-menu-item.php:22
msgid "Menu Item"
msgstr "Mục menu"

#: includes/locations/class-acf-location-post-status.php:22
msgid "Post Status"
msgstr "Trang thái bài viết"

#: includes/acf-wp-functions.php:74
#: includes/locations/class-acf-location-nav-menu.php:89
msgid "Menus"
msgstr "Menus"

#: includes/locations/class-acf-location-nav-menu.php:80
msgid "Menu Locations"
msgstr "Vị trí menu"

#: includes/locations/class-acf-location-nav-menu.php:22
msgid "Menu"
msgstr "Menu"

#: includes/locations/class-acf-location-post-taxonomy.php:22
msgid "Post Taxonomy"
msgstr "Phân loại bài viết"

#: includes/locations/class-acf-location-page-type.php:114
msgid "Child Page (has parent)"
msgstr "Trang con (có trang cha)"

#: includes/locations/class-acf-location-page-type.php:113
msgid "Parent Page (has children)"
msgstr "Trang cha (có trang con)"

#: includes/locations/class-acf-location-page-type.php:112
msgid "Top Level Page (no parent)"
msgstr "Trang cấp cao nhất (không có trang cha)"

#: includes/locations/class-acf-location-page-type.php:111
msgid "Posts Page"
msgstr "Trang bài viết"

#: includes/locations/class-acf-location-page-type.php:110
msgid "Front Page"
msgstr "Trang chủ"

#: includes/locations/class-acf-location-page-type.php:22
msgid "Page Type"
msgstr "Loại trang"

#: includes/locations/class-acf-location-current-user.php:73
msgid "Viewing back end"
msgstr "Đang xem phía sau"

#: includes/locations/class-acf-location-current-user.php:72
msgid "Viewing front end"
msgstr "Đang xem phía trước"

#: includes/locations/class-acf-location-current-user.php:71
msgid "Logged in"
msgstr "Đã đăng nhập"

#: includes/locations/class-acf-location-current-user.php:22
msgid "Current User"
msgstr "Người dùng hiện tại"

#: includes/locations/class-acf-location-page-template.php:22
msgid "Page Template"
msgstr "Mẫu trang"

#: includes/locations/class-acf-location-user-form.php:74
msgid "Register"
msgstr "Register"

#: includes/locations/class-acf-location-user-form.php:73
msgid "Add / Edit"
msgstr "Thêm / Chỉnh sửa"

#: includes/locations/class-acf-location-user-form.php:22
msgid "User Form"
msgstr "Form người dùng"

#: includes/locations/class-acf-location-page-parent.php:22
msgid "Page Parent"
msgstr "Trang cha"

#: includes/locations/class-acf-location-current-user-role.php:77
msgid "Super Admin"
msgstr "Quản trị viên cấp cao"

#: includes/locations/class-acf-location-current-user-role.php:22
msgid "Current User Role"
msgstr "Vai trò người dùng hiện tại"

#: includes/locations/class-acf-location-page-template.php:73
#: includes/locations/class-acf-location-post-template.php:85
msgid "Default Template"
msgstr "Mẫu mặc định"

#: includes/locations/class-acf-location-post-template.php:22
msgid "Post Template"
msgstr "Mẫu bài viết"

#: includes/locations/class-acf-location-post-category.php:22
msgid "Post Category"
msgstr "Danh mục bài viết"

#: includes/locations/class-acf-location-attachment.php:84
msgid "All %s formats"
msgstr "Tất cả %s các định dạng"

#: includes/locations/class-acf-location-attachment.php:22
msgid "Attachment"
msgstr "Đính kèm tệp"

#: includes/validation.php:323
msgid "%s value is required"
msgstr "%s giá trị là bắt buộc"

#: includes/admin/views/acf-field-group/conditional-logic.php:64
msgid "Show this field if"
msgstr "Hiển thị trường này nếu"

#: includes/admin/views/acf-field-group/conditional-logic.php:25
#: includes/admin/views/acf-field-group/field.php:122 includes/fields.php:385
msgid "Conditional Logic"
msgstr "Điều kiện logic"

#: includes/admin/views/acf-field-group/conditional-logic.php:169
#: includes/admin/views/acf-field-group/location-rule.php:84
msgid "and"
msgstr "và"

#: includes/admin/post-types/admin-field-groups.php:97
#: includes/admin/post-types/admin-post-types.php:118
#: includes/admin/post-types/admin-taxonomies.php:117
msgid "Local JSON"
msgstr "JSON cục bộ"

#: includes/admin/views/acf-field-group/pro-features.php:50
msgid "Clone Field"
msgstr "Trường tạo bản sao"

#. translators: %s a list of plugin
#: includes/admin/views/upgrade/notice.php:32
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Vui lòng cũng kiểm tra tất cả các tiện ích mở rộng cao cấp (%s) đã được cập "
"nhật lên phiên bản mới nhất."

#: includes/admin/views/upgrade/notice.php:29
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Phiên bản này chứa các cải tiến cho cơ sở dữ liệu của bạn và yêu cầu nâng "
"cấp."

#. translators: %1 plugin name, %2 version number
#: includes/admin/views/upgrade/notice.php:28
msgid "Thank you for updating to %1$s v%2$s!"
msgstr "Cảm ơn bạn đã cập nhật lên %1$s v%2$s!"

#: includes/admin/views/upgrade/notice.php:26
msgid "Database Upgrade Required"
msgstr "Yêu cầu Nâng cấp Cơ sở dữ liệu"

#: includes/admin/post-types/admin-field-group.php:159
#: includes/admin/views/upgrade/notice.php:17
msgid "Options Page"
msgstr "Trang cài đặt"

#: includes/admin/views/upgrade/notice.php:14 includes/fields.php:436
msgid "Gallery"
msgstr "Album ảnh"

#: includes/admin/views/upgrade/notice.php:11 includes/fields.php:426
msgid "Flexible Content"
msgstr "Nội dung linh hoạt"

#: includes/admin/views/upgrade/notice.php:8 includes/fields.php:446
msgid "Repeater"
msgstr "Lặp lại"

#: includes/admin/views/tools/tools.php:16
msgid "Back to all tools"
msgstr "Quay lại tất cả các công cụ"

#: includes/admin/views/acf-field-group/options.php:195
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Nếu nhiều nhóm trường xuất hiện trên màn hình chỉnh sửa, các tùy chọn của "
"nhóm trường đầu tiên sẽ được sử dụng (nhóm có số thứ tự thấp nhất)"

#: includes/admin/views/acf-field-group/options.php:195
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Chọn</b> các mục để <b>ẩn</b> chúng khỏi màn hình chỉnh sửa."

#: includes/admin/views/acf-field-group/options.php:194
msgid "Hide on screen"
msgstr "Ẩn trên màn hình"

#: includes/admin/views/acf-field-group/options.php:186
msgid "Send Trackbacks"
msgstr "Gửi theo dõi liên kết"

#: includes/admin/post-types/admin-taxonomy.php:126
#: includes/admin/views/acf-field-group/options.php:185
#: includes/admin/views/acf-taxonomy/advanced-settings.php:159
msgid "Tags"
msgstr "Thẻ tag"

#: includes/admin/post-types/admin-taxonomy.php:128
#: includes/admin/views/acf-field-group/options.php:184
msgid "Categories"
msgstr "Danh mục"

#: includes/admin/views/acf-field-group/options.php:182
#: includes/admin/views/acf-post-type/advanced-settings.php:28
msgid "Page Attributes"
msgstr "Thuộc tính trang"

#: includes/admin/views/acf-field-group/options.php:181
msgid "Format"
msgstr "Định dạng"

#: includes/admin/views/acf-field-group/options.php:180
#: includes/admin/views/acf-post-type/advanced-settings.php:22
msgid "Author"
msgstr "Tác giả"

#: includes/admin/views/acf-field-group/options.php:179
msgid "Slug"
msgstr "Đường dẫn cố định"

#: includes/admin/views/acf-field-group/options.php:178
#: includes/admin/views/acf-post-type/advanced-settings.php:27
msgid "Revisions"
msgstr "Bản sửa đổi"

#: includes/acf-wp-functions.php:66
#: includes/admin/views/acf-field-group/options.php:177
#: includes/admin/views/acf-post-type/advanced-settings.php:23
msgid "Comments"
msgstr "Bình luận"

#: includes/admin/views/acf-field-group/options.php:176
msgid "Discussion"
msgstr "Thảo luận"

#: includes/admin/views/acf-field-group/options.php:174
#: includes/admin/views/acf-post-type/advanced-settings.php:26
msgid "Excerpt"
msgstr "Tóm tắt"

#: includes/admin/views/acf-field-group/options.php:173
msgid "Content Editor"
msgstr "Trình chỉnh sửa nội dung"

#: includes/admin/views/acf-field-group/options.php:172
msgid "Permalink"
msgstr "Liên kết cố định"

#: includes/admin/views/acf-field-group/options.php:250
msgid "Shown in field group list"
msgstr "Hiển thị trong danh sách nhóm trường"

#: includes/admin/views/acf-field-group/options.php:157
msgid "Field groups with a lower order will appear first"
msgstr "Nhóm trường có thứ tự thấp hơn sẽ xuất hiện đầu tiên"

#: includes/admin/views/acf-field-group/options.php:156
msgid "Order No."
msgstr "Số thứ tự"

#: includes/admin/views/acf-field-group/options.php:147
msgid "Below fields"
msgstr "Các trường bên dưới"

#: includes/admin/views/acf-field-group/options.php:146
msgid "Below labels"
msgstr "Dưới các nhãn"

#: includes/admin/views/acf-field-group/options.php:139
msgid "Instruction Placement"
msgstr "Vị trí hướng dẫn"

#: includes/admin/views/acf-field-group/options.php:122
msgid "Label Placement"
msgstr "Vị trí nhãn"

#: includes/admin/views/acf-field-group/options.php:110
msgid "Side"
msgstr "Thanh bên"

#: includes/admin/views/acf-field-group/options.php:109
msgid "Normal (after content)"
msgstr "Bình thường (sau nội dung)"

#: includes/admin/views/acf-field-group/options.php:108
msgid "High (after title)"
msgstr "Cao (sau tiêu đề)"

#: includes/admin/views/acf-field-group/options.php:101
msgid "Position"
msgstr "Vị trí"

#: includes/admin/views/acf-field-group/options.php:92
msgid "Seamless (no metabox)"
msgstr "Liền mạch (không có metabox)"

#: includes/admin/views/acf-field-group/options.php:91
msgid "Standard (WP metabox)"
msgstr "Tiêu chuẩn (WP metabox)"

#: includes/admin/views/acf-field-group/options.php:84
msgid "Style"
msgstr "Kiểu"

#: includes/admin/views/acf-field-group/fields.php:55
msgid "Type"
msgstr "Loại"

#: includes/admin/post-types/admin-field-groups.php:91
#: includes/admin/post-types/admin-post-types.php:111
#: includes/admin/post-types/admin-taxonomies.php:110
#: includes/admin/views/acf-field-group/fields.php:54
msgid "Key"
msgstr "Khóa"

#. translators: Hidden accessibility text for the positional order number of
#. the field.
#: includes/admin/views/acf-field-group/fields.php:48
msgid "Order"
msgstr "Đặt hàng"

#: includes/admin/views/acf-field-group/field.php:321
msgid "Close Field"
msgstr "Thoát trường"

#: includes/admin/views/acf-field-group/field.php:252
msgid "id"
msgstr "id"

#: includes/admin/views/acf-field-group/field.php:236
msgid "class"
msgstr "lớp"

#: includes/admin/views/acf-field-group/field.php:278
msgid "width"
msgstr "chiều rộng"

#: includes/admin/views/acf-field-group/field.php:272
msgid "Wrapper Attributes"
msgstr "Thuộc tính bao bọc"

#: includes/fields/class-acf-field.php:312
msgid "Required"
msgstr "Yêu cầu"

#: includes/admin/views/acf-field-group/field.php:219
msgid "Instructions"
msgstr "Hướng dẫn"

#: includes/admin/views/acf-field-group/field.php:142
msgid "Field Type"
msgstr "Loại trường"

#: includes/admin/views/acf-field-group/field.php:183
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr ""
"Một từ, không có khoảng trắng. Cho phép dấu gạch dưới và dấu gạch ngang"

#: includes/admin/views/acf-field-group/field.php:182
msgid "Field Name"
msgstr "Tên trường"

#: includes/admin/views/acf-field-group/field.php:170
msgid "This is the name which will appear on the EDIT page"
msgstr "Đây là tên sẽ xuất hiện trên trang CHỈNH SỬA"

#: includes/admin/views/acf-field-group/field.php:169
#: includes/admin/views/browse-fields-modal.php:69
msgid "Field Label"
msgstr "Nhãn trường"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete"
msgstr "Xóa"

#: includes/admin/views/acf-field-group/field.php:94
msgid "Delete field"
msgstr "Xóa trường"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move"
msgstr "Di chuyển"

#: includes/admin/views/acf-field-group/field.php:92
msgid "Move field to another group"
msgstr "Di chuyển trường sang nhóm khác"

#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate field"
msgstr "Trường Tạo bản sao"

#: includes/admin/views/acf-field-group/field.php:86
#: includes/admin/views/acf-field-group/field.php:89
msgid "Edit field"
msgstr "Chỉnh sửa trường"

#: includes/admin/views/acf-field-group/field.php:82
msgid "Drag to reorder"
msgstr "Kéo để sắp xếp lại"

#: includes/admin/post-types/admin-field-group.php:99
#: includes/admin/views/acf-field-group/location-group.php:3
msgid "Show this field group if"
msgstr "Hiển thị nhóm trường này nếu"

#: includes/admin/views/upgrade/upgrade.php:93
#: includes/ajax/class-acf-ajax-upgrade.php:34
msgid "No updates available."
msgstr "Không có bản cập nhật nào."

#. translators: %s the url to the field group page.
#: includes/admin/views/upgrade/upgrade.php:32
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr "Nâng cấp cơ sở dữ liệu hoàn tất. <a href=\"%s\">Xem những gì mới</a>"

#: includes/admin/views/upgrade/upgrade.php:27
msgid "Reading upgrade tasks..."
msgstr "Đang đọc các tác vụ nâng cấp..."

#: includes/admin/views/upgrade/network.php:165
#: includes/admin/views/upgrade/upgrade.php:64
msgid "Upgrade failed."
msgstr "Nâng cấp thất bại."

#: includes/admin/views/upgrade/network.php:162
msgid "Upgrade complete."
msgstr "Nâng cấp hoàn tất."

#. translators: %s the version being upgraded to.
#. translators: %s the new ACF version
#: includes/admin/views/upgrade/network.php:148
#: includes/admin/views/upgrade/upgrade.php:29
msgid "Upgrading data to version %s"
msgstr "Đang nâng cấp dữ liệu lên phiên bản %s"

#: includes/admin/views/upgrade/network.php:120
#: includes/admin/views/upgrade/notice.php:46
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Chúng tôi khuyến nghị bạn nên sao lưu cơ sở dữ liệu trước khi tiếp tục. Bạn "
"có chắc chắn muốn chạy trình cập nhật ngay bây giờ không?"

#: includes/admin/views/upgrade/network.php:116
msgid "Please select at least one site to upgrade."
msgstr "Vui lòng chọn ít nhất một trang web để nâng cấp."

#. translators: %s admin dashboard url page
#: includes/admin/views/upgrade/network.php:96
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Nâng cấp Cơ sở dữ liệu hoàn tất. <a href=\"%s\">Quay lại bảng điều khiển "
"mạng</a>"

#: includes/admin/views/upgrade/network.php:79
msgid "Site is up to date"
msgstr "Trang web đã được cập nhật"

#. translators: %1 current db version, %2 available db version
#: includes/admin/views/upgrade/network.php:77
msgid "Site requires database upgrade from %1$s to %2$s"
msgstr "Trang web yêu cầu nâng cấp cơ sở dữ liệu từ %1$s lên %2$s"

#: includes/admin/views/upgrade/network.php:34
#: includes/admin/views/upgrade/network.php:45
msgid "Site"
msgstr "Trang web"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
#: includes/admin/views/upgrade/network.php:25
#: includes/admin/views/upgrade/network.php:94
msgid "Upgrade Sites"
msgstr "Nâng cấp trang web"

#. translators: %s The button label name, translated seperately
#: includes/admin/views/upgrade/network.php:24
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Các trang web sau đây yêu cầu nâng cấp DB. Kiểm tra những trang web bạn muốn "
"cập nhật và sau đó nhấp %s."

#: includes/admin/views/acf-field-group/conditional-logic.php:184
#: includes/admin/views/acf-field-group/locations.php:37
msgid "Add rule group"
msgstr "Thêm nhóm quy tắc"

#: includes/admin/views/acf-field-group/locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Tạo một tập quy tắc để xác định màn hình chỉnh sửa nào sẽ sử dụng các trường "
"tùy chỉnh nâng cao này"

#: includes/admin/views/acf-field-group/locations.php:9
msgid "Rules"
msgstr "Quy tắc"

#: includes/admin/tools/class-acf-admin-tool-export.php:449
msgid "Copied"
msgstr "Đã sao chép"

#: includes/admin/tools/class-acf-admin-tool-export.php:425
msgid "Copy to clipboard"
msgstr "Sao chép vào bảng nhớ tạm"

#: includes/admin/tools/class-acf-admin-tool-export.php:331
msgid ""
"Select the items you would like to export and then select your export "
"method. Export As JSON to export to a .json file which you can then import "
"to another ACF installation. Generate PHP to export to PHP code which you "
"can place in your theme."
msgstr ""
"Chọn các mục bạn muốn xuất và sau đó chọn phương pháp xuất của bạn. Xuất "
"dưới dạng JSON để xuất ra tệp .json mà sau đó bạn có thể nhập vào cài đặt "
"ACF khác. Tạo PHP để xuất ra mã PHP mà bạn có thể đặt trong giao diện của "
"mình."

#: includes/admin/tools/class-acf-admin-tool-export.php:215
msgid "Select Field Groups"
msgstr "Chọn nhóm trường"

#: includes/admin/tools/class-acf-admin-tool-export.php:88
#: includes/admin/tools/class-acf-admin-tool-export.php:121
msgid "No field groups selected"
msgstr "Không có nhóm trường nào được chọn"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:339
#: includes/admin/tools/class-acf-admin-tool-export.php:363
msgid "Generate PHP"
msgstr "Xuất PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:34
msgid "Export Field Groups"
msgstr "Xuất nhóm trường"

#: includes/admin/tools/class-acf-admin-tool-import.php:172
msgid "Import file empty"
msgstr "Tệp nhập trống"

#: includes/admin/tools/class-acf-admin-tool-import.php:163
msgid "Incorrect file type"
msgstr "Loại tệp không chính xác"

#: includes/admin/tools/class-acf-admin-tool-import.php:158
msgid "Error uploading file. Please try again"
msgstr "Lỗi tải lên tệp. Vui lòng thử lại"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the items in that file."
msgstr ""
"Chọn tệp JSON Advanced Custom Fields mà bạn muốn nhập. Khi bạn nhấp vào nút "
"nhập dưới đây, ACF sẽ nhập các mục trong tệp đó."

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Nhập nhóm trường"

#: includes/admin/admin-internal-post-type-list.php:417
msgid "Sync"
msgstr "Đồng bộ"

#. translators: %s: field group title
#: includes/admin/admin-internal-post-type-list.php:960
msgid "Select %s"
msgstr "Lự chọn %s"

#: includes/admin/admin-internal-post-type-list.php:460
#: includes/admin/admin-internal-post-type-list.php:492
#: includes/admin/views/acf-field-group/field.php:90
msgid "Duplicate"
msgstr "Tạo bản sao"

#: includes/admin/admin-internal-post-type-list.php:460
msgid "Duplicate this item"
msgstr "Tạo bản sao mục này"

#: includes/admin/views/acf-post-type/advanced-settings.php:41
msgid "Supports"
msgstr "Hỗ trợ"

#: includes/admin/admin.php:355
#: includes/admin/views/browse-fields-modal.php:102
msgid "Documentation"
msgstr "Tài liệu hướng dẫn"

#: includes/admin/post-types/admin-field-groups.php:90
#: includes/admin/post-types/admin-post-types.php:110
#: includes/admin/post-types/admin-taxonomies.php:109
#: includes/admin/views/acf-field-group/options.php:249
#: includes/admin/views/acf-post-type/advanced-settings.php:62
#: includes/admin/views/acf-taxonomy/advanced-settings.php:114
#: includes/admin/views/upgrade/network.php:36
#: includes/admin/views/upgrade/network.php:47
msgid "Description"
msgstr "Mô tả"

#: includes/admin/admin-internal-post-type-list.php:414
#: includes/admin/admin-internal-post-type-list.php:832
msgid "Sync available"
msgstr "Đồng bộ hóa có sẵn"

#. translators: %s number of field groups synchronized
#: includes/admin/post-types/admin-field-groups.php:374
msgid "Field group synchronized."
msgid_plural "%s field groups synchronized."
msgstr[0] "Các nhóm trường %s đã được đồng bộ hóa."

#. translators: %s number of field groups duplicated
#: includes/admin/post-types/admin-field-groups.php:367
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "%s nhóm trường bị trùng lặp."

#: includes/admin/admin-internal-post-type-list.php:155
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Kích hoạt <span class=\"count\">(%s)</span>"

#: includes/admin/admin-upgrade.php:251
msgid "Review sites & upgrade"
msgstr "Xem xét các trang web & nâng cấp"

#: includes/admin/admin-upgrade.php:59 includes/admin/admin-upgrade.php:90
#: includes/admin/admin-upgrade.php:91 includes/admin/admin-upgrade.php:227
#: includes/admin/views/upgrade/network.php:21
#: includes/admin/views/upgrade/upgrade.php:23
msgid "Upgrade Database"
msgstr "Nâng cấp cơ sở dữ liệu"

#: includes/admin/views/acf-field-group/options.php:175
#: includes/admin/views/acf-post-type/advanced-settings.php:30
msgid "Custom Fields"
msgstr "Trường tùy chỉnh"

#: includes/admin/post-types/admin-field-group.php:609
msgid "Move Field"
msgstr "Di chuyển trường"

#: includes/admin/post-types/admin-field-group.php:602
#: includes/admin/post-types/admin-field-group.php:606
msgid "Please select the destination for this field"
msgstr "Vui lòng chọn điểm đến cho trường này"

#. translators: Confirmation message once a field has been moved to a different
#. field group.
#: includes/admin/post-types/admin-field-group.php:568
msgid "The %1$s field can now be found in the %2$s field group"
msgstr "Trường %1$s giờ đây có thể được tìm thấy trong nhóm trường %2$s"

#: includes/admin/post-types/admin-field-group.php:565
msgid "Move Complete."
msgstr "Di chuyển hoàn tất."

#: includes/admin/views/acf-field-group/field.php:52
#: includes/admin/views/acf-field-group/options.php:217
#: includes/admin/views/acf-post-type/advanced-settings.php:78
#: includes/admin/views/acf-taxonomy/advanced-settings.php:130
msgid "Active"
msgstr "Hoạt động"

#: includes/admin/post-types/admin-field-group.php:276
msgid "Field Keys"
msgstr "Khóa trường"

#: includes/admin/post-types/admin-field-group.php:180
msgid "Settings"
msgstr "Cài đặt"

#: includes/admin/post-types/admin-field-groups.php:92
msgid "Location"
msgstr "Vị trí"

#: includes/admin/post-types/admin-field-group.php:100
msgid "Null"
msgstr "Giá trị rỗng"

#: includes/admin/post-types/admin-field-group.php:97
#: includes/class-acf-internal-post-type.php:728
#: includes/post-types/class-acf-field-group.php:345
msgid "copy"
msgstr "sao chép"

#: includes/admin/post-types/admin-field-group.php:96
msgid "(this field)"
msgstr "(trường này)"

#: includes/admin/post-types/admin-field-group.php:94
msgid "Checked"
msgstr "Đã kiểm tra"

#: includes/admin/post-types/admin-field-group.php:90
msgid "Move Custom Field"
msgstr "Di chuyển trường tùy chỉnh"

#: includes/admin/post-types/admin-field-group.php:89
msgid "No toggle fields available"
msgstr "Không có trường chuyển đổi nào"

#: includes/admin/post-types/admin-field-group.php:87
msgid "Field group title is required"
msgstr "Tiêu đề nhóm trường là bắt buộc"

#: includes/admin/post-types/admin-field-group.php:86
msgid "This field cannot be moved until its changes have been saved"
msgstr ""
"Trường này không thể di chuyển cho đến khi các thay đổi của nó đã được lưu"

#: includes/admin/post-types/admin-field-group.php:85
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Chuỗi \"field_\" không được sử dụng ở đầu tên trường"

#: includes/admin/post-types/admin-field-group.php:69
msgid "Field group draft updated."
msgstr "Bản nháp nhóm trường đã được cập nhật."

#: includes/admin/post-types/admin-field-group.php:68
msgid "Field group scheduled for."
msgstr "Nhóm trường đã được lên lịch."

#: includes/admin/post-types/admin-field-group.php:67
msgid "Field group submitted."
msgstr "Nhóm trường đã được gửi."

#: includes/admin/post-types/admin-field-group.php:66
msgid "Field group saved."
msgstr "Nhóm trường đã được lưu."

#: includes/admin/post-types/admin-field-group.php:65
msgid "Field group published."
msgstr "Nhóm trường đã được xuất bản."

#: includes/admin/post-types/admin-field-group.php:62
msgid "Field group deleted."
msgstr "Nhóm trường đã bị xóa."

#: includes/admin/post-types/admin-field-group.php:60
#: includes/admin/post-types/admin-field-group.php:61
#: includes/admin/post-types/admin-field-group.php:63
msgid "Field group updated."
msgstr "Nhóm trường đã được cập nhật."

#: includes/admin/admin-tools.php:107
#: includes/admin/views/global/navigation.php:254
#: includes/admin/views/tools/tools.php:13
msgid "Tools"
msgstr "Công cụ"

#: includes/locations/abstract-acf-location.php:105
msgid "is not equal to"
msgstr "không bằng với"

#: includes/locations/abstract-acf-location.php:104
msgid "is equal to"
msgstr "bằng với"

#: includes/locations.php:104
msgid "Forms"
msgstr "Biểu mẫu"

#: includes/admin/post-types/admin-post-type.php:125 includes/locations.php:102
#: includes/locations/class-acf-location-page.php:22
msgid "Page"
msgstr "Trang"

#: includes/admin/post-types/admin-post-type.php:123 includes/locations.php:101
#: includes/locations/class-acf-location-post.php:22
msgid "Post"
msgstr "Bài viết"

#: includes/fields.php:328
msgid "Relational"
msgstr "Quan hệ"

#: includes/fields.php:327
msgid "Choice"
msgstr "Lựa chọn"

#: includes/fields.php:325
msgid "Basic"
msgstr "Cơ bản"

#: includes/fields.php:276
msgid "Unknown"
msgstr "Không rõ"

#: includes/fields.php:276
msgid "Field type does not exist"
msgstr "Loại trường không tồn tại"

#: includes/forms/form-front.php:220
msgid "Spam Detected"
msgstr "Phát hiện spam"

#: includes/forms/form-front.php:103
msgid "Post updated"
msgstr "Bài viết đã được cập nhật"

#: includes/forms/form-front.php:102
msgid "Update"
msgstr "Cập nhật"

#: includes/forms/form-front.php:63
msgid "Validate Email"
msgstr "Xác thực Email"

#: includes/fields.php:326 includes/forms/form-front.php:55
msgid "Content"
msgstr "Nội dung"

#: includes/admin/views/acf-post-type/advanced-settings.php:21
#: includes/forms/form-front.php:46
msgid "Title"
msgstr "Tiêu đề"

#: includes/assets.php:376 includes/forms/form-comment.php:140
msgid "Edit field group"
msgstr "Chỉnh sửa nhóm trường"

#: includes/admin/post-types/admin-field-group.php:113
msgid "Selection is less than"
msgstr "Lựa chọn ít hơn"

#: includes/admin/post-types/admin-field-group.php:112
msgid "Selection is greater than"
msgstr "Lựa chọn nhiều hơn"

#: includes/admin/post-types/admin-field-group.php:111
msgid "Value is less than"
msgstr "Giá trị nhỏ hơn"

#: includes/admin/post-types/admin-field-group.php:110
msgid "Value is greater than"
msgstr "Giá trị lớn hơn"

#: includes/admin/post-types/admin-field-group.php:109
msgid "Value contains"
msgstr "Giá trị chứa"

#: includes/admin/post-types/admin-field-group.php:108
msgid "Value matches pattern"
msgstr "Giá trị phù hợp với mô hình"

#: includes/admin/post-types/admin-field-group.php:107
msgid "Value is not equal to"
msgstr "Giá trị không bằng với"

#: includes/admin/post-types/admin-field-group.php:106
msgid "Value is equal to"
msgstr "Giá trị bằng với"

#: includes/admin/post-types/admin-field-group.php:105
msgid "Has no value"
msgstr "Không có giá trị"

#: includes/admin/post-types/admin-field-group.php:104
msgid "Has any value"
msgstr "Có bất kỳ giá trị nào"

#: includes/admin/admin-internal-post-type.php:337
#: includes/admin/views/browse-fields-modal.php:72 includes/assets.php:354
msgid "Cancel"
msgstr "Hủy"

#: includes/assets.php:350
msgid "Are you sure?"
msgstr "Bạn có chắc không?"

#: includes/assets.php:370
msgid "%d fields require attention"
msgstr "%d trường cần chú ý"

#: includes/assets.php:369
msgid "1 field requires attention"
msgstr "1 trường cần chú ý"

#: includes/assets.php:368 includes/validation.php:257
#: includes/validation.php:265
msgid "Validation failed"
msgstr "Xác thực thất bại"

#: includes/assets.php:367
msgid "Validation successful"
msgstr "Xác thực thành công"

#: includes/media.php:54
msgid "Restricted"
msgstr "Bị hạn chế"

#: includes/media.php:53
msgid "Collapse Details"
msgstr "Thu gọn chi tiết"

#: includes/media.php:52
msgid "Expand Details"
msgstr "Mở rộng chi tiết"

#: includes/admin/views/acf-post-type/advanced-settings.php:470
#: includes/media.php:51
msgid "Uploaded to this post"
msgstr "Đã tải lên bài viết này"

#: includes/media.php:50
msgctxt "verb"
msgid "Update"
msgstr "Cập nhật"

#: includes/media.php:49
msgctxt "verb"
msgid "Edit"
msgstr "Chỉnh sửa"

#: includes/assets.php:364
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""
"Những thay đổi bạn đã thực hiện sẽ bị mất nếu bạn điều hướng ra khỏi trang "
"này"

#: includes/api/api-helpers.php:3000
msgid "File type must be %s."
msgstr "Loại tệp phải là %s."

#: includes/admin/post-types/admin-field-group.php:98
#: includes/admin/views/acf-field-group/conditional-logic.php:64
#: includes/admin/views/acf-field-group/conditional-logic.php:182
#: includes/admin/views/acf-field-group/location-group.php:3
#: includes/admin/views/acf-field-group/locations.php:35
#: includes/api/api-helpers.php:2997
msgid "or"
msgstr "hoặc"

#: includes/api/api-helpers.php:2973
msgid "File size must not exceed %s."
msgstr "Kích thước tệp không được vượt quá %s."

#: includes/api/api-helpers.php:2969
msgid "File size must be at least %s."
msgstr "Kích thước tệp phải ít nhất là %s."

#: includes/api/api-helpers.php:2956
msgid "Image height must not exceed %dpx."
msgstr "Chiều cao hình ảnh không được vượt quá %dpx."

#: includes/api/api-helpers.php:2952
msgid "Image height must be at least %dpx."
msgstr "Chiều cao hình ảnh phải ít nhất là %dpx."

#: includes/api/api-helpers.php:2940
msgid "Image width must not exceed %dpx."
msgstr "Chiều rộng hình ảnh không được vượt quá %dpx."

#: includes/api/api-helpers.php:2936
msgid "Image width must be at least %dpx."
msgstr "Chiều rộng hình ảnh phải ít nhất là %dpx."

#: includes/api/api-helpers.php:1425 includes/api/api-term.php:140
msgid "(no title)"
msgstr "(không tiêu đề)"

#: includes/api/api-helpers.php:781
msgid "Full Size"
msgstr "Kích thước đầy đủ"

#: includes/api/api-helpers.php:746
msgid "Large"
msgstr "Lớn"

#: includes/api/api-helpers.php:745
msgid "Medium"
msgstr "Trung bình"

#: includes/api/api-helpers.php:744
msgid "Thumbnail"
msgstr "Hình thu nhỏ"

#: includes/acf-field-functions.php:854
#: includes/admin/post-types/admin-field-group.php:95
msgid "(no label)"
msgstr "(không nhãn)"

#: includes/fields/class-acf-field-textarea.php:135
msgid "Sets the textarea height"
msgstr "Đặt chiều cao của ô nhập liệu dạng văn bản"

#: includes/fields/class-acf-field-textarea.php:134
msgid "Rows"
msgstr "Hàng"

#: includes/fields/class-acf-field-textarea.php:22
msgid "Text Area"
msgstr "Vùng chứa văn bản"

#: includes/fields/class-acf-field-checkbox.php:421
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Thêm vào một hộp kiểm phụ để chuyển đổi tất cả các lựa chọn"

#: includes/fields/class-acf-field-checkbox.php:383
msgid "Save 'custom' values to the field's choices"
msgstr "Lưu giá trị 'tùy chỉnh' vào các lựa chọn của trường"

#: includes/fields/class-acf-field-checkbox.php:372
msgid "Allow 'custom' values to be added"
msgstr "Cho phép thêm giá trị 'tùy chỉnh'"

#: includes/fields/class-acf-field-checkbox.php:35
msgid "Add new choice"
msgstr "Thêm lựa chọn mới"

#: includes/fields/class-acf-field-checkbox.php:157
msgid "Toggle All"
msgstr "Chọn tất cả"

#: includes/fields/class-acf-field-page_link.php:487
msgid "Allow Archives URLs"
msgstr "Cho phép URL lưu trữ"

#: includes/fields/class-acf-field-page_link.php:196
msgid "Archives"
msgstr "Trang lưu trữ"

#: includes/fields/class-acf-field-page_link.php:22
msgid "Page Link"
msgstr "Liên kết trang"

#: includes/fields/class-acf-field-taxonomy.php:884
#: includes/locations/class-acf-location-user-form.php:72
msgid "Add"
msgstr "Thêm"

#: includes/admin/views/acf-field-group/fields.php:53
#: includes/fields/class-acf-field-taxonomy.php:854
msgid "Name"
msgstr "Tên"

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "%s added"
msgstr "%s đã được thêm"

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "%s already exists"
msgstr "%s đã tồn tại"

#: includes/fields/class-acf-field-taxonomy.php:791
msgid "User unable to add new %s"
msgstr "Người dùng không thể thêm mới %s"

#: includes/fields/class-acf-field-taxonomy.php:678
msgid "Term ID"
msgstr "ID mục phân loại"

#: includes/fields/class-acf-field-taxonomy.php:677
msgid "Term Object"
msgstr "Đối tượng mục phân loại"

#: includes/fields/class-acf-field-taxonomy.php:662
msgid "Load value from posts terms"
msgstr "Tải giá trị từ các mục phân loại bài viết"

#: includes/fields/class-acf-field-taxonomy.php:661
msgid "Load Terms"
msgstr "Tải mục phân loại"

#: includes/fields/class-acf-field-taxonomy.php:651
msgid "Connect selected terms to the post"
msgstr "Kết nối các mục phân loại đã chọn với bài viết"

#: includes/fields/class-acf-field-taxonomy.php:650
msgid "Save Terms"
msgstr "Lưu mục phân loại"

#: includes/fields/class-acf-field-taxonomy.php:640
msgid "Allow new terms to be created whilst editing"
msgstr "Cho phép tạo mục phân loại mới trong khi chỉnh sửa"

#: includes/fields/class-acf-field-taxonomy.php:639
msgid "Create Terms"
msgstr "Tạo mục phân loại"

#: includes/fields/class-acf-field-taxonomy.php:698
msgid "Radio Buttons"
msgstr "Các nút chọn"

#: includes/fields/class-acf-field-taxonomy.php:697
msgid "Single Value"
msgstr "Giá trị đơn"

#: includes/fields/class-acf-field-taxonomy.php:695
msgid "Multi Select"
msgstr "Chọn nhiều mục"

#: includes/fields/class-acf-field-checkbox.php:22
#: includes/fields/class-acf-field-taxonomy.php:694
msgid "Checkbox"
msgstr "Hộp kiểm"

#: includes/fields/class-acf-field-taxonomy.php:693
msgid "Multiple Values"
msgstr "Nhiều giá trị"

#: includes/fields/class-acf-field-taxonomy.php:688
msgid "Select the appearance of this field"
msgstr "Chọn hiển thị của trường này"

#: includes/fields/class-acf-field-taxonomy.php:687
msgid "Appearance"
msgstr "Hiển thị"

#: includes/fields/class-acf-field-taxonomy.php:629
msgid "Select the taxonomy to be displayed"
msgstr "Chọn phân loại để hiển thị"

#: includes/fields/class-acf-field-taxonomy.php:593
msgctxt "No Terms"
msgid "No %s"
msgstr "Không %s"

#: includes/fields/class-acf-field-number.php:240
msgid "Value must be equal to or lower than %d"
msgstr "Giá trị phải bằng hoặc thấp hơn %d"

#: includes/fields/class-acf-field-number.php:235
msgid "Value must be equal to or higher than %d"
msgstr "Giá trị phải bằng hoặc cao hơn %d"

#: includes/fields/class-acf-field-number.php:223
msgid "Value must be a number"
msgstr "Giá trị phải là một số"

#: includes/fields/class-acf-field-number.php:22
msgid "Number"
msgstr "Dạng số"

#: includes/fields/class-acf-field-radio.php:254
msgid "Save 'other' values to the field's choices"
msgstr "Lưu các giá trị 'khác' vào lựa chọn của trường"

#: includes/fields/class-acf-field-radio.php:243
msgid "Add 'other' choice to allow for custom values"
msgstr "Thêm lựa chọn 'khác' để cho phép các giá trị tùy chỉnh"

#: includes/admin/views/global/navigation.php:202
msgid "Other"
msgstr "Khác"

#: includes/fields/class-acf-field-radio.php:22
msgid "Radio Button"
msgstr "Nút chọn"

#: includes/fields/class-acf-field-accordion.php:106
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Xác định một điểm cuối để accordion trước đó dừng lại. Accordion này sẽ "
"không hiển thị."

#: includes/fields/class-acf-field-accordion.php:95
msgid "Allow this accordion to open without closing others."
msgstr "Cho phép accordion này mở mà không đóng các accordion khác."

#: includes/fields/class-acf-field-accordion.php:94
msgid "Multi-Expand"
msgstr "Mở rộng đa dạng"

#: includes/fields/class-acf-field-accordion.php:84
msgid "Display this accordion as open on page load."
msgstr "Hiển thị accordion này như đang mở khi tải trang."

#: includes/fields/class-acf-field-accordion.php:83
msgid "Open"
msgstr "Mở"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Mở rộng & Thu gọn"

#: includes/fields/class-acf-field-file.php:253
#: includes/fields/class-acf-field-file.php:265
msgid "Restrict which files can be uploaded"
msgstr "Hạn chế các tệp có thể tải lên"

#: includes/fields/class-acf-field-file.php:207
msgid "File ID"
msgstr "ID tệp"

#: includes/fields/class-acf-field-file.php:206
msgid "File URL"
msgstr "URL tệp"

#: includes/fields/class-acf-field-file.php:205
msgid "File Array"
msgstr "Array tập tin"

#: includes/fields/class-acf-field-file.php:176
msgid "Add File"
msgstr "Thêm tệp"

#: includes/admin/tools/class-acf-admin-tool-import.php:151
#: includes/fields/class-acf-field-file.php:176
msgid "No file selected"
msgstr "Không có tệp nào được chọn"

#: includes/fields/class-acf-field-file.php:140
msgid "File name"
msgstr "Tên tệp"

#: includes/fields/class-acf-field-file.php:57
msgid "Update File"
msgstr "Cập nhật tệp tin"

#: includes/fields/class-acf-field-file.php:56
msgid "Edit File"
msgstr "Sửa tệp tin"

#: includes/admin/tools/class-acf-admin-tool-import.php:55
#: includes/fields/class-acf-field-file.php:55
msgid "Select File"
msgstr "Chọn tệp tin"

#: includes/fields/class-acf-field-file.php:22
msgid "File"
msgstr "Tệp tin"

#: includes/fields/class-acf-field-password.php:22
msgid "Password"
msgstr "Mật khẩu"

#: includes/fields/class-acf-field-select.php:363
msgid "Specify the value returned"
msgstr "Chỉ định giá trị trả về"

#: includes/fields/class-acf-field-select.php:431
msgid "Use AJAX to lazy load choices?"
msgstr "Sử dụng AJAX để tải lựa chọn một cách lười biếng?"

#: includes/fields/class-acf-field-checkbox.php:333
#: includes/fields/class-acf-field-select.php:352
msgid "Enter each default value on a new line"
msgstr "Nhập mỗi giá trị mặc định trên một dòng mới"

#: includes/fields/class-acf-field-select.php:217 includes/media.php:48
msgctxt "verb"
msgid "Select"
msgstr "Lựa chọn"

#: includes/fields/class-acf-field-select.php:95
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Tải thất bại"

#: includes/fields/class-acf-field-select.php:94
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Đang tìm kiếm&hellip;"

#: includes/fields/class-acf-field-select.php:93
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Đang tải thêm kết quả&hellip;"

#. translators: %d - maximum number of items that can be selected in the select
#. field
#: includes/fields/class-acf-field-select.php:92
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Bạn chỉ có thể chọn %d mục"

#: includes/fields/class-acf-field-select.php:90
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Bạn chỉ có thể chọn 1 mục"

#. translators: %d - number of characters that should be removed from select
#. field
#: includes/fields/class-acf-field-select.php:89
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Vui lòng xóa %d ký tự"

#: includes/fields/class-acf-field-select.php:87
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Vui lòng xóa 1 ký tự"

#. translators: %d - number of characters to enter into select field input
#: includes/fields/class-acf-field-select.php:86
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Vui lòng nhập %d ký tự hoặc nhiều hơn"

#: includes/fields/class-acf-field-select.php:84
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Vui lòng nhập 1 ký tự hoặc nhiều hơn"

#: includes/fields/class-acf-field-select.php:83
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Không tìm thấy kết quả phù hợp"

#. translators: %d - number of results available in select field
#: includes/fields/class-acf-field-select.php:82
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"%d kết quả có sẵn, sử dụng các phím mũi tên lên và xuống để điều hướng."

#: includes/fields/class-acf-field-select.php:80
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Có một kết quả, nhấn enter để chọn."

#: includes/fields/class-acf-field-select.php:16
#: includes/fields/class-acf-field-taxonomy.php:699
msgctxt "noun"
msgid "Select"
msgstr "Lựa chọn"

#: includes/fields/class-acf-field-user.php:102
msgid "User ID"
msgstr "ID Người dùng"

#: includes/fields/class-acf-field-user.php:101
msgid "User Object"
msgstr "Đối tượng người dùng"

#: includes/fields/class-acf-field-user.php:100
msgid "User Array"
msgstr "Array người dùng"

#: includes/fields/class-acf-field-user.php:88
msgid "All user roles"
msgstr "Tất cả vai trò người dùng"

#: includes/fields/class-acf-field-user.php:80
msgid "Filter by Role"
msgstr "Lọc theo Vai trò"

#: includes/fields/class-acf-field-user.php:15 includes/locations.php:103
msgid "User"
msgstr "Người dùng"

#: includes/fields/class-acf-field-separator.php:22
msgid "Separator"
msgstr "Dấu phân cách"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Select Color"
msgstr "Chọn màu"

#: includes/admin/post-types/admin-post-type.php:127
#: includes/admin/post-types/admin-taxonomy.php:129
#: includes/fields/class-acf-field-color_picker.php:67
msgid "Default"
msgstr "Mặc định"

#: includes/admin/views/acf-post-type/advanced-settings.php:89
#: includes/admin/views/acf-taxonomy/advanced-settings.php:141
#: includes/fields/class-acf-field-color_picker.php:65
msgid "Clear"
msgstr "Xóa"

#: includes/fields/class-acf-field-color_picker.php:22
msgid "Color Picker"
msgstr "Bộ chọn màu"

#: includes/fields/class-acf-field-date_time_picker.php:82
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "CH"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "SA"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Chọn"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Hoàn tất"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Bây giờ"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Múi giờ"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Micro giây"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Mili giây"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Giây"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Phút"

#: includes/fields/class-acf-field-date_time_picker.php:67
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Giờ"

#: includes/fields/class-acf-field-date_time_picker.php:66
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Thời gian"

#: includes/fields/class-acf-field-date_time_picker.php:65
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Chọn thời gian"

#: includes/fields/class-acf-field-date_time_picker.php:22
msgid "Date Time Picker"
msgstr "Công cụ chọn ngày giờ"

#: includes/fields/class-acf-field-accordion.php:105
msgid "Endpoint"
msgstr "Điểm cuối"

#: includes/admin/views/acf-field-group/options.php:130
#: includes/fields/class-acf-field-tab.php:112
msgid "Left aligned"
msgstr "Căn lề trái"

#: includes/admin/views/acf-field-group/options.php:129
#: includes/fields/class-acf-field-tab.php:111
msgid "Top aligned"
msgstr "Căn lề trên"

#: includes/fields/class-acf-field-tab.php:107
msgid "Placement"
msgstr "Vị trí"

#: includes/fields/class-acf-field-tab.php:23
msgid "Tab"
msgstr "Tab"

#: includes/fields/class-acf-field-url.php:138
msgid "Value must be a valid URL"
msgstr "Giá trị phải là URL hợp lệ"

#: includes/fields/class-acf-field-link.php:153
msgid "Link URL"
msgstr "URL liên kết"

#: includes/fields/class-acf-field-link.php:152
msgid "Link Array"
msgstr "Array liên kết"

#: includes/fields/class-acf-field-link.php:124
msgid "Opens in a new window/tab"
msgstr "Mở trong cửa sổ/tab mới"

#: includes/fields/class-acf-field-link.php:119
msgid "Select Link"
msgstr "Chọn liên kết"

#: includes/fields/class-acf-field-link.php:22
msgid "Link"
msgstr "Liên kết"

#: includes/fields/class-acf-field-email.php:22
msgid "Email"
msgstr "Email"

#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-range.php:206
msgid "Step Size"
msgstr "Kích thước bước"

#: includes/fields/class-acf-field-number.php:143
#: includes/fields/class-acf-field-range.php:184
msgid "Maximum Value"
msgstr "Giá trị tối đa"

#: includes/fields/class-acf-field-number.php:133
#: includes/fields/class-acf-field-range.php:173
msgid "Minimum Value"
msgstr "Giá trị tối thiểu"

#: includes/fields/class-acf-field-range.php:22
msgid "Range"
msgstr "Thanh trượt phạm vi"

#: includes/fields/class-acf-field-button-group.php:165
#: includes/fields/class-acf-field-checkbox.php:350
#: includes/fields/class-acf-field-radio.php:210
#: includes/fields/class-acf-field-select.php:370
msgid "Both (Array)"
msgstr "Cả hai (Array)"

#: includes/admin/views/acf-field-group/fields.php:52
#: includes/fields/class-acf-field-button-group.php:164
#: includes/fields/class-acf-field-checkbox.php:349
#: includes/fields/class-acf-field-radio.php:209
#: includes/fields/class-acf-field-select.php:369
msgid "Label"
msgstr "Nhãn"

#: includes/fields/class-acf-field-button-group.php:163
#: includes/fields/class-acf-field-checkbox.php:348
#: includes/fields/class-acf-field-radio.php:208
#: includes/fields/class-acf-field-select.php:368
msgid "Value"
msgstr "Giá trị"

#: includes/fields/class-acf-field-button-group.php:211
#: includes/fields/class-acf-field-checkbox.php:411
#: includes/fields/class-acf-field-radio.php:282
msgid "Vertical"
msgstr "Dọc"

#: includes/fields/class-acf-field-button-group.php:210
#: includes/fields/class-acf-field-checkbox.php:412
#: includes/fields/class-acf-field-radio.php:283
msgid "Horizontal"
msgstr "Ngang"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "red : Red"
msgstr "red : Đỏ"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Để kiểm soát nhiều hơn, bạn có thể chỉ định cả giá trị và nhãn như thế này:"

#: includes/fields/class-acf-field-button-group.php:138
#: includes/fields/class-acf-field-checkbox.php:323
#: includes/fields/class-acf-field-radio.php:183
#: includes/fields/class-acf-field-select.php:341
msgid "Enter each choice on a new line."
msgstr "Nhập mỗi lựa chọn trên một dòng mới."

#: includes/fields/class-acf-field-button-group.php:137
#: includes/fields/class-acf-field-checkbox.php:322
#: includes/fields/class-acf-field-radio.php:182
#: includes/fields/class-acf-field-select.php:340
msgid "Choices"
msgstr "Lựa chọn"

#: includes/fields/class-acf-field-button-group.php:23
msgid "Button Group"
msgstr "Nhóm nút"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-page_link.php:519
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-radio.php:228
#: includes/fields/class-acf-field-select.php:399
#: includes/fields/class-acf-field-taxonomy.php:708
#: includes/fields/class-acf-field-user.php:132
msgid "Allow Null"
msgstr "Cho phép để trống"

#: includes/fields/class-acf-field-page_link.php:273
#: includes/fields/class-acf-field-post_object.php:254
#: includes/fields/class-acf-field-taxonomy.php:872
msgid "Parent"
msgstr "Cha"

#: includes/fields/class-acf-field-wysiwyg.php:367
msgid "TinyMCE will not be initialized until field is clicked"
msgstr "TinyMCE sẽ không được khởi tạo cho đến khi trường được nhấp"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Delay Initialization"
msgstr "Trì hoãn khởi tạo"

#: includes/fields/class-acf-field-wysiwyg.php:355
msgid "Show Media Upload Buttons"
msgstr "Hiển thị nút tải lên Media"

#: includes/fields/class-acf-field-wysiwyg.php:339
msgid "Toolbar"
msgstr "Thanh công cụ"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgid "Text Only"
msgstr "Chỉ văn bản"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual Only"
msgstr "Chỉ hình ảnh"

#: includes/fields/class-acf-field-wysiwyg.php:329
msgid "Visual & Text"
msgstr "Trực quan & văn bản"

#: includes/fields/class-acf-field-icon_picker.php:262
#: includes/fields/class-acf-field-wysiwyg.php:324
msgid "Tabs"
msgstr "Tab"

#: includes/fields/class-acf-field-wysiwyg.php:268
msgid "Click to initialize TinyMCE"
msgstr "Nhấp để khởi tạo TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:262
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Văn bản"

#: includes/fields/class-acf-field-wysiwyg.php:261
msgid "Visual"
msgstr "Trực quan"

#: includes/fields/class-acf-field-text.php:181
#: includes/fields/class-acf-field-textarea.php:217
msgid "Value must not exceed %d characters"
msgstr "Giá trị không được vượt quá %d ký tự"

#: includes/fields/class-acf-field-text.php:116
#: includes/fields/class-acf-field-textarea.php:114
msgid "Leave blank for no limit"
msgstr "Để trống nếu không giới hạn"

#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:113
msgid "Character Limit"
msgstr "Giới hạn ký tự"

#: includes/fields/class-acf-field-email.php:144
#: includes/fields/class-acf-field-number.php:194
#: includes/fields/class-acf-field-password.php:95
#: includes/fields/class-acf-field-range.php:228
#: includes/fields/class-acf-field-text.php:156
msgid "Appears after the input"
msgstr "Xuất hiện sau khi nhập"

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:193
#: includes/fields/class-acf-field-password.php:94
#: includes/fields/class-acf-field-range.php:227
#: includes/fields/class-acf-field-text.php:155
msgid "Append"
msgstr "Thêm vào"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:184
#: includes/fields/class-acf-field-password.php:85
#: includes/fields/class-acf-field-range.php:218
#: includes/fields/class-acf-field-text.php:146
msgid "Appears before the input"
msgstr "Xuất hiện trước đầu vào"

#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:183
#: includes/fields/class-acf-field-password.php:84
#: includes/fields/class-acf-field-range.php:217
#: includes/fields/class-acf-field-text.php:145
msgid "Prepend"
msgstr "Thêm vào đầu"

#: includes/fields/class-acf-field-email.php:124
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:75
#: includes/fields/class-acf-field-text.php:136
#: includes/fields/class-acf-field-textarea.php:146
#: includes/fields/class-acf-field-url.php:105
msgid "Appears within the input"
msgstr "Xuất hiện trong đầu vào"

#: includes/fields/class-acf-field-email.php:123
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:74
#: includes/fields/class-acf-field-text.php:135
#: includes/fields/class-acf-field-textarea.php:145
#: includes/fields/class-acf-field-url.php:104
msgid "Placeholder Text"
msgstr "Văn bản gợi ý"

#: includes/fields/class-acf-field-button-group.php:148
#: includes/fields/class-acf-field-email.php:104
#: includes/fields/class-acf-field-number.php:114
#: includes/fields/class-acf-field-radio.php:193
#: includes/fields/class-acf-field-range.php:154
#: includes/fields/class-acf-field-text.php:96
#: includes/fields/class-acf-field-textarea.php:94
#: includes/fields/class-acf-field-url.php:85
#: includes/fields/class-acf-field-wysiwyg.php:292
msgid "Appears when creating a new post"
msgstr "Xuất hiện khi tạo một bài viết mới"

#: includes/fields/class-acf-field-text.php:22
msgid "Text"
msgstr "Văn bản"

#: includes/fields/class-acf-field-relationship.php:753
msgid "%1$s requires at least %2$s selection"
msgid_plural "%1$s requires at least %2$s selections"
msgstr[0] "%1$s yêu cầu ít nhất %2$s lựa chọn"

#: includes/fields/class-acf-field-post_object.php:402
#: includes/fields/class-acf-field-relationship.php:616
msgid "Post ID"
msgstr "ID bài viết"

#: includes/fields/class-acf-field-post_object.php:15
#: includes/fields/class-acf-field-post_object.php:401
#: includes/fields/class-acf-field-relationship.php:615
msgid "Post Object"
msgstr "Đối tượng bài viết"

#: includes/fields/class-acf-field-relationship.php:648
msgid "Maximum Posts"
msgstr "Số bài viết tối đa"

#: includes/fields/class-acf-field-relationship.php:638
msgid "Minimum Posts"
msgstr "Số bài viết tối thiểu"

#: includes/admin/views/acf-field-group/options.php:183
#: includes/admin/views/acf-post-type/advanced-settings.php:29
#: includes/fields/class-acf-field-relationship.php:673
msgid "Featured Image"
msgstr "Ảnh đại diện"

#: includes/fields/class-acf-field-relationship.php:669
msgid "Selected elements will be displayed in each result"
msgstr "Các phần tử đã chọn sẽ được hiển thị trong mỗi kết quả"

#: includes/fields/class-acf-field-relationship.php:668
msgid "Elements"
msgstr "Các phần tử"

#: includes/fields/class-acf-field-relationship.php:602
#: includes/fields/class-acf-field-taxonomy.php:20
#: includes/fields/class-acf-field-taxonomy.php:628
#: includes/locations/class-acf-location-taxonomy.php:22
msgid "Taxonomy"
msgstr "Phân loại"

#: includes/fields/class-acf-field-relationship.php:601
#: includes/locations/class-acf-location-post-type.php:22
#: includes/post-types/class-acf-post-type.php:92
msgid "Post Type"
msgstr "Loại nội dung"

#: includes/fields/class-acf-field-relationship.php:595
msgid "Filters"
msgstr "Bộ lọc"

#: includes/fields/class-acf-field-page_link.php:480
#: includes/fields/class-acf-field-post_object.php:389
#: includes/fields/class-acf-field-relationship.php:588
msgid "All taxonomies"
msgstr "Tất cả các phân loại"

#: includes/fields/class-acf-field-page_link.php:472
#: includes/fields/class-acf-field-post_object.php:381
#: includes/fields/class-acf-field-relationship.php:580
msgid "Filter by Taxonomy"
msgstr "Lọc theo phân loại"

#: includes/fields/class-acf-field-page_link.php:450
#: includes/fields/class-acf-field-post_object.php:359
#: includes/fields/class-acf-field-relationship.php:558
msgid "All post types"
msgstr "Tất cả loại nội dung"

#: includes/fields/class-acf-field-page_link.php:442
#: includes/fields/class-acf-field-post_object.php:351
#: includes/fields/class-acf-field-relationship.php:550
msgid "Filter by Post Type"
msgstr "Lọc theo loại nội dung"

#: includes/fields/class-acf-field-relationship.php:450
msgid "Search..."
msgstr "Tìm kiếm..."

#: includes/fields/class-acf-field-relationship.php:380
msgid "Select taxonomy"
msgstr "Chọn phân loại"

#: includes/fields/class-acf-field-relationship.php:372
msgid "Select post type"
msgstr "Chọn loại nội dung"

#: includes/fields/class-acf-field-relationship.php:78
msgid "No matches found"
msgstr "Không tìm thấy kết quả nào"

#: includes/fields/class-acf-field-relationship.php:77
msgid "Loading"
msgstr "Đang tải"

#: includes/fields/class-acf-field-relationship.php:76
msgid "Maximum values reached ( {max} values )"
msgstr "Đã đạt giá trị tối đa ( {max} giá trị )"

#: includes/fields/class-acf-field-relationship.php:17
msgid "Relationship"
msgstr "Mối quan hệ"

#: includes/fields/class-acf-field-file.php:277
#: includes/fields/class-acf-field-image.php:307
msgid "Comma separated list. Leave blank for all types"
msgstr "Danh sách được phân tách bằng dấu phẩy. Để trống cho tất cả các loại"

#: includes/fields/class-acf-field-file.php:276
#: includes/fields/class-acf-field-image.php:306
msgid "Allowed File Types"
msgstr "Loại tệp được phép"

#: includes/fields/class-acf-field-file.php:264
#: includes/fields/class-acf-field-image.php:270
msgid "Maximum"
msgstr "Tối đa"

#: includes/fields/class-acf-field-file.php:144
#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-file.php:268
#: includes/fields/class-acf-field-image.php:261
#: includes/fields/class-acf-field-image.php:297
msgid "File size"
msgstr "Kích thước tệp"

#: includes/fields/class-acf-field-image.php:235
#: includes/fields/class-acf-field-image.php:271
msgid "Restrict which images can be uploaded"
msgstr "Hạn chế hình ảnh nào có thể được tải lên"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:234
msgid "Minimum"
msgstr "Tối thiểu"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:200
msgid "Uploaded to post"
msgstr "Đã tải lên bài viết"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:199
#: includes/locations/class-acf-location-attachment.php:73
#: includes/locations/class-acf-location-comment.php:61
#: includes/locations/class-acf-location-nav-menu.php:74
#: includes/locations/class-acf-location-taxonomy.php:63
#: includes/locations/class-acf-location-user-form.php:71
#: includes/locations/class-acf-location-user-role.php:78
#: includes/locations/class-acf-location-widget.php:65
msgid "All"
msgstr "Tất cả"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:194
msgid "Limit the media library choice"
msgstr "Giới hạn lựa chọn thư viện phương tiện"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:193
msgid "Library"
msgstr "Thư viện"

#: includes/fields/class-acf-field-image.php:326
msgid "Preview Size"
msgstr "Kích thước xem trước"

#: includes/fields/class-acf-field-image.php:185
msgid "Image ID"
msgstr "ID Hình ảnh"

#: includes/fields/class-acf-field-image.php:184
msgid "Image URL"
msgstr "URL Hình ảnh"

#: includes/fields/class-acf-field-image.php:183
msgid "Image Array"
msgstr "Array hình ảnh"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-checkbox.php:343
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-link.php:147
#: includes/fields/class-acf-field-radio.php:203
msgid "Specify the returned value on front end"
msgstr "Chỉ định giá trị trả về ở phía trước"

#: includes/fields/class-acf-field-button-group.php:157
#: includes/fields/class-acf-field-checkbox.php:342
#: includes/fields/class-acf-field-file.php:199
#: includes/fields/class-acf-field-link.php:146
#: includes/fields/class-acf-field-radio.php:202
#: includes/fields/class-acf-field-taxonomy.php:672
msgid "Return Value"
msgstr "Giá trị trả về"

#: includes/fields/class-acf-field-image.php:155
msgid "Add Image"
msgstr "Thêm hình ảnh"

#: includes/fields/class-acf-field-image.php:155
msgid "No image selected"
msgstr "Không có hình ảnh được chọn"

#: includes/assets.php:353 includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:135
#: includes/fields/class-acf-field-link.php:124
msgid "Remove"
msgstr "Xóa"

#: includes/admin/views/acf-field-group/field.php:89
#: includes/fields/class-acf-field-file.php:150
#: includes/fields/class-acf-field-image.php:133
#: includes/fields/class-acf-field-link.php:124
msgid "Edit"
msgstr "Chỉnh sửa"

#: includes/fields/class-acf-field-image.php:63 includes/media.php:55
msgid "All images"
msgstr "Tất cả hình ảnh"

#: includes/fields/class-acf-field-image.php:62
msgid "Update Image"
msgstr "Cập nhật hình ảnh"

#: includes/fields/class-acf-field-image.php:61
msgid "Edit Image"
msgstr "Chỉnh sửa hình ảnh"

#: includes/fields/class-acf-field-image.php:60
msgid "Select Image"
msgstr "Chọn hình ảnh"

#: includes/fields/class-acf-field-image.php:22
msgid "Image"
msgstr "Hình ảnh"

#: includes/fields/class-acf-field-message.php:113
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Cho phép đánh dấu HTML hiển thị dưới dạng văn bản hiển thị thay vì hiển thị"

#: includes/fields/class-acf-field-message.php:112
msgid "Escape HTML"
msgstr "Escape HTML"

#: includes/fields/class-acf-field-message.php:104
#: includes/fields/class-acf-field-textarea.php:162
msgid "No Formatting"
msgstr "Không định dạng"

#: includes/fields/class-acf-field-message.php:103
#: includes/fields/class-acf-field-textarea.php:161
msgid "Automatically add &lt;br&gt;"
msgstr "Tự động thêm &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:102
#: includes/fields/class-acf-field-textarea.php:160
msgid "Automatically add paragraphs"
msgstr "Tự động thêm đoạn văn"

#: includes/fields/class-acf-field-message.php:98
#: includes/fields/class-acf-field-textarea.php:156
msgid "Controls how new lines are rendered"
msgstr "Điều khiển cách hiển thị các dòng mới"

#: includes/fields/class-acf-field-message.php:97
#: includes/fields/class-acf-field-textarea.php:155
msgid "New Lines"
msgstr "Dòng mới"

#: includes/fields/class-acf-field-date_picker.php:221
#: includes/fields/class-acf-field-date_time_picker.php:208
msgid "Week Starts On"
msgstr "Tuần bắt đầu vào"

#: includes/fields/class-acf-field-date_picker.php:190
msgid "The format used when saving a value"
msgstr "Định dạng được sử dụng khi lưu một giá trị"

#: includes/fields/class-acf-field-date_picker.php:189
msgid "Save Format"
msgstr "Định dạng lưu"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Tuần"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Trước"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Tiếp theo"

#: includes/fields/class-acf-field-date_picker.php:58
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Hôm nay"

#: includes/fields/class-acf-field-date_picker.php:57
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Hoàn tất"

#: includes/fields/class-acf-field-date_picker.php:22
msgid "Date Picker"
msgstr "Công cụ chọn ngày"

#: includes/fields/class-acf-field-image.php:238
#: includes/fields/class-acf-field-image.php:274
#: includes/fields/class-acf-field-oembed.php:241
msgid "Width"
msgstr "Chiều rộng"

#: includes/fields/class-acf-field-oembed.php:238
#: includes/fields/class-acf-field-oembed.php:250
msgid "Embed Size"
msgstr "Kích thước nhúng"

#: includes/fields/class-acf-field-oembed.php:198
msgid "Enter URL"
msgstr "Nhập URL"

#: includes/fields/class-acf-field-oembed.php:22
msgid "oEmbed"
msgstr "Nhúng"

#: includes/fields/class-acf-field-true_false.php:172
msgid "Text shown when inactive"
msgstr "Văn bản hiển thị khi không hoạt động"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Off Text"
msgstr "Văn bản tắt"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Văn bản hiển thị khi hoạt động"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Văn bản bật"

#: includes/fields/class-acf-field-select.php:420
#: includes/fields/class-acf-field-true_false.php:187
msgid "Stylized UI"
msgstr "Giao diện người dùng được tạo kiểu"

#: includes/fields/class-acf-field-button-group.php:147
#: includes/fields/class-acf-field-checkbox.php:332
#: includes/fields/class-acf-field-color_picker.php:144
#: includes/fields/class-acf-field-email.php:103
#: includes/fields/class-acf-field-number.php:113
#: includes/fields/class-acf-field-radio.php:192
#: includes/fields/class-acf-field-range.php:153
#: includes/fields/class-acf-field-select.php:351
#: includes/fields/class-acf-field-text.php:95
#: includes/fields/class-acf-field-textarea.php:93
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:84
#: includes/fields/class-acf-field-wysiwyg.php:291
msgid "Default Value"
msgstr "Giá trị mặc định"

#: includes/fields/class-acf-field-true_false.php:126
msgid "Displays text alongside the checkbox"
msgstr "Hiển thị văn bản cùng với hộp kiểm"

#: includes/fields/class-acf-field-message.php:23
#: includes/fields/class-acf-field-message.php:87
#: includes/fields/class-acf-field-true_false.php:125
msgid "Message"
msgstr "Hiển thị thông điệp"

#: includes/assets.php:352 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:175
#: src/Site_Health/Site_Health.php:281 src/Site_Health/Site_Health.php:343
msgid "No"
msgstr "Không"

#: includes/assets.php:351 includes/fields/class-acf-field-true_false.php:76
#: includes/fields/class-acf-field-true_false.php:159
#: src/Site_Health/Site_Health.php:280 src/Site_Health/Site_Health.php:343
msgid "Yes"
msgstr "Có"

#: includes/fields/class-acf-field-true_false.php:22
msgid "True / False"
msgstr "Đúng / Sai"

#: includes/fields/class-acf-field-group.php:415
msgid "Row"
msgstr "Hàng"

#: includes/fields/class-acf-field-group.php:414
msgid "Table"
msgstr "Bảng"

#: includes/admin/post-types/admin-field-group.php:158
#: includes/fields/class-acf-field-group.php:413
msgid "Block"
msgstr "Khối"

#: includes/fields/class-acf-field-group.php:408
msgid "Specify the style used to render the selected fields"
msgstr "Chỉ định kiểu được sử dụng để hiển thị các trường đã chọn"

#: includes/fields.php:330 includes/fields/class-acf-field-button-group.php:204
#: includes/fields/class-acf-field-checkbox.php:405
#: includes/fields/class-acf-field-group.php:407
#: includes/fields/class-acf-field-radio.php:276
msgid "Layout"
msgstr "Bố cục"

#: includes/fields/class-acf-field-group.php:391
msgid "Sub Fields"
msgstr "Các trường phụ"

#: includes/fields/class-acf-field-group.php:22
msgid "Group"
msgstr "Nhóm"

#: includes/fields/class-acf-field-google-map.php:222
msgid "Customize the map height"
msgstr "Tùy chỉnh chiều cao bản đồ"

#: includes/fields/class-acf-field-google-map.php:221
#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:253
msgid "Height"
msgstr "Chiều cao"

#: includes/fields/class-acf-field-google-map.php:210
msgid "Set the initial zoom level"
msgstr "Đặt mức zoom ban đầu"

#: includes/fields/class-acf-field-google-map.php:209
msgid "Zoom"
msgstr "Phóng to"

#: includes/fields/class-acf-field-google-map.php:183
#: includes/fields/class-acf-field-google-map.php:196
msgid "Center the initial map"
msgstr "Trung tâm bản đồ ban đầu"

#: includes/fields/class-acf-field-google-map.php:182
#: includes/fields/class-acf-field-google-map.php:195
msgid "Center"
msgstr "Trung tâm"

#: includes/fields/class-acf-field-google-map.php:154
msgid "Search for address..."
msgstr "Tìm kiếm địa chỉ..."

#: includes/fields/class-acf-field-google-map.php:151
msgid "Find current location"
msgstr "Tìm vị trí hiện tại"

#: includes/fields/class-acf-field-google-map.php:150
msgid "Clear location"
msgstr "Xóa vị trí"

#: includes/fields/class-acf-field-google-map.php:149
#: includes/fields/class-acf-field-relationship.php:600
msgid "Search"
msgstr "Tìm kiếm"

#: includes/fields/class-acf-field-google-map.php:57
msgid "Sorry, this browser does not support geolocation"
msgstr "Xin lỗi, trình duyệt này không hỗ trợ định vị"

#: includes/fields/class-acf-field-google-map.php:22
msgid "Google Map"
msgstr "Bản đồ Google"

#: includes/fields/class-acf-field-date_picker.php:201
#: includes/fields/class-acf-field-date_time_picker.php:189
#: includes/fields/class-acf-field-time_picker.php:122
msgid "The format returned via template functions"
msgstr "Định dạng được trả về qua các hàm mẫu"

#: includes/fields/class-acf-field-color_picker.php:168
#: includes/fields/class-acf-field-date_picker.php:200
#: includes/fields/class-acf-field-date_time_picker.php:188
#: includes/fields/class-acf-field-icon_picker.php:285
#: includes/fields/class-acf-field-image.php:177
#: includes/fields/class-acf-field-post_object.php:396
#: includes/fields/class-acf-field-relationship.php:610
#: includes/fields/class-acf-field-select.php:362
#: includes/fields/class-acf-field-time_picker.php:121
#: includes/fields/class-acf-field-user.php:95
msgid "Return Format"
msgstr "Định dạng trả về"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_picker.php:210
#: includes/fields/class-acf-field-date_time_picker.php:180
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:113
#: includes/fields/class-acf-field-time_picker.php:129
msgid "Custom:"
msgstr "Tùy chỉnh:"

#: includes/fields/class-acf-field-date_picker.php:171
#: includes/fields/class-acf-field-date_time_picker.php:171
#: includes/fields/class-acf-field-time_picker.php:106
msgid "The format displayed when editing a post"
msgstr "Định dạng hiển thị khi chỉnh sửa bài viết"

#: includes/fields/class-acf-field-date_picker.php:170
#: includes/fields/class-acf-field-date_time_picker.php:170
#: includes/fields/class-acf-field-time_picker.php:105
msgid "Display Format"
msgstr "Định dạng hiển thị"

#: includes/fields/class-acf-field-time_picker.php:22
msgid "Time Picker"
msgstr "Công cụ chọn thời gian"

#. translators: counts for inactive field groups
#: acf.php:526
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "<span class=\"count\">(%s)</span> không hoạt động"

#: acf.php:487
msgid "No Fields found in Trash"
msgstr "Không tìm thấy trường nào trong thùng rác"

#: acf.php:486
msgid "No Fields found"
msgstr "Không tìm thấy trường nào"

#: acf.php:485
msgid "Search Fields"
msgstr "Tìm kiếm các trường"

#: acf.php:484
msgid "View Field"
msgstr "Xem trường"

#: acf.php:483 includes/admin/views/acf-field-group/fields.php:113
msgid "New Field"
msgstr "Trường mới"

#: acf.php:482
msgid "Edit Field"
msgstr "Chỉnh sửa trường"

#: acf.php:481
msgid "Add New Field"
msgstr "Thêm trường mới"

#: acf.php:479
msgid "Field"
msgstr "Trường"

#: acf.php:478 includes/admin/post-types/admin-field-group.php:179
#: includes/admin/post-types/admin-field-groups.php:93
#: includes/admin/views/acf-field-group/fields.php:32
msgid "Fields"
msgstr "Các trường"

#: acf.php:453
msgid "No Field Groups found in Trash"
msgstr "Không tìm thấy nhóm trường nào trong thùng rác"

#: acf.php:452
msgid "No Field Groups found"
msgstr "Không tìm thấy nhóm trường nào"

#: acf.php:451
msgid "Search Field Groups"
msgstr "Tìm kiếm nhóm trường"

#: acf.php:450
msgid "View Field Group"
msgstr "Xem nhóm trường"

#: acf.php:449
msgid "New Field Group"
msgstr "Nhóm trường mới"

#: acf.php:448
msgid "Edit Field Group"
msgstr "Chỉnh sửa nhóm trường"

#: acf.php:447
msgid "Add New Field Group"
msgstr "Thêm nhóm trường mới"

#: acf.php:446 acf.php:480
#: includes/admin/views/acf-post-type/advanced-settings.php:224
#: includes/post-types/class-acf-post-type.php:93
#: includes/post-types/class-acf-taxonomy.php:92
msgid "Add New"
msgstr "Thêm mới"

#: acf.php:445
msgid "Field Group"
msgstr "Nhóm trường"

#: acf.php:444 includes/admin/post-types/admin-field-groups.php:55
#: includes/admin/post-types/admin-post-types.php:113
#: includes/admin/post-types/admin-taxonomies.php:112
msgid "Field Groups"
msgstr "Nhóm trường"

#. Description of the plugin
#: acf.php
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr ""
"Tùy chỉnh WordPress với các trường mạnh mẽ, chuyên nghiệp và trực quan."

#. Plugin URI of the plugin
#: acf.php
msgid "https://www.advancedcustomfields.com"
msgstr "https://www.advancedcustomfields.com"

#. Plugin Name of the plugin
#: acf.php acf.php:290
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"
