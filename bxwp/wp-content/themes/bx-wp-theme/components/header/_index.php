<?php

if (!defined('ABSPATH')) {
   exit;
}

$header_classes = is_user_logged_in() ? ' bg-primary-500 text-white' : ' bg-white text-primary-500';

?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>

<head>
   <meta charset="<?php bloginfo('charset'); ?>">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <?php wp_head(); ?>
</head>

<body <?php body_class('text-primary-500 min-h-screen'); ?>>
   <?php wp_body_open(); ?>

   <input type="checkbox" id="mobile-menu-toggle" class="hidden">

   <header id="main-header" class="flex flex-col items-center border-b border-neutral-400 <?php echo esc_attr($header_classes); ?>">

      <div class="container h-24 gap-5 flex justify-between items-center">

         <nav class="w-1/4 show-on-login-page">
            <a href="<?php echo home_url(); ?>" class="flex items-center gap-1 text-xs hover:no-underline">
               <span class="dashicons dashicons-arrow-left-alt2"></span>
               <?php esc_html_e('Voltar para a home', 'bx-obastidor'); ?>
            </a>
         </nav>

         <?php get_component('logo'); ?>

         <div class="w-1/4 show-on-login-page"></div>

         <nav class="gap-5 hidden lg:flex hide-on-login-page">
            <?php

            if (has_nav_menu('primary')) {
               wp_nav_menu(
                  [
                     'theme_location' => 'primary',
                     'menu_class'     => 'font-secondary flex gap-6 uppercase',
                     'container'      => false,
                  ]
               );
            }

            ?>
         </nav>
         <nav class="flex gap-6 items-center hide-on-login-page">
            <?php get_component(['header', 'search-form']); ?>

            <ul class="flex lg:hidden gap-6 items-center">
               <li>
                  <label for="mobile-menu-toggle">
                     <?php

                     render_svg('mobile-menu', 'mobile-menu-open h-6');
                     render_svg('mobile-close', 'mobile-menu-close h-6');

                     ?>
                  </label>
               </li>
            </ul>
            <?php

            get_component('meu-bastidor-menu-header', [
               'add_menu_classes' => 'hidden',
            ]);

            ?>

         </nav>

      </div>

      <?php get_component(['header', 'under-censure']); ?>

   </header>

   <nav id="mobile-menu" class="absolute flex gap-7 flex-col p-6 w-[100vw] bg-neutral-300 font-secondary overflow-y-auto" x-data="headerHeight" x-init="updateHeaderHeight()" @resize.window="updateHeaderHeight()" x-bind:style="getHeaderHeightStyle()">
      <div>
         <?php

         get_component('meu-bastidor-menu-header', [
            'add_menu_classes' => 'uppercase',
         ]);

         if (has_nav_menu('mobile')) {
            wp_nav_menu(
               [
                  'theme_location' => 'mobile',
                  'menu_class'     => 'mobile-menu uppercase',
                  'add_item_class' => 'uppercase py-2 border-t border-neutral-400',
                  'container'      => false,
               ]
            );
         }

         ?>
      </div>
      <?php

      get_component('most-readed-widget', [
         'container_classes' => 'mb-16'
      ]);

      get_component('ad-banner', [
         'slot'              => 'home-maislidas-after',
         'container_classes' => 'mb-16'
      ]);

      ?>
   </nav>
