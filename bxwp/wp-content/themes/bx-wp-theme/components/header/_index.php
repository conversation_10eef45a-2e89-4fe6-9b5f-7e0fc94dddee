<?php

if (!defined('ABSPATH')) {
   exit;
}

$header_classes = is_user_logged_in() ? 'bg-primary-500 text-white' : 'bg-white';

?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>

<head>
   <meta charset="<?php bloginfo('charset'); ?>">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <?php wp_head(); ?>
</head>

<body <?php body_class('text-primary-500 font-primary min-h-screen'); ?>>
   <?php wp_body_open(); ?>

   <input type="checkbox" id="mobile-menu-toggle" class="hidden">

   <header id="main-header" class="flex flex-col items-center <?php esc_attr_e($header_classes); ?> border-b border-neutral-400 text-primary-500">

      <div class="container h-24 gap-5 flex justify-between items-center">

         <?php get_component('logo'); ?>

         <nav class=" gap-5 hidden md:flex">
            <?php

            if (has_nav_menu('primary')) {
               wp_nav_menu(
                  [
                     'theme_location' => 'primary',
                     'menu_class'     => 'font-secondary flex gap-6 uppercase',
                     'container'      => false,
                  ]
               );
            }

            ?>
         </nav>
         <nav class="flex gap-6 items-center">
            <ul class="flex gap-6">
               <li>
                  <a href="#search">
                     <?php render_svg('search', 'h-6'); ?>
                  </a>
               </li>
            </ul>
            <ul class="flex md:hidden gap-6 items-center">
               <li>
                  <label for="mobile-menu-toggle">
                     <?php

                     render_svg('mobile-menu', 'mobile-menu-open h-6');
                     render_svg('mobile-close', 'mobile-menu-close h-6');

                     ?>
                  </label>
               </li>
            </ul>
            <?php

            get_component('meu-bastidor-menu-header', [
               'add_menu_classes' => 'hidden',
            ]);

            ?>

         </nav>

      </div>

      <?php get_component(['header', 'under-censure']); ?>

   </header>

   <nav id="mobile-menu" class="absolute flex flex-col p-6 w-[100vw] h-[100vh] bg-neutral-300 font-secondary">
      <?php

      get_component('meu-bastidor-menu-header', [
         'add_menu_classes' => 'uppercase',
      ]);

      if (has_nav_menu('mobile')) {
         wp_nav_menu(
            [
               'theme_location' => 'mobile',
               'menu_class'     => 'mobile-menu uppercase',
               'add_item_class' => 'uppercase py-2 border-t border-neutral-400',
               'container'      => false,
            ]
         );
      }

      get_component('most-readed');

      ?>
   </nav>

   <!-- <nav class="flex w-full bg-white">
      <ul class="container pt-2">
         <li>
            <a class="opacity-70" href="<?php echo home_url(); ?>">
               <?php esc_html_e('Destaques', 'bx-wp-theme'); ?>
            </a>
         </li>
      </ul>
   </nav> -->
