<?php

if (!defined('ABSPATH')) {
   exit;
}

?>

<div class="inline-block" x-data="{ open: false }" x-effect="document.body.classList.toggle('overflow-hidden', open)">
   <button type="button" class="flex items-center hover:text-highlight-500" aria-label="Buscar" x-on:click="open = true; $nextTick(() => document.getElementById('search-input')?.focus())">
      <?php render_svg('search', 'h-6'); ?>
   </button>
   <div class="fixed left-0 w-full z-50 flex items-start justify-center bg-white bg-transparent" x-transition:enter="transition-transform ease-out duration-300" x-transition:enter-start="-translate-x-full" x-transition:enter-end="translate-x-0" x-transition:leave="transition-transform ease-in duration-300" x-transition:leave-start="translate-x-0" x-transition:leave-end="-translate-x-full" x-show="open" x-on:keydown.escape.window="open = false" x-data="headerHeight" x-init="open && updateHeaderHeight()" @resize.window="open && updateHeaderHeight()" x-bind:style="`top: ${headerHeight}px; height: calc(100vh - ${headerHeight}px);`" x-cloak>
      <form role="search" method="get" class="container mt-10" action="<?php echo esc_url(home_url('/')); ?>" x-on:click.away="open = false">
         <input type="search" id="search-input" class="w-full text-primary-500 text-[2rem] lg:text-[2.6rem] px-6 py-4 border-b-2 focus:outline-none" placeholder="<?php esc_attr_e('Pesquisar por:', 'bx-obastidor'); ?>" value="<?php echo get_search_query(); ?>" name="s" autocomplete="off" />
      </form>
   </div>
</div>
