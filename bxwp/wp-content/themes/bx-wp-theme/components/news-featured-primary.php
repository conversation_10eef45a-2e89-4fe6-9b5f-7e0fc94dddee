<?php

if (!defined('ABSPATH')) {
   exit;
}

use BX_Obastidor\BX_News\BX_News;
use BX_Obastidor\BX_SponsoredContent\BX_SponsoredContent;
use BX_Obastidor\BX_News\Utils;

$posts = Utils::get_featured_posts('principal', ['posts_per_page' => 1]);

foreach ($posts as $post) {
   $Single = get_post_type($post) === 'sponsored_content'
      ? new BX_SponsoredContent($post)
      : new BX_News($post);

?>
   <li>
      <article class="w-full">
         <?php

         if ($Single->has_label()) {

         ?>
            <header class="mt-2 mb-4">
               <?php get_component('news-label', ['post_object' => $Single]); ?>
            </header>
         <?php

         }

         ?>
         <a href="<?php echo esc_url($Single->get_link()); ?>" class="block font-bold text-4xl md:text-[42px]">
            <?php

            echo esc_html($Single->get_title());

            if ($Single->has_thumbnail()) {

            ?>
               <figure class="p-2 border border-double border-neutral-300 mt-4">
                  <?php echo $Single->get_thumbnail('large', ['class' => 'aspect-video w-full object-cover']); ?>
               </figure>
            <?php

            }

            ?>
         </a>

      </article>
   </li>

<?php

}
