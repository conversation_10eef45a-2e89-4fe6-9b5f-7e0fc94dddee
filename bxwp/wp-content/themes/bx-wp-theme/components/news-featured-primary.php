<?php

if (!defined('ABSPATH')) {
   exit;
}

use BX_Obastidor\News\News;
use BX_Obastidor\News\Utils;

$posts = Utils::get_news_posts([
   'posts_per_page' => 1,
   'tax_query'      => [
      [
         'taxonomy' => 'featured-news-order',
         'field'    => 'slug',
         'terms'    => ['principal'],
      ]
   ],
]);

foreach ($posts as $post) {
   $Single = new News($post);

?>
   <li>
      <article class="w-full">
         <?php

         if ($Single->has_label()) {

         ?>
            <header class="mt-2 mb-4">
               <?php get_component('news-label', ['post_object' => $Single]); ?>
            </header>
         <?php

         }

         ?>
         <a href="<?php echo esc_url($Single->get_link()); ?>" class="block font-primary font-bold text-4xl md:text-[42px]">
            <?php

            echo esc_html($Single->get_title());

            if ($Single->has_thumbnail()) {

            ?>
               <figure class="p-2 border border-double border-neutral-300 mt-4">
                  <?php echo $Single->get_thumbnail('large', ['class' => 'aspect-video w-full object-cover']); ?>
               </figure>
            <?php

            }

            ?>
         </a>

      </article>
   </li>

<?php

}
