<?php

if (!defined('ABSPATH')) {
   exit;
}

$News = $args['post_object'] ?? null;

if (empty($News) || !is_object($News)) {
   return;
}

?>
<li class="border-b border-neutral-300 pb-4 last:border-b-0">
   <?php

   if ($News->get_post_type() === 'ad_banner') {

   ?>
      <div class="mt-4">
         <?php

         get_component('ad-banner', ['post_object' => $News]);

         ?>
      </div>
   <?php

   } else {

   ?>
      <article>
         <header class="mt-4">
            <?php

            if ($News->has_label()) {

            ?>
               <div class="mb-4">
                  <?php get_component('news-label', ['post_object' => $News]); ?>
               </div>
            <?php

            }

            ?>
            <a href="<?php echo esc_url($News->get_link()); ?>" class="block font-primary text-2xl font-bold">
               <?php echo esc_html($News->get_title()); ?>
            </a>
            <span class="inline-block font-mono text-xs text-primary-300 mb-1">
               <?php echo esc_html($News->get_date('d/m/Y à\s H:i')); ?>
            </span>
         </header>
         <div class="flex flex-col md:flex-row gap-8 mt-4">
            <?php

            if ($News->has_thumbnail()) {

            ?>
               <a href="<?php echo esc_url($News->get_link()); ?>">
                  <figure class="flex flex-col md:h-48 aspect-[3/2] justify-center items-center overflow-hidden p-2 border border-double border-neutral-300">
                     <?php echo $News->get_thumbnail('medium', ['class' => 'w-full h-full object-cover']); ?>
                  </figure>
               </a>
            <?php

            }

            ?>
            <div class="w-2/3 flex flex-col justify-between">
               <p class="mb-8"><?php echo esc_html($News->get_excerpt(20)); ?></p>
               <p class="font-mono uppercase text-xs font-bold">
                  <a href="<?php echo esc_url($News->get_link()); ?>" class="flex items-center gap-2">
                     <span><?php esc_html_e('Leia mais', 'bx-obastidor'); ?></span>
                     <?php render_svg('arrow-right', 'h-6 inline-block'); ?>
                  </a>
               </p>
            </div>
         </div>
      </article>
   <?php

   }

   ?>
</li>
