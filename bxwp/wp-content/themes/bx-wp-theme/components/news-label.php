<?php

if (!defined('ABSPATH')) {
   exit;
}

$News = $args['post_object'] ?? null;

if (empty($News)) {
   return;
}

$label = $News->get_label();

if (empty($label)) {
   return;
}

$label_link = $News->get_label_link();

$base_classes = 'inline-block font-mono font-medium text-xs mb-1 uppercase select-none';

if ($News->is_exclusive()) {
   $label_class = 'bg-alert-500 text-white px-2 py-1';
} elseif ($News->get_post_type() === 'sponsored_content') {
   $label_class = 'text-highlight-500';
} else {
   $label_class = 'text-primary-300';
}

$classes = $base_classes . ' ' . $label_class;

if ($label_link) {

?>
   <a href="<?php echo esc_url($label_link); ?>" class="font-mono font-medium text-xs mb-1 uppercase select-none">
      <span class="<?php echo esc_attr($classes); ?>">
         <?php echo esc_html($label); ?>
      </span>
   </a>
<?php

} else {

?>
   <span class="<?php echo esc_attr($classes); ?>">
      <?php echo esc_html($label); ?>
   </span>
<?php

}
