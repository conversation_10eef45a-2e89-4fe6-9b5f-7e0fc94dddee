<?php

if (!defined('ABSPATH')) {
   exit;
}

$News = $args['post_object'] ?? null;

if (empty($News) || !is_object($News)) {
   return;
}

if ($News->is_exclusive()) {

?>
   <span class="inline-block font-mono font-medium text-xs text-white bg-alert-500 px-2 py-1 mb-1 uppercase select-none">
      <?php esc_html_e('Exclusivo', 'bx-obastidor'); ?>
   </span>
<?php

} elseif ($News->get_post_type() === 'sponsored_content') {

?>
   <span class="inline-block font-mono font-medium text-xs text-highlight-500 mb-1 uppercase select-none">
      <?php echo esc_html($News->get_sponsor_name()); ?>
   </span>
<?php

} elseif ($Category = $News->get_category()) {

?>
   <a href="<?php echo esc_url($Category->get_link()); ?>" class="font-mono font-medium text-xs text-primary-300 mb-1 uppercase select-none">
      <?php echo esc_html($Category->get_name()); ?>
   </a>
<?php

}
