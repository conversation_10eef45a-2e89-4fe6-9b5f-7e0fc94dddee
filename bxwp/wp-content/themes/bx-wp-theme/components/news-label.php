<?php

if (!defined('ABSPATH')) {
   exit;
}

$News = $args['post_object'] ?? null;

$label = empty($News) ? '' : $News->get_label();

if (empty($label)) {
   return;
}

$label_link = $News->get_label_link();

if ($label_link) {
   ?>
   <a href="<?php echo esc_url($label_link); ?>" class="font-mono font-medium text-xs text-primary-300 mb-1 uppercase select-none">
   <?php
}
   ?>
      <span class="inline-block font-mono font-medium text-xs text-highlight-500 mb-1 uppercase select-none<?php echo $News->is_exclusive() ? ' bg-alert-500 text-white px-2 py-1' : ''; ?>">
         <?php echo esc_html($label); ?>
      </span>
   <?php

if ($label_link) {
   ?>
   </a>
   <?php
}
