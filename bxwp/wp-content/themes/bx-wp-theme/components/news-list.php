<?php

if (!defined('ABSPATH')) {
   exit;
}

use BX_Obastidor\News\News;
use BX_Obastidor\News\Utils;
use BX_Obastidor\SponsoredContent\SponsoredContent;
use BX_Obastidor\AdBanner\AdBanner;

$query_args = [
   'paged'          => max(1, get_query_var('paged')),
   'posts_per_page' => get_option('posts_per_page', 15)
];

if (isset($args['exclude_ids'])) {
   $query_args['post__not_in'] = $args['exclude_ids'];
}

$query_info = []; // passed by reference will store query info
$posts = Utils::get_not_featured_posts($query_args, $query_info);

?>

<ul id="news-container">
   <?php

   foreach ($posts as $post) {
      $post_type = get_post_type($post);

      if ($post_type === 'ad_banner') {
         $post_object = new AdBanner($post);
      } elseif ($post_type === 'sponsored_content') {
         $post_object = new SponsoredContent($post);
      } else {
         $post_object = new News($post);
      }

      get_component('news-list-item', ['post_object' => $post_object]);
   }

   ?>
</ul>

<?php

if ($query_info['paged'] < $query_info['max_num_pages']) {
   $query_args = $query_info;
   $query_args['paged'] = $query_info['paged'] + 1;

?>
   <button type="button" id="load-more-news" class="btn-primary mt-4" data-query-args="<?php echo esc_attr(json_encode($query_args)); ?>">
      <span class="button-text"><?php esc_html_e('Veja mais', 'bx-obastidor'); ?></span>
      <span class="loading-text hidden"><?php esc_html_e('Carregando...', 'bx-obastidor'); ?></span>
   </button>
<?php

}
