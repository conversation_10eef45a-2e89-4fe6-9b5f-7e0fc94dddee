<?php

if (!defined('ABSPATH')) {
   exit;
}

use BX_Obastidor\BX_News\BX_News;
use BX_Obastidor\BX_News\Utils;
use BX_Obastidor\BX_SponsoredContent\BX_SponsoredContent;
use BX_Obastidor\BX_AdBanner\BX_AdBanner;

$query_args = [
   'paged' => max(1, get_query_var('paged')),
   'posts_per_page' => 15
];

if (isset($args['exclude_ids'])) {
   $query_args['post__not_in'] = $args['exclude_ids'];
}

$query_info = []; // passed by reference will store query info
$posts = Utils::get_not_featured_posts($query_args, $query_info);

?>

<ul id="news-container">
   <?php

   foreach ($posts as $post) {

      get_component(
         'news-list-item',
         [
            'post_object' => get_post_type($post) === 'ad_banner'
               ? new BX_AdBanner($post)
               : (get_post_type($post) === 'sponsored_content'
                  ? new BX_SponsoredContent($post)
                  : new BX_News($post))
         ]
      );
   }

   ?>
</ul>

<?php

if ($query_info['paged'] < $query_info['max_num_pages']) {
   $query_args = $query_info;
   $query_args['paged'] = $query_info['paged'] + 1;

?>
   <button type="button" id="load-more-news" class="mt-4 font-bold text-sm uppercase tracking-wide bg-primary-500 text-white rounded-sm px-4 py-2 inline-block select-none transition-all duration-100 ease-linear hover:bg-primary-500/80" data-query-args="<?php echo esc_attr(json_encode($query_args)); ?>">
      <span class="button-text"><?php esc_html_e('Veja mais', 'bx-obastidor'); ?></span>
      <span class="loading-text hidden"><?php esc_html_e('Carregando...', 'bx-obastidor'); ?></span>
   </button>
<?php

}
