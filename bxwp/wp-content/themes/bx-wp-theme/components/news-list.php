<?php

if (!defined('ABSPATH')) {
   exit;
}

use BX_Obastidor\News\News;
use BX_Obastidor\News\Utils;

$query_args = $args['query_args'] ?? [];

$query_info = []; // will receive query_vars
$posts = Utils::get_news_posts($query_args, $query_info);

?>

<ul id="news-container">
   <?php

   if (!empty($posts)) {
      foreach ($posts as $post) {
         get_component('news-list-item', ['post_object' => new News($post)]);
      }
   } else {

   ?>
      <li><?php esc_html_e('Nenhuma notícia encontrada', 'bx-obastidor'); ?></li>
   <?php

   }

   ?>
</ul>

<?php

if ($query_info['paged'] < $query_info['max_num_pages']) {
   $query_info['paged'] = $query_info['paged'] + 1;

?>
   <button type="button" id="load-more-news" class="btn-primary mt-4" data-query-args="<?php echo esc_attr(json_encode($query_info)); ?>">
      <span class="button-text"><?php esc_html_e('Veja mais', 'bx-obastidor'); ?></span>
      <span class="loading-text hidden"><?php esc_html_e('Carregando...', 'bx-obastidor'); ?></span>
   </button>
<?php

}
