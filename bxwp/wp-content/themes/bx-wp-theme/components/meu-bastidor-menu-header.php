<?php

if (!defined('ABSPATH')) {
   exit;
}

$add_menu_classes = $args['add_menu_classes'] ?? '';

?>
<ul class="uppercase font-secondary text-highlight-500 md:flex <?php esc_attr_e($add_menu_classes); ?>">
   <li>
      <?php

      if (is_user_logged_in()) {

      ?>
         <a class="flex gap-2 items-center" href="<?php echo esc_url(home_url('meu-bastidor')); ?>">
            <?php

            render_svg('user', 'h-8');
            esc_html_e('Meu Bastidor', 'bx-bastidor');

            ?>
         </a>
      <?php

      } else {

      ?>
         <a class="font-secondary flex gap-2 items-center" href="<?php echo esc_url(home_url('meu-bastidor/login')); ?>">
            <?php

            render_svg('user', 'h-8');
            esc_html_e('Acessar Conta', 'bx-bastidor');

            ?>
         </a>
      <?php

      }

      ?>
   </li>
</ul>
