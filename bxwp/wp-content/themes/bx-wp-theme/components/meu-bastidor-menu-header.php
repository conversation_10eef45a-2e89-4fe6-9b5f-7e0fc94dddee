<?php

if (!defined('ABSPATH')) {
   exit;
}

$add_menu_classes = $args['add_menu_classes'] ?? '';

?>
<ul class="uppercase font-secondary text-highlight-500 lg:flex show-on-hover-detection <?php esc_attr_e($add_menu_classes); ?>">
   <li class="relative">
      <?php

      if (is_user_logged_in()) {

         $current_user = wp_get_current_user();

      ?>
         <a class="flex gap-2 items-center" href="<?php echo esc_url(home_url('meu-bastidor')); ?>" title="<?php echo esc_attr($current_user->user_email); ?>">
            <?php

            $first_name = $current_user->first_name;
            $greeting   = $first_name
               ? sprintf(__('Olá, %s', 'bx-bastidor'), $first_name)
               :  __('Meu Bastidor', 'bx-bastidor');

            render_svg('user', 'h-8');
            esc_html_e($greeting, 'bx-bastidor');

            ?>
         </a>

         <ul class="show-on-hover absolute w-56 left-0 top-full bg-white shadow-md rounded-md py-6 px-4">
            <li>
               <a class="flex items-center text-primary-500 normal-case py-2" href="<?php echo esc_url(home_url('meu-bastidor')); ?>">
                  <?php

                  render_svg('reading', 'h-4 mr-2');
                  esc_html_e('Meu Bastidor', 'bx-bastidor');

                  ?>
               </a>
            </li>
            <li>
               <a class="flex items-center text-primary-500 normal-case py-2" href="<?php echo esc_url(home_url('meu-bastidor/minhas-preferencias')); ?>">
                  <?php

                  render_svg('gear', 'h-4 mr-2');
                  esc_html_e('Minhas Preferências', 'bx-bastidor');

                  ?>
               </a>
            </li>
            <li class="border-t text-primary-500 border-neutral-400 normal-case mt-4 pt-6 pb-2">
               <a href="<?php echo wp_logout_url(get_queried_object_id() ? get_permalink() : home_url()); ?>">
                  <?php esc_html_e('Sair', 'bx-bastidor'); ?>
               </a>
            </li>
         </ul>
      <?php

      } else {

      ?>
         <a class="font-secondary flex gap-2 items-center hover:no-underline" href="<?php echo esc_url(home_url('meu-bastidor/login')); ?>">
            <?php

            render_svg('user', 'h-8');
            esc_html_e('Acessar Conta', 'bx-bastidor');

            ?>
         </a>
      <?php

      }

      ?>
   </li>
</ul>
