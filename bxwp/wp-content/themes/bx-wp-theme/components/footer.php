<?php

if (!defined('ABSPATH')) {
   exit;
}

do_action('get_footer', null, []);

?>
<footer class="main-footer bg-neutral-300 py-10">

   <section class="container flex justify-between py-4 border-b border-neutral-400">

      <figure class="footer-logo">
         <?php render_svg('icon', 'w-10 h-10'); ?>
      </figure>

   </section>

   <section class="container flex gap-4 md:flex-row flex-col justify-start py-4 border-neutral-400">
      <?php

      if (has_nav_menu('footer-1')) {

      ?>
         <nav class="font-primary md:w-[25%]">
            <h2 class="text-lg font-bold mb-4"><?php esc_attr_e('O Bastidor', 'bx-bastidor'); ?></h2>
            <?php

            wp_nav_menu(
               [
                  'theme_location' => 'footer-1',
                  'menu_class'     => 'font-secondary',
                  'container'      => false,
               ]
            );

            ?>
         </nav>
      <?php

      }

      if (has_nav_menu('footer-2')) {

      ?>
         <nav class="font-primary md:w-[25%]">
            <h2 class="text-lg font-bold mb-4"><?php esc_attr_e('Editorias', 'bx-bastidor'); ?></h2>
            <?php

            wp_nav_menu(
               [
                  'theme_location' => 'footer-2',
                  'menu_class'     => 'font-secondary',
                  'container'      => false,
               ]
            );

            ?>
         </nav>
      <?php

      }

      ?>
         <nav class="font-primary md:w-[25%]">
            <h2 class="text-lg font-bold mb-4"><?php esc_attr_e('Meu bastidor', 'bx-bastidor'); ?></h2>
            <?php get_component('meu-bastidor-menu-footer'); ?>
         </nav>

   </section>

</footer>
<?php

wp_footer();

?>
</body>

</html>
