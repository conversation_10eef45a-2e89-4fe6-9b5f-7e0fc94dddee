<?php

if (!defined('ABSPATH')) {
   exit;
}

use BX_Obastidor\News\News;
use BX_Obastidor\News\Utils;

$most_readed_posts = Utils::get_most_readed_posts(5);

if (empty($most_readed_posts)) {
   return;
}

$container_classes = $args['container_classes'] ?? '';

?>
<section class="<?php echo esc_attr($container_classes); ?>">
   <h2 class="font-secondary font-bold text-base uppercase underline underline-offset-[0.35rem] decoration-2 tracking-wider"><?php esc_html_e('Mais Lidas', 'bx-obastidor'); ?></h2>
   <ol>
      <?php

      foreach ($most_readed_posts as $index => $post_stats) {
         $News = new News($post_stats['post_ID']);

      ?>
         <li class="my-8">
            <article>
               <a href="<?php echo esc_url($News->get_link()); ?>" class="font-primary font-bold text-lg text-primary-500 my-2 !no-underline block group">
                  <span class="flex h-[1.8em] w-[1.8em] border-[1px] border-solid border-primary items-center justify-center font-secondary text-sm mb-4 group-hover:bg-primary-500 group-hover:text-white"><?php echo $index + 1; ?></span>
                  <span class="block group-hover:underline-highlight"><?php echo $News->get_title(); ?></span>
               </a>
            </article>
         </li>
      <?php

      }

      ?>
   </ol>
</section>
