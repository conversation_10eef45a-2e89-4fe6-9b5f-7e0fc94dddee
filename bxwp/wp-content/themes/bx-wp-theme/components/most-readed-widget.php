<?php

if (!defined('ABSPATH')) {
   exit;
}

use BX_Obastidor\BX_News\BX_News;
use BX_Obastidor\BX_News\Utils;

$most_readed_posts = Utils::get_most_readed_posts(5);

if (empty($most_readed_posts)) {
   return;
}

?>
<section>
   <h2 class="font-secondary font-bold text-base uppercase underline underline-offset-[0.35rem] decoration-2 tracking-wider"><?php esc_html_e('Mais Lidas', 'bx-obastidor'); ?></h2>
   <ol>
      <?php

      foreach ($most_readed_posts as $index => $post_stats) {
         $News  = new BX_News($post_stats['post_ID']);
         $views = $post_stats['views_count'];

      ?>
         <li class="my-8">
            <article>
               <a href="<?php echo esc_url($News->get_link()); ?>" title="<?php printf('%d %s', $views, _n('visualização', 'visualizações', $views, 'bx-obastidor')); ?>" class="font-primary font-bold text-lg text-primary-500 my-2 !no-underline block">
                  <span class="flex h-[1.8em] w-[1.8em] border-[1px] border-solid border-primary items-center justify-center font-secondary text-sm mb-4"><?php echo $index + 1; ?></span>
                  <span class="block hover:underline-highlight"><?php echo $News->get_title(); ?></span>
               </a>
            </article>
         </li>
      <?php

      }

      ?>
   </ol>
</section>
