<?php

if (!defined('ABSPATH')) {
   exit;
}

use BX_Obastidor\News\News;
use BX_Obastidor\News\Utils;

$terms = ['secundario'];

if (is_single()) {
   $terms[] = 'principal';
}

$query_args = [
   'posts_per_page' => 5,
   'tax_query'      => [
      [
         'taxonomy' => 'featured-news-order',
         'field'    => 'slug',
         'terms'    => $terms,
      ]
   ],
];

if (is_single()) {
   $query_args['post__not_in'] = [get_queried_object_id()];
}

$posts = Utils::get_news_posts($query_args);

foreach ($posts as $post) {
   $Single = new News($post);

?>
   <li class="border-b border-neutral-300 pb-4 last:border-b-0">
      <article>
         <header>
            <?php

            if ($Single->has_label()) {

            ?>
               <div class="my-4">
                  <?php get_component('news-label', ['post_object' => $Single]); ?>
               </div>
            <?php

            }

            ?>
            <a href="<?php echo esc_url($Single->get_link()); ?>" class="block font-primary text-2xl font-bold">
               <?php echo esc_html($Single->get_title()); ?>
            </a>
         </header>
      </article>
   </li>
<?php

}
