<?php

if (!defined('ABSPATH')) {
   exit;
}

use BX_Obastidor\BX_News\BX_News;
use BX_Obastidor\BX_SponsoredContent\BX_SponsoredContent;
use BX_Obastidor\BX_News\Utils;

$query_args = [
   'posts_per_page' => 5,
];

if (isset($args['exclude_ids'])) {
   $query_args['post__not_in'] = $args['exclude_ids'];
}

$query_info = []; // passed by reference will store query info
$posts = Utils::get_featured_posts('secundario', $query_args, $query_info);

foreach ($posts as $post) {
   $Single = get_post_type($post) === 'sponsored_content'
      ? new BX_SponsoredContent($post)
      : new BX_News($post);

?>
   <li class="border-b border-neutral-300 pb-4 last:border-b-0">
      <article>
         <header>
            <?php

            if ($Single->has_label()) {

            ?>
               <div class="my-4">
                  <?php get_component('news-label', ['post_object' => $Single]); ?>
               </div>
            <?php

            }

            ?>
            <a href="<?php echo esc_url($Single->get_link()); ?>" class="block font-primary text-2xl font-bold">
               <?php echo esc_html($Single->get_title()); ?>
            </a>
         </header>
      </article>
   </li>
<?php

}
