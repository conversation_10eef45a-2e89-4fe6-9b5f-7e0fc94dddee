<?php

use BX_Obastidor\BX_AdBanner\Utils;

if (!defined('ABSPATH')) {
   exit;
}

if (empty($args['post_object']) && empty($args['slot'])) {
   return;
}

$AdBanner = $args['post_object'] ?? null;

if (empty($AdBanner)) {
   $AdBanner = Utils::get_random_ad_banner_by_slot($args['slot']);
}

if (empty($AdBanner)) {
   return;
}

?>
<div <?php
      if (!empty($args['container_classes'])) {
      ?>class="<?php echo esc_attr($args['container_classes']); ?>" <?php
                                                               }
                                                                  ?>>
   <?php echo $AdBanner->get_content(); ?>
</div>
<?php
