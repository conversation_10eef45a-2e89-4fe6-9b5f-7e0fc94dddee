<?php

if (!defined('ABSPATH')) {
   exit;
}

use BX_Obastidor\AdBanner\Utils;

$AdBanner          = $args['post_object'] ?? null;
$slot              = $args['slot'] ?? null;
$container_classes = $args['container_classes'] ?? '';

if (empty($AdBanner) && !empty($slot)) {
   $AdBanner = Utils::get_random_ad_banner_by_slot($slot);
}

if (empty($AdBanner)) {
   return;
}

?>
<div class="<?php echo esc_attr($container_classes); ?>" data-banner-id="<?php echo esc_attr($AdBanner->get_the_ID()); ?>">
   <?php echo $AdBanner->get_content(); ?>
</div>
