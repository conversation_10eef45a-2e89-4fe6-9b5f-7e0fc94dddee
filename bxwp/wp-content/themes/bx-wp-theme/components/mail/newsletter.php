<?php

if (!defined('ABSPATH')) {
   exit;
}

use BX_Obastidor\News\News;

$posts = $args['posts'] ?? [];

include_once get_template_directory() . '/components/mail/header.php';

?>

<table width="100%" border="0" cellpadding="0" cellspacing="0" style="letter-spacing: 0.02em;">
   <tbody>
      <?php

      foreach ($posts as $post) {
         $Post = new News($post);

      ?>
         <tr>
            <td>
               <?php

               if ($Post->has_label()) {
                  $label_style = 'font-family: \'Fira Mono\', monospace; font-size: 12px; padding-top: 4px; padding-bottom: 4px; text-transform: uppercase; font-weight: 500; text-decoration: none;';

                  if ($Post->is_exclusive()) {
                     $label_style .= 'background-color: #dc2828; color: #ffffff; padding-left: 4px; padding-right: 4px;';
                  } else {
                     $label_style .= 'color: #9796a5;';
                  }

               ?>
                  <p style="margin-bottom: 12px;">
                     <a href="<?php echo $Post->get_label_link(); ?>" target="_blank" style="<?php echo $label_style; ?>">
                        <?php echo esc_html($Post->get_label()); ?>
                     </a>
                  </p>
               <?php

               }

               ?>
               <h2 style="margin: 0 0 20px 0; font-size: 24px; color: #273646;">
                  <?php echo esc_html($Post->get_title()); ?>
               </h2>

               <p style="margin: 0; font-size: 12px; font-weight: 700; line-height: 1.5; color: #273646; font-family: 'Fira Mono', monospace; opacity: 0.7;">
                  <?php echo esc_html($Post->get_author()->get_name()); ?>
               </p>

               <p style="margin: 0; font-size: 12px; font-weight: 400; line-height: 1.5; color: #273646; font-family: 'Fira Mono', monospace; opacity: 0.7;">
                  <a href="mailto:<?php echo esc_html($Post->get_author()->get_email()); ?>" style="color: #3869d4; text-decoration: none;">
                     <?php echo esc_html($Post->get_author()->get_email()); ?>
                  </a>
               </p>

               <p style="margin: 0 0 20px 0; font-size: 12px; font-weight: 400; line-height: 1.5; color: #273646; font-family: 'Fira Mono', monospace; opacity: 0.7; margin-top: 12px;">
                  <?php echo __('Publicada em ', 'bx-obastidor') . ' ' . esc_html($Post->get_date('d/m/Y \à\s H:i')); ?>
               </p>

               <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5; color: #273646;">
                  <?php echo esc_html($Post->get_excerpt()); ?>
               </p>

               <p style="margin: 0 0 30px 0; text-align: center;">
                  <a href="<?php echo $Post->get_link(); ?>" target="_blank" style="display: inline-block; padding: 8px 16px; background-color: #273646; color: #ffffff; font-size: 14px; font-weight: bold; text-decoration: none; border-radius: 4px; text-transform: uppercase;">
                     <?php esc_html_e('Leia mais', 'bx-obastidor'); ?>
                  </a>
               </p>

               <p style="margin: 0 0 30px 0; text-align: center;">
                  <?php echo $Post->get_newsletter_share_buttons(); ?>
               </p>
            </td>

         </tr>

      <?php

      }

      ?>
   </tbody>
</table>

<?php

include_once get_template_directory() . '/components/mail/footer.php';

include_once get_template_directory() . '/components/mail/footer-unsubscribe.php';
