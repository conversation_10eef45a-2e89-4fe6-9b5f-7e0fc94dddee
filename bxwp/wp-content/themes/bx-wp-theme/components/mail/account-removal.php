<?php

if (!defined('ABSPATH')) {
   exit;
}

$current_user = $args['current_user'];

include_once get_template_directory() . '/components/mail/header.php';

?>

<h1 style="margin: 0 0 20px 0; font-size: 24px; color: #273646;">
   <?php esc_html_e('Alerta de Remoção de Conta - O Bastidor', 'bx-obastidor'); ?>
</h1>

<p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5; color: #273646;">
   <?php

   printf(
      esc_html__('O usuário %s solicitou a remoção de sua conta no O Bastidor.', 'bx-obastidor'),
      '<strong>' . $current_user->user_login . '</strong>'
   );

   ?>
</p>

<p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5; color: #273646;">
   <?php esc_html_e('A remoção da conta deve ser feita manualmente no caso de usuários que são administradores ou autores de qualquer conteúdo no site.', 'bx-obastidor'); ?>
</p>

<p style="margin: 0 0 30px 0; text-align: center;">
   <?php

   printf(
      esc_html__('Acesse o perfil do usuário %s.', 'bx-obastidor'),
      '<a href="' . get_edit_user_link($current_user->ID) . '" style="color: #2db4ff;">' . esc_html__('aqui', 'bx-obastidor') . '</a>'
   );

   ?>
</p>

<?php

include_once get_template_directory() . '/components/mail/footer.php';
