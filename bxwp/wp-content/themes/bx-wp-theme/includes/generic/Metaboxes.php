<?php

namespace BX_Obastidor;

if (!defined('ABSPATH')) {
   exit;
}

class Metaboxes
{
   public function __construct()
   {
      add_action('add_meta_boxes', [$this, 'add_meta_boxes']);
      add_action('save_post', [$this, 'save_post']);
   }

   public function add_meta_boxes()
   {
      foreach ($this->config['post-types'] as $screen) {
         add_meta_box(
            sanitize_title($this->config['title']),
            $this->config['title'],
            [$this, 'add_meta_box_content_cb'],
            $screen,
            $this->config['context'],
            $this->config['priority'],
         );
      }
   }

   public function add_meta_box_content_cb()
   {
?>
      <table class="form-table" role="presentation">
         <tbody>
            <?php

            foreach ($this->fields as $field) {

            ?>
               <tr>
                  <th scope="row">
                     <?php $this->label($field); ?>
                  </th>
                  <td>
                     <?php $this->field($field); ?>
                  </td>
               </tr>
            <?php

            }

            ?>
         </tbody>
      </table>
<?php
   }

   protected function label($field)
   {
      printf(
         '<label class="%s" for="%s">%s</label>',
         $field['label_class'] ?? '',
         $field['id'],
         $field['label'],
      );
   }

   protected function field($field)
   {
      switch ($field['type']) {
         default:
            $this->input($field);
      }
   }

   protected function input($field)
   {
      printf(
         '<input class="regular-text" id="%s" name="%s" type="%s" value="%s" %s %s %s %s %s>',
         $field['id'],
         $field['id'],
         $field['type'],
         $this->value($field),
         isset($field['max']) ? 'max="' . $field['max'] . '"' : '',
         isset($field['min']) ? 'min="' . $field['min'] . '"' : '',
         isset($field['step']) ? 'step="' . $field['step'] . '"' : '',
         isset($field['pattern']) ? 'pattern="' . $field['pattern'] . '"' : '',
         isset($field['autocomplete']) ? 'autocomplete="' . $field['autocomplete'] . '"' : '',
      );
   }

   protected function value($field)
   {
      global $post;

      $value = metadata_exists('post', $post->ID, $field['id'])
         ? get_post_meta($post->ID, $field['id'], true)
         : ($field['default'] ?? '');

      return str_replace('\u0027', "'", $value);
   }
}
