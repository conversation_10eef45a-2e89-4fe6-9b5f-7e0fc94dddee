<?php

if (!defined('ABSPATH')) {
   exit;
}

add_filter('login_errors', 'bx_obastidor_login_generic_error_messages');
function bx_obastidor_login_generic_error_messages($error)
{
   global $errors;
   $err_codes = $errors->get_error_codes();

   if (in_array('invalid_email', $err_codes) || in_array('invalid_username', $err_codes) || in_array('incorrect_password', $err_codes)) {
      $error   = esc_html__('Erro', 'bx-obastidor');
      $message = esc_html__('Usuário e/ou senha incorretos.', 'bx-obastidor');

      return <<<ERROR
      <strong>{$error}</strong>: {$message}<br />
   ERROR;
   }

   return $error;
}
