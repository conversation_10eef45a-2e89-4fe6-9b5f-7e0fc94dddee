<?php

if (!defined('ABSPATH')) {
   exit;
}

add_action('wp_enqueue_scripts', 'bx_obastidor_enqueue_assets');
function bx_obastidor_enqueue_assets(): void
{
   wp_enqueue_style('all', get_theme_file_uri('assets/css/all.min.css'));

   wp_enqueue_script('all', get_template_directory_uri() . '/assets/js/all.min.js', [], get_template_directory() . '/assets/js/all.min.js', ['strategy' => 'defer']);

   wp_localize_script('all', 'ajax', [
      'ajaxNonce'        => wp_create_nonce('defaultNonce'),
      'ajaxUrl'          => admin_url('admin-ajax.php'),
      'postId'           => get_the_ID(),
      'privacyPolicyUrl' => get_privacy_policy_url(),
      'termsOfUseUrl'    => get_permalink(get_page_by_path('termos-de-uso')),
      'messages'         => [
         'load_more_error' => esc_html__('Erro ao carregar notícias. Tente novamente.', 'bx-obastidor'),
      ]
   ]);
}

add_action('admin_enqueue_scripts', 'bx_obastidor_enqueue_admin_assets');
function bx_obastidor_enqueue_admin_assets($current_page)
{
   // TODO: Review this logic in custom post types
   if ($current_page !== 'post-new.php' && $current_page !== 'post.php') {
      return;
   }

   wp_enqueue_style('admin', get_theme_file_uri('assets/css/admin.min.css'));
}

add_action('enqueue_block_editor_assets', 'bx_obastidor_enqueue_block_editor_assets');
function bx_obastidor_enqueue_block_editor_assets()
{
   if (file_exists(get_template_directory() . '/assets/js/block-editor.min.js')) {
      wp_enqueue_script(
         'block-editor',
         get_template_directory_uri() . '/assets/js/block-editor.min.js',
         [],
         filemtime(get_template_directory() . '/assets/js/block-editor.min.js'),
         ['strategy' => 'defer']
      );
   }

   if (file_exists(get_template_directory() . '/assets/css/block-editor.min.css')) {
      wp_enqueue_style('block-editor', get_theme_file_uri('assets/css/block-editor.min.css'));
   }
}
