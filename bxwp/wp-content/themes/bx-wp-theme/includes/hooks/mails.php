<?php

if (!defined('ABSPATH')) {
   exit;
}

require_once get_template_directory() . '/includes/composer/vendor/autoload.php';

use Aws\Lambda\LambdaClient;

add_action('pre_wp_mail', 'bx_obastidor_aws_mailer', 10, 2);
function bx_obastidor_aws_mailer($return, $args)
{
   // Ignore this hook if aws lambda is not properly configured
   if (empty(AWS_EMAIL_QUEUE_TRIGGER_ARN) || empty(AWS_REGION)) {
      return $return;
   }

   /**
    * As on this hook we are limited to the wp_mail arguments, the mail type
    * and the newsletter id must be passed as headers:
    * X-BX-OBASTIDOR-MAIL-TYPE
    * X-BX-OBASTIDOR-NEWSLETTER-ID
    */
   $type = 'manual';
   $headers = is_array($args['headers']) ? $args['headers'] : explode("\n", $args['headers']);
   foreach ($headers as $header) {

      if (strpos($header, ':') === false) {
         continue;
      }

      list($key, $value) = explode(':', $header, 2);

      if (strtoupper(trim($key)) === 'X-BX-OBASTIDOR-MAIL-TYPE') {
         if (in_array(strtoupper(trim($value)), ['NEWSLETTER', 'MANUAL-NEWSLETTER'])) {
            $type = 'manual-newsletter';
         }
      }

      if (strtoupper(trim($key)) === 'X-BX-OBASTIDOR-NEWSLETTER-ID') {
         $newsletterId = trim($value);
      }
   }

   /*
      z.object({
         type: z.literal("manual"),
         content: z.string().min(1, "'content' cannot be empty"),
         subject: z.string().min(1, "'subject' cannot be empty"),
         recipients: z
         .array(z.email("Invalid email address"))
         .min(1, "At least one recipient is required"),
      }),
      z.object({
         type: z.literal("manual-newsletter"),
         content: z.string().min(1, "'content' cannot be empty"),
         subject: z.string().min(1, "'subject' cannot be empty"),
         recipients: z
         .array(z.email("Invalid email address"))
         .min(1, "At least one recipient is required"),
         newsletterId: z.string().min(1, "'newsletterId' cannot be empty"),
      }),
      z.object({
         type: z.literal("newsletter"),
         frequency: z.enum(["daily", "weekly"]),
      })
   */

   $parsed_args = [
      'type'       => $type,
      'content'    => $args['message'],
      'subject'    => $args['subject'],
      'recipients' => preg_split('/\s*,\s*/', $args['to']),
   ];

   if ($type === 'manual-newsletter' && !empty($newsletterId)) {
      $parsed_args['newsletterId'] = $newsletterId;
   }

   $lambdaClient = new LambdaClient([
      'region'  => AWS_REGION,
      'version' => '2015-03-31',
   ]);

   $payload = json_encode($parsed_args);

   $result = $lambdaClient->invoke([
      'FunctionName' => AWS_EMAIL_QUEUE_TRIGGER_ARN,
      'Payload'      => $payload,
   ]);

   /**
    * Returning true will stop wp_mail from trying send the message again
    */
   return true;
}
