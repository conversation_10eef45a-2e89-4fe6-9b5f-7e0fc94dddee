<?php

if (!defined('ABSPATH')) {
   exit;
}

add_filter('wp_mail_from', 'bx_obastidor_custom_mail_from');
function bx_obastidor_custom_mail_from($email)
{
   $site_domain = parse_url(get_bloginfo('url'), PHP_URL_HOST);

   return 'no-reply@' . $site_domain;
}

add_filter('wp_mail_from_name', 'bx_obastidor_custom_mail_from_name');
function bx_obastidor_custom_mail_from_name($name)
{
   return get_bloginfo('name');
}

add_filter('wp_mail_content_type', 'bx_obastidor_custom_mail_content_type');
function bx_obastidor_custom_mail_content_type($content_type)
{
   return 'text/html';
}
