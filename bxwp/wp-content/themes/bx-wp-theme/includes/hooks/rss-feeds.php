<?php

if (!defined('ABSPATH')) {
   exit;
}

add_filter('default_feed', 'bx_obastidor_set_default_feed');
function bx_obastidor_set_default_feed()
{
   return 'atom';
}

add_action('init', 'bx_obastidor_redirect_rss_to_atom');
function bx_obastidor_redirect_rss_to_atom()
{
   if (preg_match('#^/rss/?$#i', $_SERVER['REQUEST_URI'])) {
      wp_redirect(get_bloginfo('atom_url'), 301);
      exit;
   }
}

add_filter('the_content_feed', '__return_empty_string');
