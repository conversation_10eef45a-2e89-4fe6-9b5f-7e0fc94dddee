<?php

if (!defined('ABSPATH')) {
   exit;
}

add_action('init', 'bx_obastidor_media_sizes');
function bx_obastidor_media_sizes()
{
   // Configurações do tamanho da miniatura (Thumbnail)
   update_option('thumbnail_size_w', 320);
   update_option('thumbnail_size_h', 180);
   update_option('thumbnail_crop', true);

   // Configurações do tamanho médio (Medium)
   update_option('medium_size_w', 649);
   update_option('medium_size_h', 360);

   // Configurações do tamanho grande (Large)
   update_option('large_size_w', 1280);
   update_option('large_size_h', 720);
}
