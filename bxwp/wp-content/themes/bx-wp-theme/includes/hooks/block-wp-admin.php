<?php

if (!defined('ABSPATH')) {
   exit;
}

// TODO: move to entity subscriber
add_action('wp_loaded', 'bx_block_subscriber_admin_access');
function bx_block_subscriber_admin_access()
{
   if (!is_admin() || wp_doing_ajax()) {
      return;
   }

   $is_subscriber = !current_user_can('edit_posts') && current_user_can('subscriber');

   if (!$is_subscriber) {
      return;
   }

   wp_safe_redirect(home_url('meu-bastidor/minhas-preferencias'));
   exit;
}
