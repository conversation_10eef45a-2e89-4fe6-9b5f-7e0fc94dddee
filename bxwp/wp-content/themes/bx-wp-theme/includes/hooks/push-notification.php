<?php

if (!defined('ABSPATH')) {
   exit;
}

add_action('acf/init', 'bx_obastidor_push_notification_register_fields');
function bx_obastidor_push_notification_register_fields()
{
   if (!function_exists('acf_add_local_field_group')) {
      return;
   }

   acf_add_local_field_group([
      'key'    => 'group_push_notification',
      'title'  => esc_html__('Push Notifications', 'bx-obastidor'),
      'fields' => [
         [
            'key'          => 'field_fcm_json',
            'label'        => esc_html__('Configuração do FCM JSON', 'bx-obastidor'),
            'name'         => 'fcm_json',
            'type'         => 'textarea',
            'instructions' => esc_html__('Cole o conteúdo do arquivo JSON da Service Account do Google Cloud', 'bx-obastidor'),
            'placeholder'  => esc_html__('{ "project_id": "...", "client_email": "...", "private_key": "..." }'),
            'required'     => 0,
            'rows'         => 15,
         ],
      ],
      'location' => [
         [
            [
               'param'    => 'options_page',
               'operator' => '==',
               'value'    => 'push-notification-settings',
            ],
         ],
      ],
      'menu_order'            => 0,
      'position'              => 'normal',
      'style'                 => 'default',
      'label_placement'       => 'top',
      'instruction_placement' => 'label',
      'hide_on_screen'        => '',
   ]);
}

add_action('acf/init', 'bx_obastidor_push_notification_options_page');
function bx_obastidor_push_notification_options_page()
{
   if (!function_exists('acf_add_options_page')) {
      return;
   }

   acf_add_options_page([
      'page_title'  => esc_html__('Configurações de Push Notifications', 'bx-obastidor'),
      'menu_title'  => esc_html__('Push Notifications', 'bx-obastidor'),
      'menu_slug'   => 'push-notification-settings',
      'capability'  => 'manage_options',
      'parent_slug' => 'options-general.php',
      'position'    => false,
      'icon_url'    => false,
   ]);
}

add_action('rest_api_init', 'bx_obastidor_push_notification_register_rest_routes');
function bx_obastidor_push_notification_register_rest_routes()
{
   register_rest_route('bx-obastidor/v1', '/check-fcm-settings', [
      'methods'             => 'GET',
      'callback'            => 'bx_obastidor_push_notification_fcm_settings',
      'permission_callback' => function () {
         return current_user_can('manage_options');
      },
   ]);

   register_rest_route('bx-obastidor/v1', '/send-push-notification', [
      'methods'             => 'POST',
      'callback'            => 'bx_obastidor_push_notification_send_notification',
      'permission_callback' => function () {
         return current_user_can('manage_options');
      },
   ]);
}

function bx_obastidor_push_notification_fcm_settings()
{
   $fcm_json_data = json_decode(get_field('fcm_json', 'option') ?? '', true);

   return [
      'isSetupComplete' => !empty($fcm_json_data),
   ];
}

function bx_obastidor_push_notification_generate_jwt_token($service_account_data)
{
   $header = [
      'alg' => 'RS256',
      'typ' => 'JWT'
   ];

   $time = time();

   $payload = [
      'iss'   => $service_account_data['client_email'],
      'scope' => 'https://www.googleapis.com/auth/firebase.messaging',
      'aud'   => 'https://oauth2.googleapis.com/token',
      'exp'   => $time + 3600, // 1 hora
      'iat'   => $time
   ];

   $header_encoded = bx_obastidor_push_notification_base64url_encode(json_encode($header));
   $payload_encoded = bx_obastidor_push_notification_base64url_encode(json_encode($payload));

   $signature = '';

   // $signature is filled by reference, so we don't need to return it
   openssl_sign(
      $header_encoded . '.' . $payload_encoded,
      $signature,
      $service_account_data['private_key'],
      'SHA256'
   );

   $signature_encoded = bx_obastidor_push_notification_base64url_encode($signature);

   return $header_encoded . '.' . $payload_encoded . '.' . $signature_encoded;
}

function bx_obastidor_push_notification_base64url_encode($data)
{
   return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
}

function bx_obastidor_push_notification_get_access_token($jwt_token)
{
   $response = wp_remote_post('https://oauth2.googleapis.com/token', [
      'headers' => [
         'Content-Type' => 'application/x-www-form-urlencoded',
      ],
      'body' => [
         'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
         'assertion'  => $jwt_token
      ],
      'timeout' => 30
   ]);

   if (is_wp_error($response)) {
      return false;
   }

   $data = json_decode(wp_remote_retrieve_body($response), true);

   return $data['access_token'] ?? false;
}

function bx_obastidor_push_notification_send_notification($request)
{
   $fcm_json = get_field('fcm_json', 'option');

   if (empty($fcm_json)) {
      return new WP_Error('fcm_not_configured', 'Service Account não está configurado', ['status' => 400]);
   }

   $service_account_data = json_decode($fcm_json, true);
   if (json_last_error() !== JSON_ERROR_NONE) {
      return new WP_Error('fcm_invalid_json', 'JSON da Service Account inválido', ['status' => 400]);
   }

   $jwt_token = bx_obastidor_push_notification_generate_jwt_token($service_account_data);
   if (!$jwt_token) {
      return new WP_Error('fcm_jwt_error', 'Erro ao gerar JWT token', ['status' => 500]);
   }

   $access_token = bx_obastidor_push_notification_get_access_token($jwt_token);
   if (!$access_token) {
      return new WP_Error('fcm_auth_error', 'Erro ao obter access token', ['status' => 500]);
   }

   $params = $request->get_params();

   $fcm_project_id = $service_account_data['project_id'];

   $message = [
      'message' => [
         'notification' => [
            'title' => sanitize_text_field($params['title'] ?? ''),
            'body'  => sanitize_textarea_field($params['body'] ?? ''),
            'image' => esc_url_raw($params['image'] ?? '')
         ],
         'data' => [
            'news_id' => sanitize_text_field($params['news_id'] ?? ''),
            'img_url' => esc_url_raw($params['image'] ?? '')
         ],
         'topic'   => sanitize_text_field($params['topic'] ?? 'test'),
         'android' => [
            'notification' => [
               'sound'        => 'default',
               'click_action' => 'FLUTTER_NOTIFICATION_CLICK'
            ]
         ],
         'apns' => [
            'payload' => [
               'aps' => [
                  'sound' => 'default'
               ]
            ]
         ]
      ]
   ];

   $response = wp_remote_post('https://fcm.googleapis.com' . '/v1/projects/' . $fcm_project_id . '/messages:send', [
      'headers' => [
         'Content-Type'  => 'application/json',
         'Authorization' => 'Bearer ' . $access_token
      ],
      'body'    => json_encode($message),
      'timeout' => 30
   ]);

   if (is_wp_error($response)) {
      return new WP_Error('fcm_request_failed', 'Erro ao enviar notificação: ' . $response->get_error_message(), ['status' => 500]);
   }

   $status_code = wp_remote_retrieve_response_code($response);
   $data = json_decode(wp_remote_retrieve_body($response), true);

   if ($status_code !== 200) {
      $error_message = 'Erro HTTP ' . $status_code;

      if (isset($data['error'])) {
         $error_message .= ': ' . $data['error']['message'];

         if (isset($data['error']['code'])) {
            $error_message .= ' (Código: ' . $data['error']['code'] . ')';
         }
      }

      return new WP_Error('fcm_http_error', $error_message, ['status' => $status_code, 'fcm_response' => $data]);
   }

   return [
      'success' => true,
      'data'    => $data
   ];
}
