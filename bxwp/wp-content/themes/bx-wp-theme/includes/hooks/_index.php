<?php

if (!defined('ABSPATH')) {
   exit;
}

require implode(DIRECTORY_SEPARATOR, [__DIR__, 'head-content.php']);
require implode(DIRECTORY_SEPARATOR, [__DIR__, 'media-sizes.php']);
require implode(DIRECTORY_SEPARATOR, [__DIR__, 'handle-assets.php']);
require implode(DIRECTORY_SEPARATOR, [__DIR__, 'register-menus.php']);
require implode(DIRECTORY_SEPARATOR, [__DIR__, 'trackers.php']);
require implode(DIRECTORY_SEPARATOR, [__DIR__, 'under-censure.php']);
require implode(DIRECTORY_SEPARATOR, [__DIR__, 'rss-feeds.php']);
require implode(DIRECTORY_SEPARATOR, [__DIR__, 'push-notification.php']);
require implode(DIRECTORY_SEPARATOR, [__DIR__, 'meu-bastidor.php']);
require implode(DIRECTORY_SEPARATOR, [__DIR__, 'virtual-pages.php']);
require implode(DIRECTORY_SEPARATOR, [__DIR__, 'mails.php']);
require implode(DIRECTORY_SEPARATOR, [__DIR__, 'block-wp-admin.php']);
require implode(DIRECTORY_SEPARATOR, [__DIR__, 'disable-comments.php']);
require implode(DIRECTORY_SEPARATOR, [__DIR__, 'custom-roles.php']);
require implode(DIRECTORY_SEPARATOR, [__DIR__, 'generic-error-messages.php']);
require implode(DIRECTORY_SEPARATOR, [__DIR__, 'oembed-documentcloud.php']);
require implode(DIRECTORY_SEPARATOR, [__DIR__, 'oembed-local-pdf.php']);
require implode(DIRECTORY_SEPARATOR, [__DIR__, 'site-social-networks.php']);
