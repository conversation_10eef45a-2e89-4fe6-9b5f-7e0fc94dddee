<?php

if (!defined('ABSPATH')) {
   exit;
}

add_action('bx_head_scripts', 'bx_obastidor_google_analytics_track');
function bx_obastidor_google_analytics_track()
{
   if (!in_array(wp_get_environment_type(), ['production'])) {
      return;
   }

?>
   <!-- Google tag (gtag.js) -->
   <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo GOOGLE_ANALYTICS_ID; ?>"></script>
   <script>
      window.dataLayer = window.dataLayer || [];

      function gtag() {
         dataLayer.push(arguments);
      }
      gtag('js', new Date());

      gtag('config', '<?php echo GOOGLE_ANALYTICS_ID ?>');
   </script>
<?php

}

add_action('bx_head_scripts', 'bx_obastidor_umami_track');
function bx_obastidor_umami_track()
{
   if (!in_array(wp_get_environment_type(), ['production'])) {
      return;
   }

?>
   <script async defer data-website-id="<?php echo UMAMI_WEBSITE_ID; ?>" src="https://ping.obastidor.com.br/umami.js"></script>
<?php

}
