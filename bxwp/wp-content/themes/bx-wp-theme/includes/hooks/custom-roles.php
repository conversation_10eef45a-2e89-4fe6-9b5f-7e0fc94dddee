<?php

if (!defined('ABSPATH')) {
   exit;
}

add_action('init', 'add_contributor_media_files_capability');
function add_contributor_media_files_capability()
{
   $contributor_role = get_role('contributor');

   if ($contributor_role && (!$contributor_role->has_cap('upload_files') || $contributor_role->has_cap('delete_posts'))) {
      $contributor_role->add_cap('upload_files');
      $contributor_role->add_cap('delete_posts');
   }
}

add_action('init', 'rename_contributor_role');
function rename_contributor_role()
{
   global $wp_roles;

   if (!isset($wp_roles)) {
      $wp_roles = new \WP_Roles();
   }

   $wp_roles->roles['contributor']['name'] = 'Repórter';
   $wp_roles->role_names['contributor']    = 'Repórter';
}

add_action('admin_menu', 'hide_media_menu');
function hide_media_menu()
{
   $current_user = wp_get_current_user();

   if (in_array('contributor', $current_user->roles)) {
      remove_menu_page('upload.php');
   }
}
