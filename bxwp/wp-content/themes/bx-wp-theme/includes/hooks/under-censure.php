<?php

if (!defined('ABSPATH')) {
   exit;
}

add_action('init', 'bx_obastidor_register_under_censure_options_page');
function bx_obastidor_register_under_censure_options_page()
{
   if (!function_exists('acf_add_options_page')) {
      return;
   }

   \acf_add_options_page([
      'page_title' => __('Sob Censura', 'bx-obastidor'),
      'menu_title' => __('Sob Censura', 'bx-obastidor'),
      'menu_slug'  => 'under-censure',
      'capability' => 'manage_options',
      'icon_url'   => 'dashicons-warning',
   ]);
}

add_action('init', 'bx_obastidor_register_under_censure_fields');
function bx_obastidor_register_under_censure_fields()
{
   if (!function_exists('acf_add_local_field_group')) {
      return;
   }

   \acf_add_local_field_group([
      'key'    => 'group_under_censure',
      'title'  => __('Banner "Sob Censura"', 'bx-obastidor'),
      'fields' => [
         [
            'key'               => 'field_under_censure_enable',
            'label'             => __('Ativar', 'bx-obastidor'),
            'name'              => 'under_censure_enable',
            'type'              => 'true_false',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => 0,
            'wrapper'           => [
               'width' => '',
               'class' => '',
               'id'    => '',
            ],
            'message'       => '',
            'default_value' => 0,
            'ui'            => 1,
            'ui_on_text'    => __('Ativar', 'bx-obastidor'),
            'ui_off_text'   => __('Desativar', 'bx-obastidor'),
         ],
         [
            'key'               => 'field_under_censure_text',
            'label'             => __('Texto do Banner', 'bx-obastidor'),
            'name'              => 'under_censure_text',
            'type'              => 'text',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_under_censure_enable',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => [
               'width' => '',
               'class' => '',
               'id'    => '',
            ],
            'default_value' => __('Sob Censura', 'bx-obastidor'),
            'placeholder'   => '',
            'prepend'       => '',
            'append'        => '',
            'maxlength'     => '',
         ],
         [
            'key'               => 'field_under_censure_link',
            'label'             => __('Link do Banner', 'bx-obastidor'),
            'name'              => 'under_censure_link',
            'type'              => 'url',
            'instructions'      => '',
            'required'          => 0,
            'conditional_logic' => [
               [
                  [
                     'field'    => 'field_under_censure_enable',
                     'operator' => '==',
                     'value'    => '1',
                  ],
               ],
            ],
            'wrapper' => [
               'width' => '',
               'class' => '',
               'id'    => '',
            ],
            'default_value' => '',
            'placeholder'   => '',
         ],
      ],
      'location' => [
         [
            [
               'param'    => 'options_page',
               'operator' => '==',
               'value'    => 'under-censure',
            ],
         ],
      ],
      'menu_order'            => 0,
      'position'              => 'normal',
      'style'                 => 'default',
      'label_placement'       => 'top',
      'instruction_placement' => 'label',
      'hide_on_screen'        => '',
      'active'                => true,
      'description'           => '',
      'show_in_rest'          => 0,
   ]);
}
