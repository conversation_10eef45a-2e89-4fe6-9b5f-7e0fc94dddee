<?php

if (!defined('ABSPATH')) {
   exit;
}

add_filter('init', 'bx_obastidor_allow_oembed_documentcloud');
function bx_obastidor_allow_oembed_documentcloud()
{
   wp_oembed_add_provider(
      '#https?://(embed\.|www\.)?documentcloud\.org/documents/.*#i',
      'https://api.www.documentcloud.org/api/oembed',
      true
   );
}

add_action('bx_obastidor_iframe_DocumentCloud', 'bx_obastidor_clean_documentcloud_iframe_url', 10, 1);
function bx_obastidor_clean_documentcloud_iframe_url(&$iframe)
{
   $url = $iframe->getAttribute('src');
   $url_array = parse_url($url);

   if (empty($url_array['scheme']) || empty($url_array['host']) || empty($url_array['path'])) {
      return;
   }

   $scheme = $url_array['scheme'];
   $domain = $url_array['host'];
   $path   = $url_array['path'];

   $query = [];
   parse_str($url_array['query'] ?? '', $query);
   $query = array_unique($query);

   $clean_url = sprintf(
      '%s://%s%s?%s',
      $scheme,
      $domain,
      $path,
      http_build_query($query)
   );

   $iframe->setAttribute('src', $clean_url);
   $iframe->setAttribute('width', 8.5);
   $iframe->setAttribute('height', 11);
   $iframe->setAttribute('style', 'border: 1px solid #d8dee2; border-radius: 0.5rem; width: 100%; height: 100%; aspect-ratio: 8.5 / 11');

   $allowed_attrs = ['src', 'width', 'height', 'style', 'allow'];

   foreach ([...$iframe->attributes] as $attribute) {
      if (!in_array($attribute->name, $allowed_attrs)) {
         $iframe->removeAttribute($attribute->name);
      }
   }
}
