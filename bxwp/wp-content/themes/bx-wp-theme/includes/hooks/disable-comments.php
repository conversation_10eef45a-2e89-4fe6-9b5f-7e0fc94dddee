<?php

if (!defined('ABSPATH')) {
   exit;
}

add_action('admin_init', 'bx_obastidor_disable_comments_admin');
function bx_obastidor_disable_comments_admin()
{
   global $pagenow;

   if ($pagenow === 'edit-comments.php' || $pagenow === 'options-discussion.php') {
      wp_redirect(admin_url());
      exit;
   }

   remove_meta_box('dashboard_recent_comments', 'dashboard', 'normal');

   foreach (get_post_types() as $post_type) {
      if (post_type_supports($post_type, 'comments')) {
         remove_post_type_support($post_type, 'comments');
         remove_post_type_support($post_type, 'trackbacks');
      }
   }
}

add_filter('comments_open', '__return_false', 20, 2);
add_filter('pings_open', '__return_false', 20, 2);
add_filter('comments_array', '__return_empty_array', 10, 2);

add_action('admin_menu', 'bx_obastidor_remove_comments_menu');
function bx_obastidor_remove_comments_menu()
{
   remove_menu_page('edit-comments.php');
   remove_submenu_page('options-general.php', 'options-discussion.php');
}

add_action('init', 'bx_obastidor_remove_admin_bar_comments');
function bx_obastidor_remove_admin_bar_comments()
{
   if (is_admin_bar_showing()) {
      remove_action('admin_bar_menu', 'wp_admin_bar_comments_menu', 60);
   }
}

add_action('wp_before_admin_bar_render', 'bx_obastidor_remove_admin_bar_comments_menu');
function bx_obastidor_remove_admin_bar_comments_menu()
{
   global $wp_admin_bar;

   $wp_admin_bar->remove_menu('comments');
}
