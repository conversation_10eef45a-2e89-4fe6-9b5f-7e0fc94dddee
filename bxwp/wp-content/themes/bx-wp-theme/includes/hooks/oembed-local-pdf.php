<?php

if (!defined('ABSPATH')) {
   exit;
}

add_filter('init', 'bx_obastidor_allow_oembed_local_pdf');
function bx_obastidor_allow_oembed_local_pdf()
{
   wp_oembed_add_provider(
      '#^https?://([a-zA-Z0-9-]+\.)?obastidor\.com\.br/.*\.pdf$#i',
      home_url('wp-json/bx-obastidor-docs/v1/embed'),
      true
   );
}

add_action('rest_api_init', 'bx_obastidor_add_oembed_local_pdf');
function bx_obastidor_add_oembed_local_pdf()
{
   register_rest_route(
      'bx-obastidor-docs/v1',
      '/embed',
      [
         'methods'             => 'GET',
         'callback'            => 'bx_obastidor_get_oembed_local_pdf',
         'permission_callback' => '__return_true',
      ]
   );
}

function bx_obastidor_get_oembed_local_pdf($request)
{
   $attachment_id = attachment_url_to_postid($request->get_param('url'));

   $url = str_replace(
      home_url('/'),
      home_url('/', 'https'),
      wp_get_attachment_url($attachment_id)
   );

   if (empty($url)) {
      return new WP_Error('invalid_url', __('URL inválida', 'bx-obastidor'), ['status' => \WP_Http::NOT_FOUND]);
   }

   $title = $attachment_id
      ? get_the_title($attachment_id)
      : __('PDF incorporado', 'bx-obastidor');

   $oembed_data = [
      'type'          => 'rich',
      'version'       => '1.0',
      'provider_url'  => home_url(),
      'author_name'   => get_bloginfo('name'),
      'author_url'    => home_url(),
      'title'         => $title,
      'html'          => sprintf(
         '<iframe src="%s" width="100%%" height="%s" title="%s" frameborder="0" allowfullscreen></iframe>',
         esc_url($url),
         esc_attr('768px'),
         esc_attr($title)
      ),
      'height' => '768px',
   ];

   return rest_ensure_response($oembed_data);
}
