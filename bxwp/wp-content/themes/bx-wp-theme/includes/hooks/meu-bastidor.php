<?php

if (!defined('ABSPATH')) {
   exit;
}

use BX_Obastidor\Newsletter\Utils;

add_action('wp', 'bx_obastidor_login_callback');
function bx_obastidor_login_callback()
{
   if (isset($_GET['token'])) {
      $login_data = bx_obastidor_magic_link_check_token();
      $action = $login_data['action'] ?? 'error';
   } elseif (isset($_POST['bx_obastidor_login_option'])) {
      $action = $_POST['bx_obastidor_login_option'];
      $login_data = [
         'nonce'            => $_POST['_nonce'] ?? '',
         'user_login'       => $_POST['user_login'] ?? '',
         'user_password'    => $_POST['user_password'] ?? '',
         'magic_link_login' => $_POST['magic_link_login'] ?? '',
         'user_name'        => $_POST['user_name'] ?? '',
         'user_lastname'    => $_POST['user_lastname'] ?? '',
         'user_email'       => $_POST['user_email'] ?? '',
         'user_password'    => $_POST['user_password'] ?? '',
      ];
   } else {
      return;
   }

   // Hooks should kill script with bx_obastidor_login_conclusion_redirect
   do_action('bx_obastidor_login_callback_' . $action, $login_data);

   // Failsafe in case hook is not defined
   bx_obastidor_login_conclusion_redirect(
      [
         'action'  => 'error',
         'title'   => __('Erro ao processar login', 'bx-obastidor'),
         'message' => __('Ops... ocorreu um erro ao processar seu login. Tente novamente, por favor.', 'bx-obastidor'),
         'cta'     => [
            [
               'url'     => home_url('meu-bastidor/login?option=login'),
               'caption' => __('Fazer Login', 'bx-bastidor')
            ],
            [
               'url'     => home_url('meu-bastidor/login?option=register'),
               'caption' => __('Criar Conta', 'bx-bastidor')
            ]
         ]
      ]
   );
}

add_action('bx_obastidor_login_callback_register', 'bx_obastidor_mb_register');
function bx_obastidor_mb_register($login_data)
{
   $nonce = $login_data['nonce'] ?? '';

   if (!wp_verify_nonce($nonce, 'bx_obastidor_login')) {
      return bx_obastidor_login_conclusion_redirect(
         [
            'action'  => 'error',
            'title'   => __('Página expirada ou inválida', 'bx-obastidor'),
            'cta'     => [
               'url'     => home_url('meu-bastidor/login?option=login'),
               'caption' => __('Fazer Login', 'bx-bastidor')
            ]
         ]
      );
   }

   $user_name = $login_data['user_name'] ?? '';
   $user_lastname = $login_data['user_lastname'] ?? '';
   $user_email = $login_data['user_email'] ?? '';
   $user_password = $login_data['user_password'] ?? '';

   if (empty($user_name) || empty($user_lastname) || empty($user_email) || empty($user_password)) {
      return bx_obastidor_login_conclusion_redirect(
         [
            'action'  => 'error',
            'title'   => __('Cadastro inválido', 'bx-obastidor'),
            'message' => __('Por favor, preencha todos os campos.', 'bx-obastidor'),
            'cta'     => [
               'url'     => home_url('meu-bastidor/login?option=register'),
               'caption' => __('Novo Cadastro', 'bx-bastidor')
            ]
         ]
      );
   }

   if (email_exists($user_email)) {
      return bx_obastidor_login_conclusion_redirect(
         [
            'action'  => 'error',
            'title'   => __('Já existe um cadastro com este e-mail', 'bx-obastidor'),
            'message' => __('Faça login usando sua senha ou o link de acesso.', 'bx-obastidor'),
            'cta'     => [
               'url'     => home_url('meu-bastidor/login?option=login'),
               'caption' => __('Fazer Login', 'bx-bastidor')
            ]
         ]
      );
   }

   $mail_sent = bx_obastidor_send_magic_link(
      $user_email,
      [
         'template' => 'mail-verification',
         'action' => 'verified-register',
         'args' => [
            'user_name' => $user_name,
            'user_lastname' => $user_lastname,
            'user_email' => $user_email,
            'user_password' => $user_password
         ]
      ]
   );

   if ($mail_sent) {
      return bx_obastidor_login_conclusion_redirect(
         [
            'action'  => 'success',
            'title'   => __('Verifique seu email', 'bx-obastidor'),
            'message' => __('Enviamos um link de ativação para seu email.', 'bx-obastidor'),
         ]
      );
   }

   return bx_obastidor_login_conclusion_redirect(
      [
         'action'  => 'error',
         'title'   => __('Erro ao cadastrar usuário', 'bx-obastidor'),
         'message' => __('Ops... ocorreu um erro ao cadastrar o usuário. Tente novamente.', 'bx-obastidor'),
         'cta'     => [
            'url'     => home_url('meu-bastidor/login?option=register'),
            'caption' => __('Novo Cadastro', 'bx-bastidor')
         ]
      ]
   );
}

add_action('bx_obastidor_login_callback_verified-register', 'bx_obastidor_mb_verified_register');
function bx_obastidor_mb_verified_register($data)
{
   $user_email    = $data['args']['user_email'];
   $user_password = $data['args']['user_password'];
   $user_name     = $data['args']['user_name'];
   $user_lastname = $data['args']['user_lastname'];

   if (email_exists($user_email)) {
      return bx_obastidor_login_conclusion_redirect(
         [
            'action'  => 'error',
            'title'   => __('Já existe um cadastro com este e-mail', 'bx-obastidor'),
            'message' => __('Por favor, faça login usando sua senha ou o link de acesso.', 'bx-obastidor'),
            'cta'     => [
               'url'     => home_url('meu-bastidor/login?option=login'),
               'caption' => __('Fazer Login', 'bx-bastidor')
            ]
         ]
      );
   }

   $user_id = wp_insert_user([
      'user_login' => $user_email,
      'user_email' => $user_email,
      'user_pass'  => $user_password,
      'first_name' => $user_name,
      'last_name' => $user_lastname,
      'role'       => 'subscriber',
      'meta_input' => [
         'bx_obastidor_subscriber_status' => 'subscribed',
         'bx_obastidor_customized_feed_sections' => get_categories([
            'exclude'    => get_cat_ID('Sem categoria'),
            'hide_empty' => false,
            'fields'     => 'ids'
         ])
      ]
   ]);

   if (is_wp_error($user_id)) {

      return bx_obastidor_login_conclusion_redirect(
         [
            'action'  => 'error',
            'title'   => __('Erro ao criar usuário', 'bx-obastidor'),
            'message' => __('Ops... ocorreu um erro ao criar o usuário. Tente novamente.', 'bx-obastidor'),
            'cta'     => [
               'url'     => home_url('meu-bastidor/login?option=register'),
               'caption' => __('Novo Cadastro', 'bx-bastidor')
            ]
         ]
      );
   }

   $user = get_user_by('ID', $user_id);

   Utils::update_subscriber_newsletters($user_id, Utils::get_newsletters_lists('slug'));

   bx_obastidor_mb_verified_login([
      'args' => [
         'user' => $user
      ]
   ]);
}

add_action('bx_obastidor_login_callback_login', 'bx_obastidor_mb_login');
function bx_obastidor_mb_login($login_data)
{
   $nonce = $login_data['nonce'] ?? '';
   if (!wp_verify_nonce($nonce, 'bx_obastidor_login')) {
      return bx_obastidor_login_conclusion_redirect(
         [
            'action'  => 'error',
            'title'   => __('Página expirada ou inválida', 'bx-obastidor'),
            'cta'     => [
               'url'     => home_url('meu-bastidor/login?option=login'),
               'caption' => __('Fazer Login', 'bx-bastidor')
            ]
         ]
      );
   }

   $magic_link_email = $login_data['magic_link_login'] ?? '';

   if (!empty($magic_link_email)) {
      $user = get_user_by('email', $magic_link_email);
      if ($user) {
         return bx_obastidor_login_conclusion_redirect(
            bx_obastidor_send_magic_link(
               $magic_link_email,
               [
                  'template' => 'magic-link',
                  'action'   => 'verified-login',
                  'args'     => [
                     'user' => $user
                  ]
               ]
            )
         );
      } else {
         return bx_obastidor_login_conclusion_redirect(
            [
               'action'  => 'error',
               'title'   => __('E-mail não encontrado', 'bx-obastidor'),
               'message' =>
               __('Verfique se o endereço está correto e tente novamente ou crie uma nova conta.', 'bx-obastidor'),
               'cta'     => [
                  [
                     'url'     => home_url('meu-bastidor/login?option=login'),
                     'caption' => __('Fazer Login', 'bx-bastidor')
                  ],
                  [
                     'url'     => home_url('meu-bastidor/login?option=register'),
                     'caption' => __('Criar Conta', 'bx-bastidor')
                  ]
               ]
            ]
         );
      }
   }

   $user_login = $login_data['user_login'] ?? '';
   $user_password = $login_data['user_password'] ?? '';

   $user = wp_authenticate($user_login, $user_password);

   if (! is_wp_error($user)) {
      return bx_obastidor_login_conclusion_redirect(
         bx_obastidor_mb_verified_login(
            [
               'args' => [
                  'user' => $user
               ]
            ]
         )
      );
   }

   return bx_obastidor_login_conclusion_redirect(
      [
         'action'  => 'error',
         'title'   => __('Erro ao fazer login', 'bx-obastidor'),
         'message' => __('Ops... ocorreu um erro ao fazer login. Tente novamente, por favor.', 'bx-obastidor'),
         'cta'     => [
            'url'     => home_url('meu-bastidor/login?option=login'),
            'caption' => __('Fazer Login', 'bx-bastidor')
         ]
      ]
   );
}

add_action('bx_obastidor_login_callback_verified-login', 'bx_obastidor_mb_verified_login');
function bx_obastidor_mb_verified_login($data)
{
   $user = $data['args']['user'];

   $current_user = wp_set_current_user($user->ID);
   wp_set_auth_cookie($user->ID);
   do_action('wp_login', $current_user->user_login, $current_user);

   wp_redirect(home_url('meu-bastidor'));
   exit;
}

function bx_obastidor_send_magic_link($email, $args = [])
{
   $token_validity = 60 * 15;

   $magic_link_data = [
      'email'       => $email,
      'token'       => wp_generate_password(32, false, false),
      'expires_at'  => time() + $token_validity,
      'redirect_to' => $args['redirect_to'] ?? home_url('meu-bastidor/login'),
      'action'      => $args['action'] ?? 'login',
      'args'        => $args['args'] ?? []
   ];

   $mail_template = $args['template'] ?? 'magic-link';

   set_transient(
      'bx_obastidor_magic_link_data_' . $magic_link_data['token'],
      $magic_link_data,
      $token_validity + (60 * 60) // guarda 1 hora extra para informar sobre token expirado
   );

   $magic_link_url = home_url('meu-bastidor/login') . '?token=' . urlencode($magic_link_data['token']);

   ob_start();

   get_component('mail/' . $mail_template, [
      'email'          => $email,
      'magic_link_url' => $magic_link_url
   ]);

   $content = ob_get_clean();

   $mail_sent = wp_mail($email, __('Seu link de acesso - O Bastidor'), $content, [
      'Content-Type: text/html; charset=UTF-8'
   ]);

   return [
      'icon'    => 'inbox',
      'action'  => $mail_sent ? 'success' : 'error',
      'title'   => $mail_sent ? __('Link enviado com sucesso', 'bx-obastidor') : __('Erro ao enviar link', 'bx-obastidor'),
      'message' => $mail_sent ? __('Acesse seu e-mail e clique no link que enviamos para continuar.', 'bx-obastidor') : __('Erro ao enviar e-mail', 'bx-obastidor'),
      'cta'     => $mail_sent ? false : [
         'url'     => home_url('meu-bastidor/login?option=login'),
         'caption' => __('Retornar', 'bx-bastidor')
      ]
   ];
}

function bx_obastidor_login_user($user_login, $user_password)
{
   $user = wp_authenticate($user_login, $user_password);

   if (is_wp_error($user)) {
      return [
         'action'  => 'error',
         'title'   => __('E-mail ou senha inválidos', 'bx-obastidor'),
         'cta'     => [
            'url'     => home_url('meu-bastidor/login?option=login'),
            'caption' => __('Fazer Login', 'bx-bastidor')
         ]
      ];
   }

   $current_user = wp_set_current_user($user->ID);
   wp_set_auth_cookie($user->ID);
   do_action('wp_login', $current_user->user_login, $current_user);

   wp_redirect(home_url('meu-bastidor'));
   exit;
}

function bx_obastidor_magic_link_check_token()
{
   $token = $_GET['token'] ?? '';

   if (empty($token)) {
      return;
   }

   $magic_link_data = get_transient('bx_obastidor_magic_link_data_' . $token);

   delete_transient('bx_obastidor_magic_link_data_' . $token);

   if (!$magic_link_data || $magic_link_data['token'] !== $token) {
      return bx_obastidor_login_conclusion_redirect(
         [
            'action'  => 'error',
            'title'   => __('Ops... este link não é válido.'),
            'message' => __('Solicite um novo link.', 'bx-obastidor'),
            'cta'     => [
               'url'     => home_url('meu-bastidor/login?option=login'),
               'caption' => __('Fazer Login', 'bx-bastidor')
            ]
         ]
      );
   }

   if ($magic_link_data['expires_at'] < time()) {
      return bx_obastidor_login_conclusion_redirect(
         [
            'action'  => 'error',
            'title'   => __('Ops... este link já expirou. Solicite um novo link.', 'bx-obastidor'),
            'cta'     => [
               'url'     => home_url('meu-bastidor/login?option=login'),
               'caption' => __('Fazer Login', 'bx-bastidor')
            ]
         ]
      );
   }

   return $magic_link_data;
}

add_action('rest_api_init', 'bx_obastidor_register_rest_routes');
function bx_obastidor_register_rest_routes()
{
   register_rest_route('bx-obastidor/v1', '/delete-account', [
      'methods'             => 'DELETE',
      'callback'            => 'bx_obastidor_delete_account_callback',
      'permission_callback' => '__return_true',
   ]);

   register_rest_route('bx-obastidor/v1', '/save-preferences', [
      'methods'             => 'POST',
      'callback'            => 'bx_obastidor_save_preferences_callback',
      'permission_callback' => '__return_true',
   ]);

   register_rest_route('bx-obastidor/v1', '/get-preferences', [
      'methods'             => 'GET',
      'callback'            => 'bx_obastidor_get_preferences_callback',
      'permission_callback' => '__return_true',
   ]);
}

function bx_obastidor_delete_account_callback($request)
{
   $current_user = wp_get_current_user();

   if (!$current_user->exists()) {
      return new WP_Error('not_logged_in', __('Usuário não está logado', 'bx-obastidor'), ['status' => WP_Http::UNAUTHORIZED]);
   }

   $has_posts = count_user_posts($current_user->ID) > 0;

   $is_subscriber = in_array('subscriber', $current_user->roles);

   $success = false;

   if ($is_subscriber && !$has_posts) {
      require_once ABSPATH . 'wp-admin/includes/user.php';
      $success = wp_delete_user($current_user->ID);

      $title   = $success ? __('Cadastro removido', 'bx-obastidor') : __('Erro ao remover cadastro', 'bx-obastidor');
      $message = $success ? __('Cadastro removido com sucesso!', 'bx-obastidor') : __('Erro ao remover cadastro', 'bx-obastidor');
   } else {
      $success = bx_obastidor_disable_user_account($current_user);

      $title   = $success ? __('Cadastro desabilitado', 'bx-obastidor') : __('Erro ao desabilitar cadastro', 'bx-obastidor');
      $message = $success ? __('Cadastro desabilitado com sucesso!', 'bx-obastidor') : __('Erro ao desabilitar cadastro', 'bx-obastidor');
   }

   if ($success) {
      wp_logout();
   }

   $confirmation_id = wp_generate_password(8, false, false);

   $confirmation_data = [
      'action'  => $success ? 'success' : 'error',
      'title'   => $title,
      'message' => $message
   ];

   set_transient('bx_obastidor_confirmation_' . $confirmation_id, $confirmation_data, 60 * 15);

   return [
      'success' => $success,
      'data'    => [
         'message'     => $message,
         'redirect_to' => home_url('meu-bastidor/login?option=conclusion&confirmation=' . $confirmation_id),
      ]
   ];
}

function bx_obastidor_disable_user_account($current_user)
{
   add_filter('send_password_change_email', '__return_false');
   add_filter('send_email_change_email', '__return_false');

   $success = wp_update_user([
      'ID'       => $current_user->ID,
      'role'     => '',
      'password' => wp_generate_password(64, true, true),
   ]);

   remove_filter('send_password_change_email', '__return_false');
   remove_filter('send_email_change_email', '__return_false');

   if ($success) {
      ob_start();

      get_component('mail/account-removal', [
         'current_user' => $current_user
      ]);

      $content = ob_get_clean();

      wp_mail(get_option('admin_email'), __('Providência necessária - Remoção de conta - O Bastidor'), $content, [
         'Content-Type: text/html; charset=UTF-8',
      ]);
   }

   return $success;
}

function bx_obastidor_get_preferences_callback($request)
{
   $current_user = wp_get_current_user();

   if (!$current_user->exists()) {
      return new WP_Error('not_logged_in', __('Usuário não está logado', 'bx-obastidor'), ['status' => WP_Http::UNAUTHORIZED]);
   }

   $categories    = (array) get_user_meta($current_user->ID, 'bx_obastidor_customized_feed_sections', true);
   $notifications = (array) get_user_meta($current_user->ID, 'bx_obastidor_user_notifications', true);

   return rest_ensure_response([
      'success' => true,
      'message' => __('Preferências obtidas com sucesso', 'bx-obastidor'),
      'data'    => [
         'categories'    => $categories,
         'notifications' => $notifications,
         'current_user'  => [
            'first_name' => $current_user->first_name,
            'last_name'  => $current_user->last_name,
            'email'      => $current_user->user_email,
            'password'   => $current_user->user_pass,
         ]
      ]
   ]);
}

function bx_obastidor_save_preferences_callback($request)
{
   $current_user = wp_get_current_user();

   if (!$current_user->exists()) {
      return new WP_Error('not_logged_in', __('Usuário não está logado', 'bx-obastidor'), ['status' => WP_Http::UNAUTHORIZED]);
   }

   $params        = $request->get_params();
   $categories    = $params['categories'] ?? [];
   $notifications = $params['notifications'] ?? [];
   $modified_user = $params['current_user'] ?? [];

   $current_categories    = (array) get_user_meta($current_user->ID, 'bx_obastidor_customized_feed_sections', true);
   $current_notifications = (array) get_user_meta($current_user->ID, 'bx_obastidor_user_notifications', true);

   $sanitized_categories = [];

   foreach ($categories as $category_id) {
      $category_id = (int) $category_id;

      $term = get_term($category_id, 'category');

      if ($term && !is_wp_error($term)) {
         $sanitized_categories[] = $category_id;
      }
   }

   $result_categories = update_user_meta($current_user->ID, 'bx_obastidor_customized_feed_sections', $sanitized_categories);

   $result_notifications = BX_Obastidor\Newsletter\Utils::update_subscriber_newsletters($current_user->ID, $notifications);

   $result_current_user = wp_update_user([
      'ID'           => $current_user->ID,
      'first_name'   => $modified_user['first_name'] ?? $current_user->first_name,
      'last_name'    => $modified_user['last_name'] ?? $current_user->last_name,
      'user_email'   => $modified_user['email'] ?? $current_user->user_email,
      'user_pass'    => $modified_user['password'] ?? $current_user->user_pass,
      'display_name' => $modified_user['first_name'] . ' ' . $modified_user['last_name'] ?? $current_user->display_name,
   ]);

   if ($result_notifications) {
      update_user_meta($current_user->ID, 'bx_obastidor_subscriber_status', 'subscribed');
   }

   if ($current_categories !== $sanitized_categories && $result_categories === false) {
      return new WP_Error('save_failed', __('Erro ao atualizar categorias', 'bx-obastidor'), ['status' => WP_Http::INTERNAL_SERVER_ERROR]);
   }

   if ($current_notifications !== $notifications && $result_notifications === false) {
      return new WP_Error('save_failed', __('Erro ao atualizar notificações', 'bx-obastidor'), ['status' => WP_Http::INTERNAL_SERVER_ERROR]);
   }

   if (
      (
         $current_user->first_name !== $modified_user['first_name'] ||
         $current_user->last_name !== $modified_user['last_name'] ||
         $current_user->user_email !== $modified_user['email'] ||
         $current_user->user_pass !== $modified_user['password']
      ) && $result_current_user === false
   ) {
      return new WP_Error('save_failed', __('Erro ao atualizar usuário', 'bx-obastidor'), ['status' => WP_Http::INTERNAL_SERVER_ERROR]);
   }

   return rest_ensure_response([
      'success' => true,
      'message' => __('Preferências salvas com sucesso', 'bx-obastidor'),
      'data'    => [
         'categories'    => $sanitized_categories,
         'notifications' => $notifications,
         'current_user'  => $modified_user,
      ]
   ]);
}

function bx_obastidor_login_conclusion_redirect($data)
{
   $confirmation_id = wp_generate_password(8, false, false);

   set_transient('bx_obastidor_confirmation_' . $confirmation_id, $data, 60 * 15);

   wp_safe_redirect(home_url('meu-bastidor/login?option=conclusion&confirmation=' . $confirmation_id));
   exit;
}
