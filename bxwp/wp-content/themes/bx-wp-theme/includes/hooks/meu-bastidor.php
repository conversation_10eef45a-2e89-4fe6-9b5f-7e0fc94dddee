<?php

if (!defined('ABSPATH')) {
   exit;
}

add_action('wp', 'bx_obastidor_send_magic_link');
function bx_obastidor_send_magic_link()
{
   if (!isset($_POST['action']) || $_POST['action'] !== 'bx_obastidor_send_magic_link') {
      return;
   }

   if (!wp_verify_nonce($_POST['nonce'] ?? '', 'bx_obastidor_send_magic_link')) {
      wp_die(__('Não autorizado', 'bx-obastidor'));
      return;
   }

   $email = $_POST['email'] ?? '';

   if (empty($email) || !is_email($email)) {
      wp_die(__('E-mail inválido', 'bx-obastidor'));
      return;
   }

   $token_validity = 60 * 15;

   $magic_link_data = [
      'email'       => $email,
      'token'       => wp_generate_password(32, false, false),
      'expires_at'  => time() + $token_validity,
      'redirect_to' => $_POST['redirect_to'] ?? home_url('meu-bastidor/login')
   ];

   set_transient(
      'bx_obastidor_magic_link_data_' . $magic_link_data['token'],
      $magic_link_data,
      $token_validity + (60 * 60) // guarda 1 hora extra para informar sobre token expirado
   );

   $magic_link_url = home_url('meu-bastidor/login') . '?token=' . urlencode($magic_link_data['token']);

   ob_start();

   get_component('mail/magic-link', [
      'email'          => $email,
      'magic_link_url' => $magic_link_url
   ]);

   $content = ob_get_clean();

   $mail_sent = wp_mail($email, __('Seu link de acesso - O Bastidor'), $content, [
      'Content-Type: text/html; charset=UTF-8'
   ]);

   $confirmation_id = wp_generate_password(8, false, false);

   $confirmation_data = [
      'action'  => $mail_sent ? 'success' : 'error',
      'title'   => $mail_sent ? __('Link enviado com sucesso', 'bx-obastidor') : __('Erro ao enviar link', 'bx-obastidor'),
      'message' => $mail_sent ? __('Acesse seu e-mail e clique no link que enviamos para continuar.', 'bx-obastidor') : __('Erro ao enviar e-mail', 'bx-obastidor')
   ];

   set_transient('bx_obastidor_confirmation_' . $confirmation_id, $confirmation_data, 60 * 15);

   wp_redirect(home_url('meu-bastidor/confirmacao?confirmation=' . $confirmation_id));
   exit;
}

add_action('wp', 'bx_obastidor_magic_link_check_token');
function bx_obastidor_magic_link_check_token()
{
   $token = $_GET['token'] ?? '';

   if (empty($token)) {
      return;
   }

   $magic_link_data = get_transient('bx_obastidor_magic_link_data_' . $token);

   delete_transient('bx_obastidor_magic_link_data_' . $token);

   if (!$magic_link_data || $magic_link_data['token'] !== $token) {
      wp_safe_redirect(add_query_arg(
         'bx_obastidor_magic_link_error',
         __('Ops... este link não é válido. Solicite um novo link.', 'bx-obastidor'),
         home_url('meu-bastidor/login')
      ));
      exit;
   }

   if ($magic_link_data['expires_at'] < time()) {
      wp_safe_redirect(add_query_arg(
         'bx_obastidor_magic_link_error',
         __('Ops... este link já expirou. Solicite um novo link.', 'bx-obastidor'),
         home_url('meu-bastidor/login')
      ));
      exit;
   }

   $email = $magic_link_data['email'] ?? '';

   $existing_user = get_user_by('email', $email);

   if ($existing_user) {
      $user_id     = $existing_user->ID;
      $redirect_to = $magic_link_data['redirect_to'];
   } else {
      $user_id = wp_insert_user([
         'user_login' => $email,
         'user_email' => $email,
         'user_pass'  => wp_generate_password(64, true, true),
         'role'       => 'subscriber'
      ]);

      if (is_wp_error($user_id)) {
         $confirmation_id = wp_generate_password(8, false, false);

         $confirmation_data = [
            'action'  => 'error',
            'title'   => __('Erro ao criar usuário', 'bx-obastidor'),
            'message' => __('Ops... ocorreu um erro ao criar o usuário. Solicite um novo link.', 'bx-obastidor')
         ];

         set_transient('bx_obastidor_confirmation_' . $confirmation_id, $confirmation_data, 60 * 15);

         wp_safe_redirect(home_url('meu-bastidor/confirmacao?confirmation=' . $confirmation_id));
         exit;
      }

      $redirect_to = $magic_link_data['redirect_to'];
   }

   $current_user = wp_set_current_user($user_id);
   wp_set_auth_cookie($user_id);
   do_action('wp_login', $current_user->user_login, $current_user);

   wp_safe_redirect($redirect_to);
   exit;
}

add_action('rest_api_init', 'bx_obastidor_register_remove_account_endpoint');
function bx_obastidor_register_remove_account_endpoint()
{
   register_rest_route('bx-obastidor/v1', '/delete-account', [
      'methods'             => 'POST',
      'callback'            => 'bx_obastidor_delete_account_callback',
      'permission_callback' => '__return_true',
   ]);
}

function bx_obastidor_delete_account_callback($request)
{
   $current_user = wp_get_current_user();

   if (!$current_user->exists()) {
      return [
         'success' => false,
         'data'    => [
            'message'     => __('Usuário não existe', 'bx-obastidor'),
            'redirect_to' => home_url('meu-bastidor/login')
         ]
      ];
   }

   $has_posts = count_user_posts($current_user->ID) > 0;

   $is_subscriber = in_array('subscriber', $current_user->roles);

   $success = false;

   if ($is_subscriber && !$has_posts) {
      require_once ABSPATH . 'wp-admin/includes/user.php';
      $success = wp_delete_user($current_user->ID);

      $title   = $success ? __('Cadastro removido', 'bx-obastidor') : __('Erro ao remover cadastro', 'bx-obastidor');
      $message = $success ? __('Cadastro removido com sucesso!', 'bx-obastidor') : __('Erro ao remover cadastro', 'bx-obastidor');
   } else {
      $success = bx_obastidor_disable_user_account($current_user);

      $title   = $success ? __('Cadastro desabilitado', 'bx-obastidor') : __('Erro ao desabilitar cadastro', 'bx-obastidor');
      $message = $success ? __('Cadastro desabilitado com sucesso!', 'bx-obastidor') : __('Erro ao desabilitar cadastro', 'bx-obastidor');
   }

   if ($success) {
      wp_logout();
   }

   $confirmation_id = wp_generate_password(8, false, false);

   $confirmation_data = [
      'action'  => $success ? 'success' : 'error',
      'title'   => $title,
      'message' => $message
   ];

   set_transient('bx_obastidor_confirmation_' . $confirmation_id, $confirmation_data, 60 * 15);

   return [
      'success' => $success,
      'data'    => [
         'message'     => $message,
         'redirect_to' => home_url('meu-bastidor/confirmacao?confirmation=' . $confirmation_id),
      ]
   ];
}

function bx_obastidor_disable_user_account($current_user)
{
   add_filter('send_password_change_email', '__return_false');
   add_filter('send_email_change_email', '__return_false');

   $success = wp_update_user([
      'ID'       => $current_user->ID,
      'role'     => '',
      'password' => wp_generate_password(64, true, true),
   ]);

   remove_filter('send_password_change_email', '__return_false');
   remove_filter('send_email_change_email', '__return_false');

   if ($success) {
      ob_start();

      get_component('mail/account-removal', [
         'current_user' => $current_user
      ]);

      $content = ob_get_clean();

      wp_mail(get_option('admin_email'), __('Providência necessária - Remoção de conta - O Bastidor'), $content, [
         'Content-Type: text/html; charset=UTF-8',
      ]);
   }

   return $success;
}
