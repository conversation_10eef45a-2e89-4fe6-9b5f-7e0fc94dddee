<?php

if (!defined('ABSPATH')) {
   exit;
}

class Virtual_Pages
{
   public function __construct()
   {
      \add_filter('bx_sntls_virtual_pages_list', [$this, 'virtual_pages']);
      \add_action('wp_head', [$this, 'meta_tags']);
      \add_filter('document_title_parts', [$this, 'set_title'], 11);
      \add_filter('wpseo_title', [$this, 'set_title'], 11);
      \add_action('wp', [$this, 'access_control']);
   }

   public function virtual_pages($pages)
   {
      $pages[] = [
         'title'         => 'Meu Bastidor',
         'filename'      => 'meu-bastidor',
         'endpoint_base' => 'meu-bastidor',
         'rewrites'      => [
            [
               'url' => 'meu-bastidor',
            ],
         ],
         'redirect' => [
            'url' => 'meu-bastidor/login',
            'condition' => function () {
               return !is_user_logged_in();
            }
         ]
      ];

      $pages[] = [
         'title'         => 'Login – Meu Bastidor',
         'filename'      => 'login',
         'endpoint_base' => 'meu-bastidor',
         'rewrites'      => [
            [
               'url' => 'meu-bastidor/login',
            ],
         ],
         'redirect' => [
            'url' => 'meu-bastidor',
            'condition' => 'is_user_logged_in'
         ]
      ];

      $pages[] = [
         'title'         => 'Cadastre-se – Meu Bastidor',
         'filename'      => 'cadastre-se',
         'endpoint_base' => 'meu-bastidor',
         'rewrites'      => [
            [
               'url' => 'meu-bastidor/cadastre-se',
            ],
         ],
         'redirect' => [
            'url' => 'meu-bastidor',
            'condition' => 'is_user_logged_in'
         ]
      ];

      $pages[] = [
         'title'         => 'Minhas Preferências – Meu Bastidor',
         'filename'      => 'minhas-preferencias',
         'endpoint_base' => 'meu-bastidor',
         'rewrites'      => [
            [
               'url' => 'meu-bastidor/minhas-preferencias',
            ],
         ],
         'redirect' => [
            'url' => 'meu-bastidor/login',
            'condition' => function () {
               return !is_user_logged_in();
            }
         ]
      ];

      $pages[] = [
         'title'         => 'Confirmação – Meu Bastidor',
         'filename'      => 'confirmacao',
         'endpoint_base' => 'meu-bastidor',
         'rewrites'      => [
            [
               'url' => 'meu-bastidor/confirmacao',
            ],
         ],
         'redirect' => [
            'force_404' => true,
            'condition' => function () {
               $confirmation = $_GET['confirmation'] ?? null;

               if (empty($confirmation)) {
                  return true;
               }

               $confirmation_data = get_transient('bx_obastidor_confirmation_' . $confirmation);

               if (empty($confirmation_data)) {
                  return true;
               }

               return false;
            }
         ]
      ];

      return $pages;
   }

   public function meta_tags()
   {
      $template = \get_query_var('virtual_page');

      if (empty($template)) {
         return;
      }

      $meu_bastidor_pages = ['login', 'cadastre-se', 'minhas-preferencias', 'confirmacao'];

      if (in_array($template, $meu_bastidor_pages)) {
         echo '<meta name="robots" content="noindex, nofollow">' . PHP_EOL;
      }
   }

   public function set_title($title)
   {
      $pages = apply_filters('bx_sntls_virtual_pages_list', []);

      if (empty($pages)) {
         return $title;
      }

      $template = get_query_var('virtual_page');

      foreach ($pages as $page) {
         if ($page['filename'] !== $template) {
            continue;
         }

         $new_title = $page['title'] . ' – ' . get_bloginfo('name');

         if (is_array($title)) {
            return [
               'title' => $new_title,
            ];
         }

         return $new_title;
      }

      return $title;
   }

   public function access_control()
   {
      $template = \get_query_var('virtual_page');

      if (empty($template)) {
         return;
      }

      $virtual_pages = apply_filters('bx_sntls_virtual_pages_list', []);

      $current_page = null;

      foreach ($virtual_pages as $page) {
         if ($page['filename'] === $template) {
            $current_page = $page;
            break;
         }
      }

      if (empty($current_page)) {
         return;
      }

      $redirect = $current_page['redirect'];

      if (empty($redirect)) {
         return;
      }

      if (!is_callable($redirect['condition'] ?? null)) {
         return;
      }

      if ($redirect['condition']()) {

         if ($redirect['force_404'] ?? false) {
            status_header(404);
            nocache_headers();
            include(get_query_template('404'));
            exit;
         }

         if ($redirect['url'] ?? false) {
            wp_safe_redirect(home_url($redirect['url']));
            exit;
         }
      }
   }
}

new Virtual_Pages();
