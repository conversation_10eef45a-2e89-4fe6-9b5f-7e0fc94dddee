<?php

if (!defined('ABSPATH')) {
   exit;
}

function bx_obastidor_get_social_networks() {
    return [
        'bluesky'   => 'Bluesky',
        'facebook'  => 'Facebook',
        'instagram' => 'Instagram',
        'linkedin'  => 'LinkedIn',
        'threads'   => 'Threads',
        'whatsapp' => 'WhatsApp',
        'x'         => 'X',
   ];
}

add_action('customize_register', 'bx_obastidor_customize_register_social_networks');
function bx_obastidor_customize_register_social_networks($wp_customize) {

    $wp_customize->add_section(
        'bx_obastidor_site_social_networks',
        [
            'title' => 'Links de Redes Sociais',
            'description' => 'URLs completas dos perfis, incluindo o protocolo (https://).',
            'active_callback' => '__return_true',
        ]
    );

    foreach (bx_obastidor_get_social_networks() as $network => $label) {
        $wp_customize->add_setting(
            "social_network_{$network}",
            [
                'default'           => '',
                'sanitize_callback' => 'esc_url_raw',
                'transport'         => 'refresh',
            ]
        );

        $wp_customize->add_control(
            "social_network_{$network}",
            [
                'label'       => esc_html__($label, 'bx-obastidor'),
                'section'     => 'bx_obastidor_site_social_networks',
                'type'        => 'url',
                'input_attrs' => [
                    'placeholder' => __('https://', 'text-domain'),
                ]
            ]
        );
    }
}