<?php

if (!defined('ABSPATH')) {
   exit;
}

require_once get_template_directory() . '/includes/generic/TGM_Plugin_Activation.php';

add_action('tgmpa_register', 'bx_obastidor_register_required_plugins');
function bx_obastidor_register_required_plugins()
{
   $plugins = [
      [
         'name'             => 'BX Essentials',
         'slug'             => 'bx-essentials',
         'required'         => true,
         'force_activation' => true,
      ],
   ];

   $config = array(
      'id'           => 'obastidor',
      'default_path' => '',
      'menu'         => 'tgmpa-install-plugins',
      'parent_slug'  => 'themes.php',
      'capability'   => 'edit_theme_options',
      'has_notices'  => true,
      'dismissable'  => true,
      'dismiss_msg'  => '',
      'is_automatic' => false,
      'message'      => '',
   );

   tgmpa($plugins, $config);
}
