<?php

namespace BX_Obastidor\BX_Taxonomy;

class BX_Taxonomy
{
   protected \WP_Term $term;

   public function __construct($term)
   {
      if (empty($term)) {
         return null;
      }

      $term = get_term($term);

      if (is_wp_error($term)) {
         return $term;
      }

      $this->term = $term;
   }

   public function get_the_ID()
   {
      return $this->term->term_id;
   }

   public function get_slug()
   {
      return $this->term->slug;
   }

   public function get_name()
   {
      return $this->term->name;
   }

   public function get_link()
   {
      return get_term_link($this->get_the_ID());
   }

   public function get_description()
   {
      return $this->term->description;
   }

   public function get_taxonomy()
   {
      return $this->term->taxonomy;
   }
}
