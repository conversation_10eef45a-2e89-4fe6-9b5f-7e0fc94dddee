<?php

namespace BX_Obastidor\AdBanner;

if (!defined('ABSPATH')) {
   exit;
}

class Utils
{
   public static function get_ad_banners_by_slot($slot, $override_query = [])
   {
      $slot_id = \get_term_by('slug', $slot, 'ad_banner_slot')->term_id;

      $default_query_args = [
         'post_type' => 'ad_banner',
         'status'    => 'publish',
         'orderby'   => 'date',
         'order'     => 'DESC',
         'tax_query' => [
            [
               'taxonomy' => 'ad_banner_slot',
               'field'    => 'term_id',
               'terms'    => $slot_id,
            ]
         ],
      ];

      return \get_posts(wp_parse_args($override_query, $default_query_args));
   }

   public static function get_random_ad_banner_by_slot($slot)
   {
      if (empty($slot)) {
         return null;
      }

      $term = \get_term_by('slug', $slot, 'ad_banner_slot');
      if (empty($term)) {
         throw new \Exception(sprintf(__('%s is not a valid slot', 'bx-obastidor'), $slot));
      }

      $slot_id = $term->term_id;

      $post = \get_posts([
         'post_type'      => 'ad_banner',
         'status'         => 'publish',
         'orderby'        => 'rand',
         'posts_per_page' => 1,
         'tax_query'      => [
            [
               'taxonomy' => 'ad_banner_slot',
               'field'    => 'term_id',
               'terms'    => $slot_id,
            ]
         ],
      ]);

      return count($post) > 0 ? new AdBanner($post[0]) : null;
   }

   public static function set_stats($request)
   {
      $banner_id = $request->get_param('banner_id');
      if (empty($banner_id)) {
         return \rest_ensure_response(
            new \WP_Error(
               'invalid_request',
               __('Banner ID is required', 'bx-obastidor'),
               ['status' => \WP_Http::BAD_REQUEST]
            )
         );
      }

      $event = $request->get_param('event');
      if (empty($event)) {
         return \rest_ensure_response(
            new \WP_Error(
               'invalid_request',
               __('Event is required', 'bx-obastidor'),
               ['status' => \WP_Http::BAD_REQUEST]
            )
         );
      }

      add_post_meta($banner_id, 'bx_obastidor_ad_banner_stats_' . $event, current_time('timestamp'));

      return \rest_ensure_response(
         [
            'success' => true,
         ]
      );
   }

   public static function get_clicks_count($banner_id)
   {
      return count(get_post_meta($banner_id, 'bx_obastidor_ad_banner_stats_click'));
   }

   public static function get_views_count($banner_id)
   {
      return count(get_post_meta($banner_id, 'bx_obastidor_ad_banner_stats_view'));
   }
}
