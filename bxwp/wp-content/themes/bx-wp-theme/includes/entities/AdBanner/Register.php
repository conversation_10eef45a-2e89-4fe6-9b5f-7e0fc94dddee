<?php

namespace BX_Obastidor\AdBanner;

if (!defined('ABSPATH')) {
   exit;
}

class Register
{
   const SIZES = [
      [
         'slug'   => 'half-banner',
         'name'   => 'Half Banner (468x60)',
         'width'  => 468,
         'height' => 60,
      ],
      [
         'slug'   => 'billboard',
         'name'   => 'Billboard (970x90)',
         'width'  => 970,
         'height' => 90,
      ],
      [
         'slug'   => 'halfpage',
         'name'   => 'Half Page (300x600)',
         'width'  => 300,
         'height' => 600,
      ],
      [
         'slug'   => 'square',
         'name'   => 'Square (300x250)',
         'width'  => 300,
         'height' => 250,
      ]
   ];

   const SLOTS = [
      [
         'slug'        => 'home-top',
         'name'        => 'Home: Topo da Página',
         'description' => 'Anúncio centralizado logo abaixo do cabeçalho, antes do conteúdo da página',
         'size'        => 'billboard'
      ],
      [
         'slug'        => 'home-maislidas-after',
         'name'        => 'Home: Abaixo das Mais Lidas',
         'description' => '<PERSON><PERSON><PERSON> sob a lista de notícias mais lidas da home',
         'size'        => 'halfpage'
      ],
      [
         'slug'        => 'newslist-shuffle',
         'name'        => 'Home: Listas de Notícias',
         'description' => 'Anúncios aleatórias entre as chamadas listadas na home',
         'size'        => 'half-banner',
         'default'     => true
      ],
      [
         'slug'        => 'section-top',
         'name'        => 'Editoria: Topo da Página',
         'description' => 'Anúncio na parte superior da página de seção',
         'size'        => 'billboard'
      ],
      [
         'slug'        => 'section-maislidas-after',
         'name'        => 'Editoria: Abaixo das Mais Lidas',
         'description' => 'Anúncio sob a lista de notícias mais lidas nas páginas de seção',
         'size'        => 'square'
      ],
      [
         'slug'        => 'section-newslist-shuffle',
         'name'        => 'Editoria: Lista de Notícia',
         'description' => 'Anúncios aleatórios entre as chamadas listadas nas páginas de seção',
         'size'        => 'half-banner'
      ],
      [
         'slug'        => 'news-top',
         'name'        => 'Notícia: Topo da Página',
         'description' => 'Anúncio na parte superior da página de notícias',
         'size'        => 'billboard'
      ],
      [
         'slug'        => 'news-sidebar',
         'name'        => 'Notícia: Lateral',
         'description' => 'Anúncio na lateral direita da página de notícias',
         'size'        => 'halfpage'
      ],
      [
         'slug'        => 'news-bottom',
         'name'        => 'Notícia: Abaixo do Conteúdo',
         'description' => 'Anúncio sob o conteúdo da notícia',
         'size'        => 'half-banner'
      ],
      [
         'slug'        => 'news-newslist-shuffle',
         'name'        => 'Notícia: Listas de Notícias',
         'description' => 'Anúncios aleatórios entre as chamadas listadas na notícia',
         'size'        => 'half-banner'
      ],
   ];

   public function __construct()
   {
      \add_action('init', [$this, 'register_post_type']);
      \add_action('init', [$this, 'register_ad_banner_size_taxonomy']);
      \add_action('init', [$this, 'register_ad_banner_slot_taxonomy']);
      \add_action('init', [$this, 'register_meta_fields']);
      \add_filter('bx_news_get_news_posts', [$this, 'inject_banner_ads_into_news'], 10, 2);
      \add_action('rest_api_init', [$this, 'register_rest_routes']);

      \add_filter('manage_ad_banner_posts_columns', [$this, 'add_stats_columns']);
      \add_filter('manage_edit-ad_banner_sortable_columns', [$this, 'make_stats_columns_sortable']);
      \add_action('manage_ad_banner_posts_custom_column', [$this, 'add_stats_columns_content'], 20, 2);
   }

   public function add_stats_columns($columns)
   {
      $columns['clicks'] = __('Cliques', 'bx-obastidor');
      $columns['views'] = __('Visualizações', 'bx-obastidor');
      return $columns;
   }

   public function make_stats_columns_sortable($columns)
   {
      $columns['clicks'] = 'clicks';
      $columns['views'] = 'views';
      return $columns;
   }

   public function add_stats_columns_content($column_name, $post_id)
   {
      if ($column_name === 'clicks') {
         echo Utils::get_clicks_count($post_id);
      }

      if ($column_name === 'views') {
         echo Utils::get_views_count($post_id);
      }
   }

   public function register_post_type()
   {
      $labels = [
         'name'          => __('Anúncios', 'bx-obastidor'),
         'singular_name' => __('Anúncio', 'bx-obastidor'),
         'all_items'     => __('Todos os Anúncios', 'bx-obastidor'),
      ];

      \register_post_type(
         'ad_banner',
         [
            'public'             => true,
            'publicly_queryable' => false,
            'labels'             => $labels,
            'show_in_rest'       => true,
            'supports'           => ['title', 'editor', 'custom-fields'],
            'menu_icon'          => 'dashicons-megaphone',
            'capability_type'    => 'ad_banner',
            'capabilities'       => [
               'edit_post'          => 'edit_others_posts',
               'read_post'          => 'edit_others_posts',
               'delete_post'        => 'edit_others_posts',
               'edit_posts'         => 'edit_others_posts',
               'edit_others_posts'  => 'edit_others_posts',
               'publish_posts'      => 'edit_others_posts',
               'read_private_posts' => 'edit_others_posts',
               'create_posts'       => 'edit_others_posts',
            ],
            'template'           => [
               [
                  'core/image',
                  [
                     'className' => 'ad-banner-image',
                     'align'     => 'center',
                     'scale'     => 'cover',
                  ]
               ],
            ],
            'template_lock' => 'contentOnly',
         ]
      );
   }

   public function register_ad_banner_size_taxonomy()
   {
      $args = [
         'labels' => [
            'name'          => __('Tamanhos', 'bx-obastidor'),
            'singular_name' => __('Tamanho', 'bx-obastidor'),
            'menu_name'     => __('Tamanhos', 'bx-obastidor'),
         ],
         'public'            => false,
         'hierarchical'      => true,
         'show_in_rest'      => true,
         'show_admin_column' => true,
      ];

      \register_taxonomy('ad_banner_size', ['ad_banner'], $args);

      foreach (self::SIZES as $size) {
         if (term_exists($size['slug'], 'ad_banner_size')) {
            $term = get_term_by('slug', $size['slug'], 'ad_banner_size');
         } else {
            $term = \wp_insert_term($size['name'], 'ad_banner_size', ['slug' => $size['slug']]);

            if (is_wp_error($term)) {
               wp_die(new \Exception('Error inserting term: ' . $term->get_error_message()));
            }
         }

         $current_height = get_term_meta($term->term_id, 'ad_banner_size_height', true);
         if (isset($size['height']) && (empty($current_height) || $current_height !== $size['height'])) {
            \update_term_meta($term->term_id, 'ad_banner_size_height', $size['height']);
         }

         $current_width = get_term_meta($term->term_id, 'ad_banner_size_width', true);
         if (isset($size['width']) && (empty($current_width) || $current_width !== $size['width'])) {
            \update_term_meta($term->term_id, 'ad_banner_size_width', $size['width']);
         }
      }
   }

   public function register_ad_banner_slot_taxonomy()
   {
      $args = [
         'labels' => [
            'name'          => __('Posições', 'bx-obastidor'),
            'singular_name' => __('Posição', 'bx-obastidor'),
            'menu_name'     => __('Posições', 'bx-obastidor'),
         ],
         'public'            => false,
         'hierarchical'      => true,
         'show_in_rest'      => true,
         'show_admin_column' => true,
      ];

      \register_taxonomy('ad_banner_slot', ['ad_banner'], $args);

      foreach (self::SLOTS as $slot) {
         if (term_exists($slot['slug'], 'ad_banner_slot')) {
            $term = get_term_by('slug', $slot['slug'], 'ad_banner_slot', OBJECT);
         } else {
            $term = \wp_insert_term($slot['name'], 'ad_banner_slot', ['slug' => $slot['slug']]);

            if (is_wp_error($term)) {
               wp_die(new \Exception('Error inserting term: ' . $term->get_error_message()));
            }
         }

         $current_size = get_term_meta($term->term_id, 'ad_banner_slot_size', true);
         if (isset($slot['size']) && (empty($current_size) || $current_size !== $slot['size'])) {
            \update_term_meta($term->term_id, 'ad_banner_slot_size', $slot['size']);
         }
      }
   }

   public function register_meta_fields()
   {
      \register_meta(
         'term',
         'ad_banner_size_height',
         [
            'object_subtype' => 'ad_banner_size',
            'show_in_rest'   => true,
            'single'         => true,
            'type'           => 'integer',
         ]
      );

      \register_meta(
         'term',
         'ad_banner_size_width',
         [
            'object_subtype' => 'ad_banner_size',
            'show_in_rest'   => true,
            'single'         => true,
            'type'           => 'integer',
         ]
      );

      \register_meta(
         'term',
         'ad_banner_slot_size',
         [
            'object_subtype' => 'ad_banner_slot',
            'show_in_rest'   => true,
            'single'         => true,
            'type'           => 'string',
         ]
      );

      \register_meta(
         'post',
         'bx_obastidor_unpublish_date',
         [
            'object_subtype' => 'ad_banner',
            'show_in_rest'   => true,
            'single'         => true,
            'type'           => 'string',
         ]
      );

      \register_meta(
         'post',
         'bx_obastidor_external_link',
         [
            'object_subtype' => 'ad_banner',
            'show_in_rest'   => true,
            'single'         => true,
            'type'           => 'string',
            'default'        => '',
         ]
      );
   }

   public function inject_banner_ads_into_news($posts, $query_info)
   {
      // Do not inject banners on featured queries
      if (isset($query_info['tax_query'])) {
         foreach ($query_info['tax_query'] as $tax_query) {
            if ($tax_query['taxonomy'] === 'featured-news-order') {
               if (empty($tax_query['operator']) || $tax_query['operator'] !== 'NOT IN') {
                  return $posts;
               }
            }
         }
      }

      // Get banner ads for the slot
      if (is_singular()) {
         $ad_banner_slot = 'news-newslist-shuffle';
      } elseif (is_category()) {
         $ad_banner_slot = 'section-newslist-shuffle';
      } else { // is_home
         $ad_banner_slot = 'newslist-shuffle';
      }

      $banner_ads = Utils::get_ad_banners_by_slot($ad_banner_slot);

      // Get the number of posts and banner ads
      $banner_ads_count = count($banner_ads);
      $posts_count = count($posts);

      // If there is no banner ads, return the posts
      if ($banner_ads_count === 0) {
         return $posts;
      }

      // For each banner ad select a random post for adding banner before it
      $banner_slots_indexes = range(0, $posts_count - 1);
      shuffle($banner_slots_indexes);
      $banner_slots = array_slice($banner_slots_indexes, 0, $banner_ads_count);

      // Inject the banner ads into the posts
      $posts_with_banner_ads = [];
      while (count($posts) > 0) {
         $index = isset($index) ? $index + 1 : 0;
         if (count($banner_ads) > 0 && in_array($index, $banner_slots)) {
            $posts_with_banner_ads[] = array_shift($banner_ads);
         }
         $posts_with_banner_ads[] = array_shift($posts);
      }

      // Return the posts with the banner ads injected
      return $posts_with_banner_ads;
   }

   public function register_rest_routes()
   {
      register_rest_route('bx-obastidor/v1', 'ad-banner/stats', [
         'methods'  => 'POST',
         'callback' => [Utils::class, 'set_stats'],
         'permission_callback' => [\BX_Obastidor\Utils\Utils::class, 'check_rest_nonce'],
         'args'     => [
            'banner_id' => [
               'required' => true
            ],
            'event' => [
               'required' => true
            ]
         ]
      ]);
   }
}
