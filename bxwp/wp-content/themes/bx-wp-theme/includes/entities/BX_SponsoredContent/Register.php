<?php

namespace BX_Obastidor\BX_SponsoredContent;

if (!defined('ABSPATH')) {
   exit;
}

class Register
{
   public function __construct()
   {
      \add_action('init', [$this, 'register_post_type']);
      \add_action('init', [$this, 'register_taxonomy']);
      \add_action('acf/init', [$this, 'register_acf_fields']);
      \add_action('acf/meta_box_load', [$this, 'register_taxonomy_fields']);
      \add_action('acf/save_post', [$this, 'sync_start_date_with_post_date'], 20);
      \add_filter('acf/load_value/name=bx_obastidor_sponsored_content_start_date', [$this, 'load_start_date_from_post_date'], 10, 3);
      \add_action('acf/render_field/type=date_time_picker', [$this, 'modify_date_time_picker_html'], 100, 1);
      \add_filter('bx_news_get_featured', [$this, 'inject_sponsored_content_into_news'], 10, 4);
      \add_action('save_post', [$this, 'schedule_unpublish_cron'], 10, 1);
      \add_filter('bx_news_get_post_object', [$this, 'get_sponsored_content_post_object'], 10, 2);
   }

   public function register_post_type()
   {
      $labels = [
         'name'          => __('Conteúdo Patrocinado', 'bx-obastidor'),
         'singular_name' => __('Conteúdo Patrocinado', 'bx-obastidor'),
         'menu_name'     => __('Conteúdo Patrocinado', 'bx-obastidor'),
         'all_items'     => __('Todos os Conteúdos Patrocinados', 'bx-obastidor'),
      ];

      \register_post_type(
         'sponsored_content',
         [
            'public'       => true,
            'labels'       => $labels,
            'show_in_rest' => true,
            'supports'     => ['title', 'editor', 'thumbnail', 'excerpt'],
            'menu_icon'    => 'dashicons-tag',
         ]
      );
   }

   public function register_taxonomy()
   {
      $args = [
         'labels' => [
            'name'          => __('Nível de Destaque', 'bx-obastidor'),
            'singular_name' => __('Nível de Destaque', 'bx-obastidor'),
            'menu_name'     => __('Destaque', 'bx-obastidor'),
         ],
         'capabilities' => [
            'manage_terms' => 'manage_featured_news_order',
            'edit_terms'   => 'manage_featured_news_order',
            'delete_terms' => 'manage_featured_news_order',
            'assign_terms' => 'edit_posts',
         ],
         'public'            => true,
         'hierarchical'      => true,
         'show_ui'           => true,
         'show_in_menu'      => false,
         'show_in_rest'      => true,
         'show_admin_column' => false,
         'meta_box_cb'       => false,
         'meta_box_sanitize_cb' => [$this, 'sanitize_featured_news_order'],
      ];

      \register_taxonomy('featured-news-order', ['sponsored_content'], $args);
   }

   public function register_taxonomy_fields()
   {
      $featured_news_order_tax_terms = \get_terms([
         'taxonomy' => 'featured-news-order',
         'hide_empty' => false,
         'fields' => 'all'
      ], OBJECT);

      $featured_news_order_choices = [
         'news-list' => __('Lista de Notícias', 'bx-obastidor'),
      ];

      foreach ($featured_news_order_tax_terms as $term) {
         $featured_news_order_choices[$term->slug] = $term->name;
      }

      \acf_add_local_field_group([
         'key'    => 'group_sponsored_content_validity',
         'title'  => esc_html__('Vigência', 'bx-obastidor'),
         'fields' => [
            [
               'key'           => 'field_sponsored_content_featured_news_order',
               'label'         => esc_html__('Nível de Destaque', 'bx-obastidor'),
               'name'          => 'bx_obastidor_sponsored_content_featured_news_order',
               'type'          => 'checkbox',
               'instructions'  => esc_html__('Onde exibir o conteúdo patrocinado.', 'bx-obastidor'),
               'choices'       => $featured_news_order_choices,
               'default_value' => ['news-list'],
            ],
         ],
      ]);
   }

   public function register_acf_fields()
   {
      if (!function_exists('acf_add_local_field_group')) {
         return;
      }

      \acf_add_local_field_group([
         'key'    => 'group_sponsored_content_validity',
         'title'  => esc_html__('Vigência', 'bx-obastidor'),
         'fields' => [
            [
               'key'           => 'field_sponsored_content_sponsor',
               'label'         => esc_html__('Anunciante', 'bx-obastidor'),
               'name'          => 'bx_obastidor_sponsored_content_sponsor',
               'type'          => 'text',
               'instructions'  => esc_html__('Nome do anunciante do conteúdo patrocinado.', 'bx-obastidor'),
               'default_value' => esc_html__('Conteúdo Patrocinado', 'bx-obastidor'),
               'placeholder'   => esc_html__('Conteúdo Patrocinado', 'bx-obastidor'),
            ]
         ],
         'location' => [
            [
               [
                  'param'    => 'post_type',
                  'operator' => '==',
                  'value'    => 'sponsored_content',
               ],
            ],
         ],
         'menu_order'            => 0,
         'position'              => 'side',
         'style'                 => 'default',
         'label_placement'       => 'top',
         'instruction_placement' => 'label',
         'hide_on_screen'        => '',
         'active'                => true,
         'description'           => '',
         'show_in_rest'          => 0,
      ]);
   }

   public function sync_start_date_with_post_date($post_id)
   {
      if (get_post_type($post_id) !== 'sponsored_content') {
         return;
      }

      $start_date = get_field('bx_obastidor_sponsored_content_start_date', $post_id);

      if (!$start_date) {
         return;
      }

      $post_data = [
         'ID' => $post_id,
         'post_date' => $start_date,
         'post_date_gmt' => get_gmt_from_date($start_date),
      ];

      wp_update_post($post_data);
   }

   public function load_start_date_from_post_date($value, $post_id, $field)
   {
      if (get_post_type($post_id) !== 'sponsored_content' || !empty($value)) {
         return $value;
      }

      $post = get_post($post_id);

      return $post ? $post->post_date : $value;
   }

   public function modify_date_time_picker_html($field)
   {

      if (get_post_type() !== 'sponsored_content') {
         return $field;
      }

      if (empty($field['placeholder']) || $field['type'] !== 'date_time_picker') {
         return $field;
      }

?>
      <script>
         document.addEventListener('DOMContentLoaded', function() {
            let setInputFieldPlaceholderTimeout = false;

            function setInputFieldPlaceholder() {
               const inputField = document.querySelector('<?php echo "#acf-{$field['key']} ~ .hasDatepicker"; ?>');
               if (!inputField) {
                  setInputFieldPlaceholderTimeout = setTimeout(setInputFieldPlaceholder, 100);
                  return false;
               }
               clearTimeout(setInputFieldPlaceholderTimeout);
               inputField.setAttribute('placeholder', '<?php echo $field['placeholder']; ?>');
            }
            setInputFieldPlaceholder();
         });
      </script>
<?php

      return $field;
   }

   public function inject_sponsored_content_into_news($posts, $featured_label, $query_info, $query)
   {
      // prepare sponsored content query
      $sponsored_content_query_args = [
         'post_type' => 'sponsored_content', // get sponsored_content posts
         'posts_per_page' => count($posts), // limit results to the number of queried posts
      ];

      $tax_query = $query->query_vars['tax_query']; // get tax_query from the main query

      if (!empty($tax_query)) {
         $sponsored_content_query_args['tax_query'] = $tax_query; // if query has no tax_query
      }

      $sponsored_content_posts = (new \WP_Query($sponsored_content_query_args))->posts;

      $max_posts = isset($query_info['posts_per_page']) ? intval($query_info['posts_per_page']) : -1;

      if (count($sponsored_content_posts) === 0) {
         return $posts;
      }

      if (count($posts) === 0) {
         return array_slice($sponsored_content_posts, 0, $max_posts);
      }

      $sponsored_content_slots = range(0, count($posts) - 1);
      shuffle($sponsored_content_slots);
      $sponsored_content_slots = array_slice(
         $sponsored_content_slots,
         0,
         count($sponsored_content_posts)
      );

      $posts_with_sponsored_content = [];

      foreach ($posts as $post_index => $post) {
         if (count($sponsored_content_posts) > 0 && in_array($post_index, $sponsored_content_slots)) {
            $posts_with_sponsored_content[] = array_shift($sponsored_content_posts);
         }
         if (count($posts_with_sponsored_content) < $max_posts) {
            $posts_with_sponsored_content[] = $post;
         }
      }

      return $posts_with_sponsored_content;
   }

   public function schedule_unpublish_cron($post_id)
   {
      if (get_post_type($post_id) !== 'sponsored_content') {
         return;
      }

      $end_date = get_post_meta($post_id, 'bx_obastidor_unpublish_date', true);
      if (!$end_date) {
         return;
      }

      $end_timestamp = strtotime($end_date);

      if ($end_timestamp < time()) {
         wp_update_post([
            'ID' => $post_id,
            'post_status' => 'draft'
         ]);
         return;
      }

      while ($next_timestamp = wp_next_scheduled('bx_sponsored_content_unpublish_expired', [$post_id])) {
         wp_unschedule_event($next_timestamp, 'bx_sponsored_content_unpublish_expired', [$post_id]);
      }

      wp_schedule_single_event($end_timestamp, 'bx_sponsored_content_unpublish_expired', [$post_id]);
   }

   /**
    * Force terms being an array of ints (save_post hook needs this)
    */
   public function sanitize_featured_news_order($taxonomy, $terms)
   {
      $sanitized_term_ids = [];
      foreach ((array)$terms as $term) {
         if (!empty($term)) {
            $sanitized_term_ids[] = intval($term);
         }
      }

      return $sanitized_term_ids;
   }

   public function register_meta_fields()
   {
      \register_meta(
         'post',
         'bx_obastidor_unpublish_date',
         [
            'object_subtype' => 'sponsored_content',
            'show_in_rest' => true,
            'single' => true,
            'type' => 'string',
         ]
      );
   }

   public function get_sponsored_content_post_object($PostObject, $post)
   {
      if ($post->post_type === 'sponsored_content') {
         return new BX_SponsoredContent($post);
      }

      return $PostObject;
   }
}
