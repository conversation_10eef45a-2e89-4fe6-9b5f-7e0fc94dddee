<?php

namespace BX_Obastidor\BX_SponsoredContent;

if (!defined('ABSPATH')) {
   exit;
}

/**
 * Sponsored Content
 *
 * Regular posts with hability to be scheduled to publishing and unpublishing.
 */
class BX_SponsoredContent extends \BX_Obastidor\BX_News\BX_News
{

   /**
    * Unpublish the post.
    */
   public function unpublish()
   {
      $this->post->post_status = 'draft';
      \wp_update_post($this->post);
   }

   /**
    * Get the sponsor name.
    *
    * @return string
    */
   public function get_sponsor_name()
   {
      return \get_field('bx_obastidor_sponsored_content_sponsor', $this->ID) ?? 'Conteúdo Patrocinado';
   }

   /**
    * Get the sponsor link.
    *
    * @return string
    */
   public function get_sponsor_link()
   {
      return \get_field('bx_obastidor_sponsored_content_external_link', $this->ID);
   }

   /**
    * Get the post link honoring external link if exists.
    *
    * @return string
    */
   public function get_link()
   {
      return $this->get_sponsor_link() ?: get_the_permalink();
   }
}
