<?php

namespace BX_Obastidor\Newsletter;

class Newsletter extends \BX_Obastidor\BX_Post\BX_Post
{
   public function send()
   {
      $recipients = (array) $this->get_meta('newsletter_recipients');

      $newsletter_headers = [
         'X-BX-OBASTIDOR-MAIL-TYPE: manual-newsletter',
         'X-BX-OBASTIDOR-NEWSLETTER-ID: ' . (string) $this->ID,
      ];

      return \wp_mail(
         implode(',', $recipients),
         $this->get_subject(),
         $this->post->post_content,
         $newsletter_headers
      );
   }

   public function get_subject()
   {
      return $this->post->post_title;
   }

   public function get_status()
   {
      // TODO: implement status from aws lambda
      return null;
   }

   public function update_content($content)
   {
      return \wp_update_post([
         'ID'           => $this->ID,
         'post_content' => $content,
      ]);
   }
}
