<?php

namespace BX_Obastidor\Newsletter;

if (!defined('ABSPATH')) {
   exit;
}

class Register
{
   function __construct()
   {
      add_action('init', [$this, 'register_post_type']);
      add_action('admin_init', [$this, 'set_subscriber_status']);
      add_action('admin_menu', [$this, 'add_custom_subpages']);
      add_action('rest_api_init', [$this, 'register_routes']);
   }

   public function register_post_type()
   {
      register_post_type('newsletter', [
         'labels' => [
            'name'          => __('Newsletters', 'bx-obastidor'),
            'singular_name' => __('Newsletter', 'bx-obastidor'),
         ],
         'public'           => false,
         'show_in_rest'     => true,
         'supports'         => ['title', 'editor', 'custom-fields'],
         'rewrite'          => false,
         'can_export'       => true,
         'delete_with_user' => false,
         'hierarchical'     => false,
      ]);
   }

   public function add_custom_subpages()
   {
      add_menu_page(
         __('Newsletter', 'bx-obastidor'),
         __('Newsletter', 'bx-obastidor'),
         'manage_options',
         'newsletter',
         [$this, 'render_send_log_page'],
         'dashicons-email-alt',
         30
      );

      add_submenu_page(
         'newsletter',
         __('Inscritos da Newsletter', 'bx-obastidor'),
         __('Inscritos', 'bx-obastidor'),
         'manage_options',
         'newsletter-subscribers',
         [$this, 'render_subscribers_page']
      );
   }

   public function register_routes()
   {
      register_rest_route('bx-obastidor/v1', '/subscribers', [
         'methods' => 'GET',
         'callback' => [$this, 'get_subscribers_callback'],
         'permission_callback' => function () {
            return current_user_can('manage_options');
         },
      ]);

      register_rest_route('bx-obastidor/v1', '/send-newsletter', [
         'methods' => 'POST',
         'callback' => [Utils::class, 'send_newsletter_callback'],
         'permission_callback' => function () {
            return current_user_can('manage_options');
         },
      ]);

      register_rest_route('bx-obastidor/v1', '/mail-recepient-list', [
         'methods' => 'GET',
         'callback' => [$this, 'get_recepient_list_callback'],
         'permission_callback' => function () {
            return current_user_can('manage_options');
         },
         'args' => [
            'list_name' => [
               'type'     => 'string',
               'required' => false,
            ],
         ],
      ]);

      register_rest_route('bx-obastidor/v1', '/newsletters/(?P<name>[-\w]+)', [
         'methods' => 'GET',
         'callback' => [$this, 'get_newsletter_callback'],
         'permission_callback' => function ($request) {
            return $request->get_header('X-Obastidor-Api-Token') === BX_OBASTIDOR_API_TOKEN;
         },
      ]);
   }

   public function render_send_log_page()
   {

?>
      <div class="wrap">
         <h1 class="wp-heading-inline"><?php esc_attr_e('Envios da Newsletter', 'bx-obastidor'); ?></h1>
         <hr class="wp-header-end">
         <?php

         $table = new Newsletter_Log_Table();
         $table->prepare_items();
         $table->display();

         ?>
      </div>
   <?php

   }

   public function render_subscribers_page()
   {

   ?>
      <div class="wrap">
         <h1 class="wp-heading-inline"><?php esc_attr_e('Inscritos na Newsletter', 'bx-obastidor'); ?></h1>
         <hr class="wp-header-end">
         <table class="wp-list-table widefat fixed striped" style="margin-top: 45px;">
            <thead>
               <tr>
                  <th><?php esc_attr_e('E-mail', 'bx-obastidor'); ?></th>
                  <th><?php esc_attr_e('Listas', 'bx-obastidor'); ?></th>
                  <th><?php esc_attr_e('Data de Inscrição', 'bx-obastidor'); ?></th>
                  <th><?php esc_attr_e('Status', 'bx-obastidor'); ?></th>
                  <th><?php esc_attr_e('Ações', 'bx-obastidor'); ?></th>
               </tr>
            </thead>
            <tbody>
               <?php

               foreach (Utils::get_subscribers(['status' => 'all']) as $subscriber) {
                  $nonce = wp_create_nonce('bx_obastidor_set_subscriber_status');

                  $is_subscribed = Utils::get_subscriber_status($subscriber->ID) === 'subscribed';

                  $action        = $is_subscribed ? 'unsubscribe' : 'subscribe';
                  $action_url    = admin_url('/admin.php?page=newsletter-subscribers&action=' . $action . '&subscriber_id=' . $subscriber->ID . '&nonce=' . $nonce);
                  $action_text   = $is_subscribed ? __('Desativar', 'bx-obastidor') : __('Ativar', 'bx-obastidor');

                  $status_icon   = $is_subscribed ? 'dashicons-yes-alt' : 'dashicons-no-alt';
                  $status_color  = $is_subscribed ? '#46b450' : '#dc3232';
                  $status_title  = $is_subscribed ? __('Inscrito', 'bx-obastidor') : __('Não inscrito', 'bx-obastidor');

               ?>
                  <tr>
                     <td><a href="<?php echo admin_url('user-edit.php?user_id=' . $subscriber->ID); ?>"><?php echo $subscriber->user_email; ?></a></td>
                     <td><?php echo implode(', ', Utils::get_subscriber_newsletters($subscriber->ID, 'name')); ?></td>
                     <td><?php echo wp_date('d/m/Y H:i', strtotime($subscriber->user_registered)); ?></td>
                     <td>
                        <span class="dashicons <?php echo $status_icon; ?>" style="color: <?php echo $status_color; ?>;" title="<?php echo $status_title; ?>"></span>
                     </td>
                     <td>
                        <a href="<?php echo $action_url; ?>" class="button button-small"><?php echo $action_text; ?></a>
                     </td>
                  </tr>
               <?php

               }

               ?>
            </tbody>
         </table>
      </div>
<?php

   }

   public function get_recepient_list_callback($request)
   {
      $list_name = $request->get_param('list_name');
      $recipients = Utils::get_subscribers(['list_name' => $list_name, 'return_type' => 'email']);

      return rest_ensure_response([
         'success' => true,
         'data'    => $recipients,
      ]);
   }

   public function get_newsletter_callback($request)
   {
      $name = $request->get_param('name');

      $post_ids = Utils::get_newsletter_post_ids($name);

      if (empty($post_ids)) {
         $error_message = is_null($post_ids)
            ? __('Newsletter inválida', 'bx-obastidor')
            : __('Não há posts para enviar a newsletter', 'bx-obastidor');

         return rest_ensure_response(
            new \WP_Error('invalid_newsletter', $error_message, ['status' => \WP_Http::FORBIDDEN])
         );
      }

      $recipients = Utils::get_subscribers(['list_name' => $name, 'return_type' => 'email']);

      if (empty($recipients)) {
         return rest_ensure_response(
            new \WP_Error('no_recipients', __('Não há inscritos para enviar a newsletter', 'bx-obastidor'), ['status' => \WP_Http::FORBIDDEN])
         );
      }

      $content = Utils::get_newsletter_post_list_content($post_ids);

      if (empty($content)) {
         return rest_ensure_response(
            new \WP_Error('error_creating_newsletter', __('Erro ao criar a newsletter', 'bx-obastidor'), ['status' => \WP_Http::INTERNAL_SERVER_ERROR])
         );
      }

      $subject = Utils::get_newsletters_lists('title')[$name];

      $Newsletter = Utils::create_newsletter(
         [
            'content'    => $content,
            'subject'    => Utils::get_newsletters_lists('title')[$name],
            'recipients' => $recipients,
            'type'       => $name,
         ]
      );

      if (is_wp_error($Newsletter)) {
         return rest_ensure_response(
            new \WP_Error('error_creating_newsletter', __('Erro ao criar a newsletter', 'bx-obastidor'), ['status' => \WP_Http::INTERNAL_SERVER_ERROR])
         );
      }

      return rest_ensure_response([
         'success' => true,
         'data'    => [
            'newsletter_id' => $Newsletter->get_the_ID(),
            'type'          => 'newsletter',
            'subject'       => $subject,
            'recipients'    => $recipients,
            'content'       => $content,
         ],
      ]);
   }

   public function get_subscribers_callback($request)
   {
      $list_name = $request->get_param('list_name');
      $return_type = $request->get_param('return_type') ?? 'email';

      $subscribers = Utils::get_subscribers(compact('list_name', 'return_type'));

      return rest_ensure_response([
         'success' => true,
         'data'    => $subscribers,
      ]);
   }

   public function set_subscriber_status()
   {
      if (!isset($_GET['action']) || !isset($_GET['subscriber_id']) || !isset($_GET['nonce'])) {
         return;
      }

      $nonce = $_GET['nonce'];
      $action = $_GET['action'];
      $subscriber_id = (int) $_GET['subscriber_id'];

      if (!current_user_can('manage_options')) {
         wp_die(__('Você não tem permissão para executar esta ação.', 'bx-obastidor'));
      }

      if (!wp_verify_nonce($nonce, 'bx_obastidor_set_subscriber_status')) {
         wp_die(__('Link de segurança inválido.', 'bx-obastidor'));
      }

      if (!$subscriber_id || !get_user_by('ID', $subscriber_id)) {
         wp_die(__('Usuário inválido.', 'bx-obastidor'));
      }

      $status = $action === 'subscribe' ? 'subscribed' : 'unsubscribed';
      update_user_meta($subscriber_id, 'bx_obastidor_subscriber_status', $status);

      $redirect_url = remove_query_arg(['action', 'subscriber_id', 'nonce'], $_SERVER['REQUEST_URI']);
      wp_redirect($redirect_url);
      exit;
   }
}
