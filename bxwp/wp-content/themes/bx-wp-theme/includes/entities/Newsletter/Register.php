<?php

namespace BX_Obastidor\Newsletter;

if (!defined('ABSPATH')) exit;

class Register
{
   function __construct()
   {
      add_action('init', [$this, 'register_pages']);
      add_action('init', [$this, 'register_newsletter_options_fields']);
   }

   public function register_pages()
   {
      if (!function_exists('acf_add_options_page')) {
         return;
      }

      acf_add_options_page([
         'page_title'  => __('Configurações da Newsletter', 'bx-obastidor'),
         'menu_title'  => __('Newsletter', 'bx-obastidor'),
         'menu_slug'   => 'newsletter-settings',
         'capability'  => 'manage_options',
         'icon_url'    => 'dashicons-email-alt',
         'position'    => 30,
         'redirect'    => false,
      ]);
   }

   public function register_newsletter_options_fields()
   {
      if (!function_exists('acf_add_local_field_group')) {
         return;
      }

      acf_add_local_field_group([
         'key'    => 'group_newsletter',
         'title'  => __('Newsletter', 'bx-obastidor'),
         'fields' => [
            // Newsletter Semanal
            [
               'key'               => 'field_newsletter_weekly_tab',
               'label'             => __('Newsletter Semanal', 'bx-obastidor'),
               'name'              => '',
               'type'              => 'tab',
               'instructions'      => '',
               'required'          => 0,
               'conditional_logic' => 0,
               'placement'         => 'top',
               'endpoint'          => 0,
            ],
            [
               'key'               => 'field_newsletter_weekly_active',
               'label'             => __('Ativar Newsletter Semanal', 'bx-obastidor'),
               'name'              => 'newsletter_weekly_active',
               'type'              => 'true_false',
               'instructions'      => __('Ative para enviar newsletter semanal automaticamente', 'bx-obastidor'),
               'required'          => 0,
               'conditional_logic' => 0,
               'default_value'     => 0,
               'ui'                => 1,
               'ui_on_text'        => __('Ativo', 'bx-obastidor'),
               'ui_off_text'       => __('Inativo', 'bx-obastidor'),
            ],
            [
               'key'               => 'field_newsletter_weekly_quantity',
               'label'             => __('Quantidade de Posts', 'bx-obastidor'),
               'name'              => 'newsletter_weekly_quantity',
               'type'              => 'number',
               'instructions'      => __('Número de posts a serem incluídos na newsletter semanal', 'bx-obastidor'),
               'required'          => 0,
               'conditional_logic' => [
                  [
                     [
                        'field'    => 'field_newsletter_weekly_active',
                        'operator' => '==',
                        'value'    => '1',
                     ],
                  ],
               ],
               'default_value'     => 5,
               'placeholder'       => '',
               'prepend'           => '',
               'append'            => __('posts', 'bx-obastidor'),
               'min'               => 1,
               'max'               => 50,
               'step'              => 1,
            ],
            [
               'key'               => 'field_newsletter_weekly_date',
               'label'             => __('Dia da Semana', 'bx-obastidor'),
               'name'              => 'newsletter_weekly_date',
               'type'              => 'date_time_picker',
               'instructions'      => __('Selecione o dia da semana e horário para envio', 'bx-obastidor'),
               'required'          => 0,
               'conditional_logic' => [
                  [
                     [
                        'field'    => 'field_newsletter_weekly_active',
                        'operator' => '==',
                        'value'    => '1',
                     ],
                  ],
               ],
               'default_value'     => '',
               'allow_null'        => 0,
               'multiple'          => 0,
               'ui'                => 1,
               'return_format'     => 'value',
               'ajax'              => 0,
               'placeholder'       => '',
            ],

            // Newsletter Diária
            [
               'key'               => 'field_newsletter_daily_tab',
               'label'             => __('Newsletter Diária', 'bx-obastidor'),
               'name'              => '',
               'type'              => 'tab',
               'instructions'      => '',
               'required'          => 0,
               'conditional_logic' => 0,
               'placement'         => 'top',
               'endpoint'          => 0,
            ],
            [
               'key'               => 'field_newsletter_daily_active',
               'label'             => __('Ativar Newsletter Diária', 'bx-obastidor'),
               'name'              => 'newsletter_daily_active',
               'type'              => 'true_false',
               'instructions'      => __('Ative para enviar newsletter diária automaticamente', 'bx-obastidor'),
               'required'          => 0,
               'conditional_logic' => 0,
               'default_value'     => 0,
               'ui'                => 1,
               'ui_on_text'        => __('Ativo', 'bx-obastidor'),
               'ui_off_text'       => __('Inativo', 'bx-obastidor'),
            ],
            [
               'key'               => 'field_newsletter_daily_quantity',
               'label'             => __('Quantidade de Posts', 'bx-obastidor'),
               'name'              => 'newsletter_daily_quantity',
               'type'              => 'number',
               'instructions'      => __('Número de posts a serem incluídos na newsletter diária', 'bx-obastidor'),
               'required'          => 0,
               'conditional_logic' => [
                  [
                     [
                        'field'    => 'field_newsletter_daily_active',
                        'operator' => '==',
                        'value'    => '1',
                     ],
                  ],
               ],
               'default_value'     => 3,
               'placeholder'       => '',
               'prepend'           => '',
               'append'            => __('posts', 'bx-obastidor'),
               'min'               => 1,
               'max'               => 20,
               'step'              => 1,
            ],
            [
               'key'               => 'field_newsletter_daily_time',
               'label'             => __('Horário de Envio', 'bx-obastidor'),
               'name'              => 'newsletter_daily_time',
               'type'              => 'time_picker',
               'instructions'      => __('Horário para envio da newsletter diária', 'bx-obastidor'),
               'required'          => 0,
               'conditional_logic' => [
                  [
                     [
                        'field'    => 'field_newsletter_daily_active',
                        'operator' => '==',
                        'value'    => '1',
                     ],
                  ],
               ],
               'display_format'    => 'H:i',
               'return_format'     => 'H:i',
               'default_value'     => '08:00',
            ],

            // Disparo Individual
            [
               'key'               => 'field_newsletter_manual_tab',
               'label'             => __('Disparo Individual', 'bx-obastidor'),
               'name'              => '',
               'type'              => 'tab',
               'instructions'      => '',
               'required'          => 0,
               'conditional_logic' => 0,
               'placement'         => 'top',
               'endpoint'          => 0,
            ],
            [
               'key'               => 'field_newsletter_manual_title',
               'label'             => __('Título da Newsletter', 'bx-obastidor'),
               'name'              => 'newsletter_manual_title',
               'type'              => 'text',
               'instructions'      => __('Título personalizado para esta newsletter', 'bx-obastidor'),
               'required'          => 0,
               'conditional_logic' => 0,
               'default_value'     => '',
               'placeholder'       => __('Ex: Edição Especial - Notícias da Semana', 'bx-obastidor'),
               'prepend'           => '',
               'append'            => '',
               'maxlength'         => 100,
            ],
            [
               'key'               => 'field_newsletter_manual_posts',
               'label'             => __('Selecionar Posts', 'bx-obastidor'),
               'name'              => 'newsletter_manual_posts',
               'type'              => 'post_object',
               'instructions'      => __('Selecione os posts que deseja incluir nesta newsletter', 'bx-obastidor'),
               'required'          => 0,
               'conditional_logic' => 0,
               'post_type'         => [
                  0 => 'post',
               ],
               'taxonomy'          => '',
               'allow_null'        => 0,
               'multiple'          => 1,
               'return_format'     => 'object',
               'ui'                => 1,
            ],
            [
               'key'               => 'field_newsletter_manual_send_button',
               'label'             => __('Enviar Newsletter', 'bx-obastidor'),
               'name'              => 'newsletter_manual_send_button',
               'type'              => 'button_group',
               'instructions'      => __('Clique para enviar esta newsletter imediatamente', 'bx-obastidor'),
               'required'          => 0,
               'conditional_logic' => [
                  [
                     [
                        'field'    => 'field_newsletter_manual_posts',
                        'operator' => '!=',
                        'value'    => '',
                     ],
                  ],
               ],
               'choices'           => [
                  'send_now' => __('📧 Enviar Agora', 'bx-obastidor'),
               ],
               'allow_null'        => 1,
               'default_value'     => '',
               'layout'            => 'horizontal',
               'return_format'     => 'value',
            ],

            // Filtros de Usuários
            [
               'key'               => 'field_newsletter_users_tab',
               'label'             => __('Filtros de Usuários', 'bx-obastidor'),
               'name'              => '',
               'type'              => 'tab',
               'instructions'      => '',
               'required'          => 0,
               'conditional_logic' => 0,
               'placement'         => 'top',
               'endpoint'          => 0,
            ],
            [
               'key'               => 'field_newsletter_user_roles',
               'label'             => __('Perfis de Usuário', 'bx-obastidor'),
               'name'              => 'newsletter_user_roles',
               'type'              => 'checkbox',
               'instructions'      => __('Selecione os perfis de usuário que receberão a newsletter', 'bx-obastidor'),
               'required'          => 0,
               'conditional_logic' => 0,
               'choices'           => [
                  'subscriber'    => __('Assinantes', 'bx-obastidor'),
                  'contributor'   => __('Colaboradores', 'bx-obastidor'),
                  'author'        => __('Autores', 'bx-obastidor'),
                  'editor'        => __('Editores', 'bx-obastidor'),
                  'administrator' => __('Administradores', 'bx-obastidor'),
               ],
               'allow_custom'      => 0,
               'default_value'     => ['subscriber'],
               'layout'            => 'vertical',
               'toggle'            => 0,
               'return_format'     => 'value',
               'save_custom'       => 0,
            ],
            [
               'key'               => 'field_newsletter_user_meta_filter',
               'label'             => __('Filtro por Meta Campo', 'bx-obastidor'),
               'name'              => 'newsletter_user_meta_filter',
               'type'              => 'group',
               'instructions'      => __('Configure filtros adicionais baseados em meta campos dos usuários', 'bx-obastidor'),
               'required'          => 0,
               'conditional_logic' => 0,
               'layout'            => 'block',
               'sub_fields'        => [
                  [
                     'key'               => 'field_newsletter_meta_key',
                     'label'             => __('Meta Key', 'bx-obastidor'),
                     'name'              => 'meta_key',
                     'type'              => 'text',
                     'instructions'      => __('Nome do meta campo (ex: newsletter_preference)', 'bx-obastidor'),
                     'required'          => 0,
                     'conditional_logic' => 0,
                     'default_value'     => '',
                     'placeholder'       => 'newsletter_preference',
                     'prepend'           => '',
                     'append'            => '',
                     'maxlength'         => '',
                  ],
                  [
                     'key'               => 'field_newsletter_meta_value',
                     'label'             => __('Meta Value', 'bx-obastidor'),
                     'name'              => 'meta_value',
                     'type'              => 'text',
                     'instructions'      => __('Valor do meta campo para filtrar usuários', 'bx-obastidor'),
                     'required'          => 0,
                     'conditional_logic' => 0,
                     'default_value'     => '',
                     'placeholder'       => 'yes',
                     'prepend'           => '',
                     'append'            => '',
                     'maxlength'         => '',
                  ],
               ],
            ],
         ],
         'location' => [
            [
               [
                  'param'    => 'options_page',
                  'operator' => '==',
                  'value'    => 'newsletter-settings',
               ],
            ],
         ],
         'menu_order'            => 0,
         'position'              => 'normal',
         'style'                 => 'default',
         'label_placement'       => 'top',
         'instruction_placement' => 'label',
         'hide_on_screen'        => '',
         'active'                => true,
         'description'           => '',
      ]);
   }

   /**
    * Helper functions to get newsletter settings
    */
   public static function is_weekly_active()
   {
      return (bool) get_field('newsletter_weekly_active', 'option');
   }

   public static function get_weekly_quantity()
   {
      return (int) get_field('newsletter_weekly_quantity', 'option') ?: 5;
   }

   public static function get_weekly_day()
   {
      return get_field('newsletter_weekly_day', 'option') ?: 'monday';
   }

   public static function get_weekly_time()
   {
      return get_field('newsletter_weekly_time', 'option') ?: '09:00';
   }

   public static function is_daily_active()
   {
      return (bool) get_field('newsletter_daily_active', 'option');
   }

   public static function get_daily_quantity()
   {
      return (int) get_field('newsletter_daily_quantity', 'option') ?: 3;
   }

   public static function get_daily_time()
   {
      return get_field('newsletter_daily_time', 'option') ?: '08:00';
   }

   public static function get_manual_title()
   {
      return get_field('newsletter_manual_title', 'option') ?: '';
   }

   public static function get_manual_posts()
   {
      return get_field('newsletter_manual_posts', 'option') ?: [];
   }

   public static function get_user_roles()
   {
      return get_field('newsletter_user_roles', 'option') ?: ['subscriber'];
   }

   public static function get_user_meta_filter()
   {
      return get_field('newsletter_user_meta_filter', 'option') ?: [];
   }
}
