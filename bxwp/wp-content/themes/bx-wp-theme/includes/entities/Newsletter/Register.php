<?php

namespace BX_Obastidor\Newsletter;

if (!defined('ABSPATH')) exit;

class Register
{
   function __construct()
   {
      add_action('init', [$this, 'register_pages']);
      add_action('init', [$this, 'register_newsletter_options_fields']);
   }

   public function register_pages()
   {
      if (!function_exists('acf_add_options_page')) {
         return;
      }

      acf_add_options_page([
         'page_title' => __('Newsletter', 'bx-obastidor'),
         'menu_title' => __('Newsletter', 'bx-obastidor'),
         'menu_slug'  => 'newsletter',
         'capability' => 'manage_options',
         'icon_url'   => 'dashicons-email-alt',
      ]);
   }

   public function register_newsletter_options_fields()
   {
      if (!function_exists('acf_add_local_field_group')) {
         return;
      }

      acf_add_local_field_group([
         'key'    => 'group_newsletter',
         'title'  => __('Newsletter', 'bx-obastidor'),
         'fields' => []
      ]);
   }
}
