# Sistema de Newsletter - Subpáginas

## 📋 Estrutura das Subpáginas

O sistema de newsletter foi organizado em **4 subpáginas** para melhor organização e usabilidade:

---

## 🏠 **Newsletter** (Página Principal)
- **Slug**: `newsletter`
- **Função**: Menu principal com redirecionamento
- **Redirect**: `true` (redireciona para primeira subpágina)

---

## ⚙️ **1. Configurações** 
- **Slug**: `newsletter-settings`
- **Função**: Configurações automáticas de envio
- **Campos ACF**: `group_newsletter_settings`

### 📅 **Newsletter Semanal**
- ✅ Ativo (true/false)
- ✅ Quantidade de posts (1-50)
- ✅ Dia da semana (select)
- ✅ Horário de envio (time picker)

### 📆 **Newsletter Diária**
- ✅ Ativo (true/false)
- ✅ Selecionar posts (post_object multi-select)
- ✅ Horário de envio (time picker)

---

## 📧 **2. Envios**
- **Slug**: `newsletter-sends`
- **Função**: Envios manuais e disparos individuais
- **Campos ACF**: `group_newsletter_sends`

### 🎯 **Envio Manual**
- ✅ Título personalizado (text)
- ✅ Selecionar posts (post_object multi-select)
- ✅ Botão "📧 Enviar Agora" (button_group)

---

## 📊 **3. Relatórios**
- **Slug**: `newsletter-reports`
- **Função**: Estatísticas e logs de envios
- **Campos ACF**: `group_newsletter_reports`

### 📈 **Informações**
- ✅ Mensagem informativa (message)
- ✅ Log dos últimos envios (textarea readonly)

---

## 👥 **4. Usuários**
- **Slug**: `newsletter-users`
- **Função**: Filtros e configurações de usuários
- **Campos ACF**: `group_newsletter_users`

### 🎯 **Filtros de Usuários**
- ✅ Perfis de usuário (checkbox)
  - Assinantes
  - Colaboradores
  - Autores
  - Editores
  - Administradores
- ✅ Filtro por meta campo (group)
  - Meta Key (text)
  - Meta Value (text)

---

## 🎨 **Interface do Usuário**

### **Menu WordPress:**
```
📧 Newsletter
├── ⚙️ Configurações
├── 📧 Envios  
├── 📊 Relatórios
└── 👥 Usuários
```

### **Fluxo de Uso:**
1. **Configurações**: Configure envios automáticos
2. **Usuários**: Defina quem recebe as newsletters
3. **Envios**: Faça disparos manuais quando necessário
4. **Relatórios**: Monitore performance e logs

---

## 🔧 **Implementação Técnica**

### **Páginas ACF:**
```php
// Página principal (com redirect)
acf_add_options_page([
   'menu_slug' => 'newsletter',
   'redirect'  => true
]);

// Subpáginas
acf_add_options_sub_page([
   'menu_slug'   => 'newsletter-settings',
   'parent_slug' => 'newsletter'
]);
```

### **Grupos de Campos:**
```php
// Cada subpágina tem seu próprio grupo
'group_newsletter_settings'  // Configurações
'group_newsletter_sends'     // Envios
'group_newsletter_reports'   // Relatórios  
'group_newsletter_users'     // Usuários
```

### **Location Rules:**
```php
'location' => [
   [
      [
         'param'    => 'options_page',
         'operator' => '==',
         'value'    => 'newsletter-settings', // Slug da subpágina
      ],
   ],
]
```

---

## 📱 **Responsividade e UX**

### **Organização Visual:**
- **Abas por funcionalidade** em cada subpágina
- **Conditional logic** para campos relacionados
- **Ícones visuais** para melhor identificação
- **Mensagens informativas** em cada seção

### **Fluxo Intuitivo:**
1. **Primeiro acesso**: Redireciona para Configurações
2. **Navegação**: Menu lateral com todas as opções
3. **Contexto**: Cada página focada em uma função específica
4. **Feedback**: Mensagens de sucesso/erro em cada ação

---

## 🚀 **Vantagens da Estrutura**

### **Organização:**
- ✅ **Separação clara** de responsabilidades
- ✅ **Interface limpa** sem sobrecarga de campos
- ✅ **Navegação intuitiva** entre funcionalidades
- ✅ **Escalabilidade** para futuras funcionalidades

### **Manutenibilidade:**
- ✅ **Código modular** com métodos separados
- ✅ **Campos organizados** por contexto
- ✅ **Fácil extensão** de novas subpáginas
- ✅ **Debug simplificado** por área

### **Usabilidade:**
- ✅ **Foco por tarefa** (configurar, enviar, monitorar)
- ✅ **Menos confusão** visual
- ✅ **Workflow natural** do usuário
- ✅ **Acesso rápido** às funções mais usadas

---

## 📋 **Como Usar**

### **1. Configuração Inicial:**
1. Acesse **Newsletter > Configurações**
2. Configure newsletters automáticas
3. Vá para **Newsletter > Usuários**
4. Defina filtros de usuários

### **2. Envio Manual:**
1. Acesse **Newsletter > Envios**
2. Digite título personalizado
3. Selecione posts específicos
4. Clique em "📧 Enviar Agora"

### **3. Monitoramento:**
1. Acesse **Newsletter > Relatórios**
2. Visualize logs de envios
3. Monitore estatísticas

---

## 🔮 **Futuras Expansões**

### **Possíveis Subpáginas:**
- **📝 Templates**: Gerenciar templates de email
- **📈 Analytics**: Estatísticas avançadas
- **🎨 Design**: Personalização visual
- **⚡ Automações**: Workflows avançados
- **📱 Integrations**: APIs externas

A estrutura modular permite fácil adição de novas funcionalidades! 🚀
