<?php

namespace BX_Obastidor\Newsletter;

require_once get_template_directory() . '/includes/composer/vendor/autoload.php';

use Aws\Lambda\LambdaClient;

class Newsletter_Log_Table extends \WP_List_Table
{
   private $newsletter_stats = [];

   public function __construct()
   {
      parent::__construct([
         'screen' => 'newsletter',
      ]);
   }

   public function get_columns()
   {
      return [
         'subject'     => __('Assunto', 'bx-obastidor'),
         'type'        => __('Tipo', 'bx-obastidor'),
         'date'        => __('Data', 'bx-obastidor'),
         'click_count' => __('Cliques', 'bx-obastidor'),
         'open_count'  => __('Aberturas', 'bx-obastidor'),
      ];
   }

   public function get_sortable_columns()
   {
      return [
         'date' => ['date', true],
      ];
   }

   public function prepare_items()
   {
      $per_page     = 20;
      $current_page = $this->get_pagenum();
      $total_items  = $this->get_total_items();

      $new_posts = get_posts([
         'post_type'      => 'newsletter',
         'posts_per_page' => $per_page,
         'paged'          => $current_page,
         'orderby'        => $_GET['orderby'] ?? 'date',
         'order'          => $_GET['order'] ?? 'DESC',
      ]);

      $this->set_stats($new_posts);

      $this->items = $new_posts;

      $this->set_pagination_args([
         'total_items'     => $total_items,
         'per_page'        => $per_page,
         'total_pages'     => ceil($total_items / $per_page),
         'infinite_scroll' => false,
      ]);
   }

   private function get_total_items()
   {
      $query = new \WP_Query([
         'post_type'      => 'newsletter',
         'posts_per_page' => -1,
         'fields'         => 'ids',
      ]);

      return $query->found_posts;
   }

   public function column_default($item, $column_name)
   {
      $Newsletter = new Newsletter($item);

      $newsletter_types = Utils::get_newsletters_lists();

      switch ($column_name) {
         case 'subject':
            return sprintf('<span style="font-weight: bold;">%s</span>', $Newsletter->get_subject());
         case 'date':
            return $Newsletter->get_date('d/m/Y H:i');
         case 'type':
            return $newsletter_types[$Newsletter->get_meta('newsletter_type')] ?? '';
         case 'click_count':
            return $this->get_stats($Newsletter->get_the_ID())['click_count'] ?? 0;
         case 'open_count':
            return $this->get_stats($Newsletter->get_the_ID())['open_count'] ?? 0;
      }
   }

   public function get_stats($newsletter_id = null)
   {
      return empty($newsletter_id)
         ? $this->newsletter_stats
         : $this->newsletter_stats[$newsletter_id] ?? [];
   }

   public function set_stats($posts)
   {
      if (empty(AWS_REGION) || empty(AWS_NEWSLETTER_METRICS_GETTER_ARN)) {
         return;
      }

      $lambdaClient = new LambdaClient([
         'region'  => AWS_REGION,
         'version' => '2015-03-31',
      ]);

      $parsed_args = [
         'newsletterIds' => array_map(function ($post) {
            return (string) $post->ID;
         }, $posts),
      ];

      $results = $lambdaClient->invoke([
         'FunctionName' => AWS_NEWSLETTER_METRICS_GETTER_ARN,
         'Payload'      => json_encode($parsed_args),
      ]);

      $response = json_decode($results['Payload'], true);

      $this->newsletter_stats = $response;
   }
}
