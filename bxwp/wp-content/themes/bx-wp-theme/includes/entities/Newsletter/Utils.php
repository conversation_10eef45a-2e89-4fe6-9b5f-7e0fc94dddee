<?php

namespace BX_Obastidor\Newsletter;

if (!defined('ABSPATH')) {
   exit;
}

use BX_Obastidor\News\News;
use BX_Obastidor\News\Utils as NewsUtils;

class Utils
{
   public static function get_newsletters_lists($info = 'title')
   {
      $lists = [
         [
            'slug'  => 'alerta',
            'title' => __('Alertas e notícias exclusivas', 'bx-bastidor'),
         ],
         [
            'slug'   => 'diario',
            'title'  => __('Destaques do dia', 'bx-bastidor'),
            'period' => 'midnight',
         ],
         [
            'slug'   => 'semanal',
            'title'  => __('Destaques da semana', 'bx-bastidor'),
            'period' => '1 week ago midnight',
         ],
      ];

      return array_column($lists, $info, 'slug');
   }

   public static function get_newsletter_post_ids($name)
   {
      $date_references = self::get_newsletters_lists('period');

      if (empty($date_references[$name])) {
         return null;
      }

      $most_readed_posts     = NewsUtils::get_most_readed_posts(15, $date_references[$name]);
      $most_readed_posts_ids = array_column($most_readed_posts, 'post_ID');

      $post_ids = get_posts([
         'post_type'   => 'post',
         'numberposts' => 15,
         'date_query'  => [
            [
               'after' => $date_references[$name],
            ]
         ],
         'fields' => 'ids',
      ]);

      // Reorder posts: popular first (keeping popularity order), then chronological
      $popular_posts   = array_intersect($most_readed_posts_ids, $post_ids);
      $remaining_posts = array_diff($post_ids, $popular_posts);

      return array_merge($popular_posts, $remaining_posts);
   }

   public static function get_subscribers($args = [])
   {
      $list_name   = $args['list_name'] ?? null;
      $return_type = $args['return_type'] ?? 'object';
      $status      = $args['status'] ?? 'subscribed';

      $query_list_names = $list_name
         ? [$list_name]
         : array_keys(self::get_newsletters_lists());

      $list_names_meta_query = ['relation' => 'OR'];

      foreach ($query_list_names as $current_list_name) {
         $list_names_meta_query[] = [
            'key'     => 'bx_obastidor_user_notifications',
            'value'   => $current_list_name,
            'compare' => 'LIKE',
         ];
      }

      $query_args = $status === 'all'
         ? [
            'meta_query' => [
               'key'     => 'bx_obastidor_subscriber_status',
               'compare' => 'EXISTS',
            ],
         ]
         : [
            'meta_query' => [
               'relation' => 'AND',
               [
                  'key'     => 'bx_obastidor_subscriber_status',
                  'value'   => $status,
                  'compare' => '=',
               ],
               $list_names_meta_query
            ]
         ];

      if ($return_type === 'ID') {
         $query_args['fields'] = 'ID';
      } else if ($return_type === 'email') {
         $query_args['fields'] = 'user_email';
      }

      $user_query = new \WP_User_Query($query_args);

      return $user_query->get_results();
   }

   // TODO: move to entity subscriber
   public static function update_subscriber_newsletters($user_id, $newsletters_lists)
   {
      if (!get_user_by('ID', $user_id)) {
         return new \WP_Error('user_not_found', __('Usuário não encontrado', 'bx-bastidor'), ['status' => \WP_Http::NOT_FOUND]);
      }

      if (!is_array($newsletters_lists)) {
         return new \WP_Error('invalid_newsletters_lists', __('Lista de notificações inválida', 'bx-bastidor'), ['status' => \WP_Http::BAD_REQUEST]);
      }

      $updated_newsletters_lists = [];

      foreach (array_keys(self::get_newsletters_lists()) as $list_name) {
         if (in_array($list_name, $newsletters_lists)) {
            $updated_newsletters_lists[] = $list_name;
         }
      }

      update_user_meta($user_id, 'bx_obastidor_user_notifications', $updated_newsletters_lists);

      return $updated_newsletters_lists;
   }

   // TODO: move to entity subscriber
   public static function get_subscriber_newsletters($user_id, $field = 'slug')
   {
      $user_newsletters = (array) get_user_meta($user_id, 'bx_obastidor_user_notifications', true);

      $response = [];

      foreach (self::get_newsletters_lists() as $slug => $name) {
         if (in_array($slug, $user_newsletters)) {
            $response[] = $field === 'slug' ? $slug : $name;
         }
      }

      return $response;
   }

   // TODO: move to entity subscriber
   public static function get_subscriber_status($user_id)
   {
      $status = get_user_meta($user_id, 'bx_obastidor_subscriber_status', true);

      return empty($status) ? 'unsubscribed' : $status;
   }

   public static function send_post_list_newsletter($post_ids, $recipients, $subject, $headers = [])
   {
      if (empty($post_ids)) {
         return new \WP_Error('no_post_ids', __('Nenhum ID de post fornecido', 'bx-bastidor'), ['status' => \WP_Http::BAD_REQUEST]);
      }

      if (empty($recipients)) {
         return new \WP_Error('no_recipient', __('Nenhum destinatário fornecido', 'bx-bastidor'), ['status' => \WP_Http::BAD_REQUEST]);
      }

      if (empty($subject)) {
         if (!is_array($post_ids)) {
            $post_ids = [$post_ids];
         }
         $Post = new News($post_ids[0]);

         $subject = $Post->get_title();
      }

      $Newsletter = self::create_newsletter(
         [
            'content'    => self::get_newsletter_post_list_content($post_ids),
            'subject'    => $subject,
            'recipients' => $recipients,
         ]
      );

      $response = $Newsletter->send();

      return \rest_ensure_response($response);
   }

   public static function get_newsletter_post_list_content($post_ids)
   {

      $posts = get_posts([
         'post__in'    => $post_ids,
         'post_type'   => 'post',
         'post_status' => 'publish',
         'numberposts' => -1,
         'orderby'     => 'post__in',
      ]);

      if (empty($posts)) {
         return [
            'body'    => '',
            'subject' => '',
         ];
      }

      ob_start();

      get_component('mail/newsletter', ['posts' => $posts]);

      $body = ob_get_clean();

      return $body;
   }

   public static function create_newsletter($args = [])
   {
      $type       = $args['type'] ?? 'alerta';
      $content    = $args['content'] ?? '';
      $subject    = $args['subject'] ?? '';
      $recipients = $args['recipients'] ?? [];
      $post_ids   = $args['post_ids'] ?? [];

      $hash = md5($subject . implode(',', $recipients) . implode(',', $post_ids));

      $existing_newsletter = get_page_by_path('newsletter-' . $hash);

      /**
       * Short circuit the creation of a new newsletter if there is already
       *  one with the same subject, recipients and post ids
       */
      if ($existing_newsletter) {
         $Newsletter = new Newsletter($existing_newsletter);
         // update content since post may change
         $Newsletter->update_content($content);

         return $Newsletter;
      }

      $newsletter = wp_insert_post([
         'post_type'    => 'newsletter',
         'post_title'   => $subject,
         'post_content' => $content,
         'meta_input'   => [
            'newsletter_recipients' => $recipients,
            'newsletter_post_ids'   => $post_ids,
            'newsletter_type'       => $type,
         ],
         'post_status' => 'publish',
         'slug'        => 'newsletter-' . $hash,
      ]);

      if (is_wp_error($newsletter)) {
         return new \WP_Error('error_creating_newsletter', __('Erro ao criar a newsletter', 'bx-obastidor'), ['status' => \WP_Http::INTERNAL_SERVER_ERROR]);
      }

      return new Newsletter($newsletter);
   }

   public static function send_newsletter_callback($request)
   {
      $post_ids   = $request->get_param('post_ids');
      $recipients = $request->get_param('recipients');
      $list_name  = $request->get_param('list_name');
      $subject    = $request->get_param('subject');
      $headers    = $request->get_param('headers');

      if (empty($recipients) && !empty($list_name)) {
         $recipients = self::get_subscribers(['list_name' => $list_name, 'return_type' => 'email']);
      }

      return self::send_post_list_newsletter($post_ids, $recipients, $subject, $headers);
   }
}
