<?php

namespace BX_Obastidor\Newsletter;

if (!defined('ABSPATH')) exit;

class Utils
{
   /**
    * Get filtered users for newsletter
    */
   public static function get_newsletter_users()
   {
      $user_roles = Register::get_user_roles();
      $meta_filter = Register::get_user_meta_filter();

      $args = [
         'role__in' => $user_roles,
         'meta_query' => []
      ];

      // Add meta filter if configured
      if (!empty($meta_filter['meta_key']) && !empty($meta_filter['meta_value'])) {
         $args['meta_query'][] = [
            'key'     => $meta_filter['meta_key'],
            'value'   => $meta_filter['meta_value'],
            'compare' => '='
         ];
      }

      return get_users($args);
   }

   /**
    * Get posts for weekly newsletter
    */
   public static function get_weekly_posts()
   {
      $quantity = Register::get_weekly_quantity();
      
      return get_posts([
         'post_type'      => 'post',
         'post_status'    => 'publish',
         'posts_per_page' => $quantity,
         'orderby'        => 'date',
         'order'          => 'DESC',
         'date_query'     => [
            [
               'after' => '1 week ago'
            ]
         ]
      ]);
   }

   /**
    * Get posts for daily newsletter
    */
   public static function get_daily_posts()
   {
      $quantity = Register::get_daily_quantity();
      
      return get_posts([
         'post_type'      => 'post',
         'post_status'    => 'publish',
         'posts_per_page' => $quantity,
         'orderby'        => 'date',
         'order'          => 'DESC',
         'date_query'     => [
            [
               'after' => '1 day ago'
            ]
         ]
      ]);
   }

   /**
    * Send newsletter
    */
   public static function send_newsletter($type = 'weekly', $posts = null, $title = null)
   {
      $users = self::get_newsletter_users();
      
      if (empty($users)) {
         return false;
      }

      // Get posts based on type
      if ($posts === null) {
         switch ($type) {
            case 'weekly':
               $posts = self::get_weekly_posts();
               $title = $title ?: __('Newsletter Semanal', 'bx-obastidor');
               break;
            case 'daily':
               $posts = self::get_daily_posts();
               $title = $title ?: __('Newsletter Diária', 'bx-obastidor');
               break;
            case 'manual':
               $posts = Register::get_manual_posts();
               $title = $title ?: Register::get_manual_title();
               break;
         }
      }

      if (empty($posts)) {
         return false;
      }

      // Generate email content
      $content = self::generate_email_content($posts, $title);
      
      // Send to all users
      $sent_count = 0;
      foreach ($users as $user) {
         if (wp_mail($user->user_email, $title, $content)) {
            $sent_count++;
         }
      }

      return $sent_count;
   }

   /**
    * Generate email content
    */
   private static function generate_email_content($posts, $title)
   {
      ob_start();
      ?>
      <html>
      <head>
         <meta charset="UTF-8">
         <title><?php echo esc_html($title); ?></title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
         <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h1 style="color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px;">
               <?php echo esc_html($title); ?>
            </h1>
            
            <?php foreach ($posts as $post): ?>
               <div style="margin-bottom: 30px; border-bottom: 1px solid #eee; padding-bottom: 20px;">
                  <h2 style="color: #34495e; margin-bottom: 10px;">
                     <a href="<?php echo get_permalink($post); ?>" style="color: #3498db; text-decoration: none;">
                        <?php echo get_the_title($post); ?>
                     </a>
                  </h2>
                  
                  <?php if (has_post_thumbnail($post->ID)): ?>
                     <img src="<?php echo get_the_post_thumbnail_url($post->ID, 'medium'); ?>" 
                          style="max-width: 100%; height: auto; margin-bottom: 15px;" 
                          alt="<?php echo get_the_title($post); ?>">
                  <?php endif; ?>
                  
                  <p style="color: #7f8c8d; margin-bottom: 15px;">
                     <?php echo wp_trim_words(get_the_excerpt($post), 30); ?>
                  </p>
                  
                  <a href="<?php echo get_permalink($post); ?>" 
                     style="background: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;">
                     Ler mais
                  </a>
               </div>
            <?php endforeach; ?>
            
            <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; color: #7f8c8d;">
               <p>Você está recebendo este email porque se inscreveu em nossa newsletter.</p>
               <p><a href="<?php echo home_url('/unsubscribe'); ?>" style="color: #e74c3c;">Cancelar inscrição</a></p>
            </div>
         </div>
      </body>
      </html>
      <?php
      return ob_get_clean();
   }

   /**
    * Schedule newsletters
    */
   public static function schedule_newsletters()
   {
      // Schedule weekly newsletter
      if (Register::is_weekly_active()) {
         $day = Register::get_weekly_day();
         $time = Register::get_weekly_time();
         
         if (!wp_next_scheduled('newsletter_weekly_send')) {
            wp_schedule_event(
               strtotime("next {$day} {$time}"),
               'weekly',
               'newsletter_weekly_send'
            );
         }
      }

      // Schedule daily newsletter
      if (Register::is_daily_active()) {
         $time = Register::get_daily_time();
         
         if (!wp_next_scheduled('newsletter_daily_send')) {
            wp_schedule_event(
               strtotime("today {$time}"),
               'daily',
               'newsletter_daily_send'
            );
         }
      }
   }

   /**
    * Clear scheduled newsletters
    */
   public static function clear_scheduled_newsletters()
   {
      wp_clear_scheduled_hook('newsletter_weekly_send');
      wp_clear_scheduled_hook('newsletter_daily_send');
   }
}

// Hook the scheduled events
add_action('newsletter_weekly_send', function() {
   Utils::send_newsletter('weekly');
});

add_action('newsletter_daily_send', function() {
   Utils::send_newsletter('daily');
});

// Handle manual send button
add_action('acf/save_post', function($post_id) {
   if ($post_id !== 'options') return;
   
   $send_button = get_field('newsletter_manual_send_button', 'option');
   if ($send_button === 'send_now') {
      $posts = Register::get_manual_posts();
      $title = Register::get_manual_title();
      
      if (!empty($posts)) {
         $sent = Utils::send_newsletter('manual', $posts, $title);
         
         // Clear the button and show success message
         update_field('newsletter_manual_send_button', '', 'option');
         
         // Add admin notice
         add_action('admin_notices', function() use ($sent) {
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p>Newsletter enviada com sucesso para ' . $sent . ' usuários!</p>';
            echo '</div>';
         });
      }
   }
});
