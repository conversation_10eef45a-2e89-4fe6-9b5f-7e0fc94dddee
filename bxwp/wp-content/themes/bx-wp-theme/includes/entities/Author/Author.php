<?php

/**
 * Name: Author
 * Type: User
 * Description: Recupera o objeto WP_User associado ao autor do post.
 */

namespace BX_Obastidor\Author;

if (!defined('ABSPATH')) {
   exit;
}

class Author
{
   public $user = null;
   public $user_ID = 0;

   public function __construct($user_ID = null)
   {
      $this->user    = \get_user($user_ID);
      $this->user_ID = $this->user->ID;
   }

   public function get_name()
   {
      return $this->user->get('display_name');
   }

   public function get_link()
   {
      return \get_author_posts_url($this->user_ID);
   }

   public function get_author_posts($number = 6)
   {
      $author_query = new \WP_Query([
         'post_status'    => 'publish',
         'posts_per_page' => $number,
         'author'         => $this->user_ID,
      ]);

      return $author_query->posts;
   }

   public function get_email()
   {
      return $this->user->get('user_email');
   }

   public function get_description()
   {
      return $this->user->get('description');
   }

   public function get_roles()
   {
      return !empty($this->user->roles) ? $this->user->roles : [];
   }

   public function has_role($role)
   {
      return in_array($role, $this->get_roles());
   }

   public function get_avatar($size = 100)
   {
      if ($this->get_email() === '<EMAIL>') {
         $contato_avatar_url = get_template_directory_uri() . '/assets/images/avatar-author-contato.png';

         return '<img src="' . $contato_avatar_url . '" width="' . $size . '" height="' . $size . '" />';
      }

      $avatar = \get_avatar($this->user_ID, $size, '', '', ['class' => 'w-full h-full object-cover']);

      if (empty($avatar)) {
         return render_svg('author-placeholder', 'w-full h-full object-cover', true);
      }

      return $avatar;
   }

   public function get_social_links()
   {
      return [
         'bluesky'   => get_field('user_field_bluesky', 'user_' . $this->user_ID),
         'x'         => get_field('user_field_x', 'user_' . $this->user_ID),
         'linkedin'  => get_field('user_field_linkedin', 'user_' . $this->user_ID),
         'facebook'  => get_field('user_field_facebook', 'user_' . $this->user_ID),
         'instagram' => get_field('user_field_instagram', 'user_' . $this->user_ID),
         'threads'   => get_field('user_field_threads', 'user_' . $this->user_ID)
      ];
   }
}
