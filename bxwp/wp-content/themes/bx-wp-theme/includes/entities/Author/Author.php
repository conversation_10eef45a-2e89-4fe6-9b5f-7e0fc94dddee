<?php

/**
 * Name: Author
 * Type: User
 * Description: Recupera o objeto WP_User associado ao autor do post.
 */

namespace BX_Obastidor\Author;

if (!defined('ABSPATH')) {
   exit;
}

class Author
{
   public $user = null;
   public $user_ID = 0;

   public function __construct($user_ID = null)
   {
      $this->user    = \get_user($user_ID);
      $this->user_ID = $this->user->ID;
   }

   public function get_name()
   {
      return $this->user->get('display_name');
   }

   public function get_link()
   {
      return \get_author_posts_url($this->user_ID);
   }

   public function get_author_posts($number = 6)
   {
      return \get_posts(
         [
            'posts_per_page' => $number,
            'author'         => $this->user_ID
         ]
      );
   }

   public function get_email()
   {
      return $this->user->get('user_email');
   }

   public function get_description()
   {
      return $this->user->get('description');
   }

   public function get_avatar($size = 100)
   {
      $avatar = \get_avatar($this->user_ID, $size, '', '', ['class' => 'w-full h-full object-cover']);

      if (empty($avatar)) {
         return render_svg('author-placeholder', 'w-full h-full object-cover', true);
      }

      return $avatar;
   }

   public function get_social_links()
   {
      return [
         'bluesky'   => 'https://bsky.app/',
         'x'         => 'https://x.com/',
         'linkedin'  => 'https://br.linkedin.com/',
         'facebook'  => 'https://www.facebook.com/',
         'instagram' => 'https://www.instagram.com/',
         'threads'   => 'https://www.threads.com/',
      ];
   }
}
