<?php

namespace BX_Obastidor\Author;

if (!defined('ABSPATH')) {
   exit;
}

class Register
{
   public function __construct()
   {
      add_action('template_redirect', [$this, 'redirect_authors_without_content']);
      add_action('init', [$this, 'register_fields']);
      add_filter('user_contactmethods', [$this, 'remove_yoast_admin_user_social'], 99);
   }

   public function redirect_authors_without_content()
   {
      if (!is_author()) {
         return;
      }

      $redirect_to = home_url();

      $Author = new Author(get_the_author_meta('ID'));

      if ($Author->has_role('subscriber')) {
         wp_redirect($redirect_to);
         exit;
      }

      $author_posts = $Author->get_author_posts(1);
      if (empty($author_posts)) {
         wp_redirect($redirect_to);
         exit;
      }
   }

   public function register_fields()
   {
      if (!function_exists('acf_add_local_field_group')) {
         return;
      }

      acf_add_local_field_group([
         'key'    => 'user_social_networks',
         'title'  => esc_html__('Redes Sociais', 'bx-obastidor'),
         'fields' => [
            [
               'key'     => 'user_field_facebook',
               'label'   => esc_html__('Facebook', 'bx-obastidor'),
               'name'    => 'facebook',
               'type'    => 'url',
               'wrapper' => [
                  'width' => '',
                  'class' => '',
                  'id'    => '',
               ],
               'default_value' => '',
               'placeholder'   => '',
            ],
            [
               'key'     => 'user_field_instagram',
               'label'   => esc_html__('Instagram', 'bx-obastidor'),
               'name'    => 'instagram',
               'type'    => 'url',
               'wrapper' => [
                  'width' => '',
                  'class' => '',
                  'id'    => '',
               ],
               'default_value' => '',
               'placeholder'   => '',
            ],
            [
               'key'     => 'user_field_x',
               'label'   => esc_html__('X (Twitter)', 'bx-obastidor'),
               'name'    => 'x',
               'type'    => 'url',
               'wrapper' => [
                  'width' => '',
                  'class' => '',
                  'id'    => '',
               ],
               'default_value' => '',
               'placeholder'   => '',
            ],
            [
               'key'     => 'user_field_linkedin',
               'label'   => esc_html__('LinkedIn', 'bx-obastidor'),
               'name'    => 'linkedin',
               'type'    => 'url',
               'wrapper' => [
                  'width' => '',
                  'class' => '',
                  'id'    => '',
               ],
               'default_value' => '',
               'placeholder'   => '',
            ],
            [
               'key'     => 'user_field_threads',
               'label'   => esc_html__('Threads', 'bx-obastidor'),
               'name'    => 'threads',
               'type'    => 'url',
               'wrapper' => [
                  'width' => '',
                  'class' => '',
                  'id'    => '',
               ],
               'default_value' => '',
               'placeholder'   => '',
            ],
            [
               'key'     => 'user_field_bluesky',
               'label'   => esc_html__('Bluesky', 'bx-obastidor'),
               'name'    => 'bluesky',
               'type'    => 'url',
               'wrapper' => [
                  'width' => '',
                  'class' => '',
                  'id'    => '',
               ],
               'default_value' => '',
               'placeholder'   => '',
            ],
         ],
         'location' => [
            [
               [
                  'param'    => 'user_form',
                  'operator' => '==',
                  'value'    => 'edit',
               ],
            ],
         ],
         'menu_order'            => 0,
         'position'              => 'normal',
         'style'                 => 'default',
         'label_placement'       => 'top',
         'instruction_placement' => 'label',
         'hide_on_screen'        => '',
         'active'                => true,
         'description'           => '',
         'show_in_rest'          => 1,
      ]);
   }

   public function remove_yoast_admin_user_social($profile_fields)
   {
      unset($profile_fields['facebook']);
      unset($profile_fields['instagram']);
      unset($profile_fields['linkedin']);
      unset($profile_fields['myspace']);
      unset($profile_fields['pinterest']);
      unset($profile_fields['soundcloud']);
      unset($profile_fields['tumblr']);
      unset($profile_fields['twitter']);
      unset($profile_fields['youtube']);
      unset($profile_fields['wikipedia']);
      unset($profile_fields['mastodon']);

      return $profile_fields;
   }
}
