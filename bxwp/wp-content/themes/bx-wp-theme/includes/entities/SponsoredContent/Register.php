<?php

namespace BX_Obastidor\SponsoredContent;

if (!defined('ABSPATH')) {
   exit;
}

class Register
{
   public function __construct()
   {
      \add_action('init', [$this, 'register_post_type']);
      \add_action('after_setup_theme', [$this, 'register_taxonomy']);
      \add_action('init', [$this, 'register_meta_fields']);
      \add_filter('bx_news_get_featured', [$this, 'inject_sponsored_content_into_news'], 10, 4);
      \add_action('save_post_sponsored_content', [\BX_Obastidor\CommonAds\Utils::class, 'schedule_unpublishing_post']);
      \add_filter('bx_news_get_post_object', [$this, 'get_sponsored_content_post_object'], 10, 2);
      \add_filter('bx_news_get_label', [$this, 'get_sponsored_content_label'], 10, 2);
      \add_filter('bx_news_get_label_link', [$this, 'get_sponsored_content_label_link'], 10, 2);
   }

   public function register_post_type()
   {
      $labels = [
         'name'          => __('Conteúdo Patrocinado', 'bx-obastidor'),
         'singular_name' => __('Conteúdo Patrocinado', 'bx-obastidor'),
         'menu_name'     => __('Conteúdo Patrocinado', 'bx-obastidor'),
         'all_items'     => __('Todos os Conteúdos Patrocinados', 'bx-obastidor'),
      ];

      \register_post_type(
         'sponsored_content',
         [
            'public'       => true,
            'labels'       => $labels,
            'show_in_rest' => true,
            'supports'     => ['title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'],
            'menu_icon'    => 'dashicons-tag',
            'rewrite'      => [
               'slug' => 'ads',
            ],
         ]
      );
   }

   public function register_taxonomy()
   {
      add_filter(
         'bx_taxonomy_post_type_featured-news-order',
         function ($post_types) {
            $post_types[] = 'sponsored_content';
            return $post_types;
         }
      );
   }

   public function register_meta_fields()
   {
      \register_meta(
         'post',
         'bx_obastidor_unpublish_date',
         [
            'object_subtype' => 'sponsored_content',
            'show_in_rest'   => true,
            'single'         => true,
            'type'           => 'string',
            'default'        => '',
         ]
      );

      \register_meta(
         'post',
         'bx_obastidor_sponsor_label',
         [
            'object_subtype' => 'sponsored_content',
            'show_in_rest'   => true,
            'single'         => true,
            'type'           => 'string',
            'default'        => '',
         ]
      );

      \register_meta(
         'post',
         'bx_obastidor_external_link',
         [
            'object_subtype' => 'sponsored_content',
            'show_in_rest'   => true,
            'single'         => true,
            'type'           => 'string',
            'default'        => '',
         ]
      );
   }

   public function inject_sponsored_content_into_news($posts, $featured_label, $query_info, $query)
   {
      // prepare sponsored content query
      $sponsored_content_query_args = [
         'post_type'      => 'sponsored_content',   // get sponsored_content posts
         'posts_per_page' => count($posts),         // limit results to the number of queried posts
      ];

      $tax_query = $query->query_vars['tax_query']; // get tax_query from the main query

      if (!empty($tax_query)) {
         $sponsored_content_query_args['tax_query'] = $tax_query; // if query has no tax_query
      }

      $sponsored_content_posts = (new \WP_Query($sponsored_content_query_args))->posts;

      $max_posts = isset($query_info['posts_per_page']) ? intval($query_info['posts_per_page']) : -1;

      if (count($sponsored_content_posts) === 0) {
         return $posts;
      } elseif (count($posts) === 0) {
         return array_slice($sponsored_content_posts, 0, $max_posts);
      }

      $sponsored_content_slots = range(0, count($posts) - 1);

      shuffle($sponsored_content_slots);
      $sponsored_content_slots = array_slice($sponsored_content_slots, 0, count($sponsored_content_posts));

      $posts_with_sponsored_content = [];

      foreach ($posts as $post_index => $post) {
         if (count($sponsored_content_posts) > 0 && in_array($post_index, $sponsored_content_slots)) {
            $posts_with_sponsored_content[] = array_shift($sponsored_content_posts);
         }

         $posts_with_sponsored_content[] = $post;
      }

      return $posts_with_sponsored_content;
   }

   /**
    * Force terms being an array of ints (save_post hook needs this)
    */
   public function sanitize_featured_news_order($taxonomy, $terms)
   {
      $sanitized_term_ids = [];
      foreach ((array)$terms as $term) {
         if (!empty($term)) {
            $sanitized_term_ids[] = intval($term);
         }
      }

      return $sanitized_term_ids;
   }

   public function get_sponsored_content_post_object($PostObject, $post)
   {
      if ($post->post_type === 'sponsored_content') {
         return new SponsoredContent($post);
      }

      return $PostObject;
   }

   public function get_sponsored_content_label($label, $post_id)
   {
      if (get_post_type($post_id) !== 'sponsored_content') {
         return $label;
      }

      $sponsor_label = get_post_meta($post_id, 'bx_obastidor_sponsor_label', true);
      if ($sponsor_label) {
         return $sponsor_label;
      }

      return __('Conteúdo Patrocinado', 'bx-obastidor');
   }

   public function get_sponsored_content_label_link($label_link, $post_id)
   {
      if (get_post_type($post_id) !== 'sponsored_content') {
         return $label_link;
      }

      $external_link = get_post_meta($post_id, 'bx_obastidor_external_link', true);
      if ($external_link) {
         return $external_link;
      }

      return $label_link;
   }
}
