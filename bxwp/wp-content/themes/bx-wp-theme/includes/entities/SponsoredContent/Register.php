<?php

namespace BX_Obastidor\SponsoredContent;

if (!defined('ABSPATH')) {
   exit;
}

class Register
{
   public function __construct()
   {
      \add_action('init', [$this, 'register_post_type']);
      \add_action('after_setup_theme', [$this, 'register_taxonomy']);
      \add_action('init', [$this, 'register_meta_fields']);
      \add_filter('bx_news_get_news_posts', [$this, 'inject_sponsored_content_into_news'], 10, 2);
      \add_filter('bx_news_get_label', [$this, 'get_sponsored_content_label'], 10, 2);
      \add_filter('bx_news_get_label_link', [$this, 'get_sponsored_content_label_link'], 10, 2);
   }

   public function register_post_type()
   {
      $labels = [
         'name'          => __('Conteúdo Patrocinado', 'bx-obastidor'),
         'singular_name' => __('Conteúdo Patrocinado', 'bx-obastidor'),
         'menu_name'     => __('Conteúdo Patrocinado', 'bx-obastidor'),
         'all_items'     => __('Todos os Conteúdos Patrocinados', 'bx-obastidor'),
      ];

      \register_post_type(
         'sponsored_content',
         [
            'public'          => true,
            'labels'          => $labels,
            'show_in_rest'    => true,
            'supports'        => ['title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'],
            'menu_icon'       => 'dashicons-tag',
            'capability_type' => 'sponsored_content',
            'capabilities'    => [
               'edit_post'          => 'edit_others_posts',
               'read_post'          => 'edit_others_posts',
               'delete_post'        => 'edit_others_posts',
               'edit_posts'         => 'edit_others_posts',
               'edit_others_posts'  => 'edit_others_posts',
               'publish_posts'      => 'edit_others_posts',
               'read_private_posts' => 'edit_others_posts',
               'create_posts'       => 'edit_others_posts',
            ],
            'rewrite'      => [
               'slug' => 'ads',
            ],
         ]
      );
   }

   public function register_taxonomy()
   {
      add_filter(
         'bx_taxonomy_post_type_featured-news-order',
         function ($post_types) {
            $post_types[] = 'sponsored_content';
            return $post_types;
         }
      );
   }

   public function register_meta_fields()
   {
      \register_meta(
         'post',
         'bx_obastidor_unpublish_date',
         [
            'object_subtype' => 'sponsored_content',
            'show_in_rest'   => true,
            'single'         => true,
            'type'           => 'string',
            'default'        => '',
         ]
      );

      \register_meta(
         'post',
         'bx_obastidor_sponsor_label',
         [
            'object_subtype' => 'sponsored_content',
            'show_in_rest'   => true,
            'single'         => true,
            'type'           => 'string',
            'default'        => '',
         ]
      );

      \register_meta(
         'post',
         'bx_obastidor_external_link',
         [
            'object_subtype' => 'sponsored_content',
            'show_in_rest'   => true,
            'single'         => true,
            'type'           => 'string',
            'default'        => '',
         ]
      );
   }

   public function inject_sponsored_content_into_news($posts, $query_info)
   {
      $sponsored_content_query_args  = [
         'post_status'    => 'publish',
         'post_type'      => 'sponsored_content',
         'posts_per_page' => count($posts),
      ];

      if (!empty($query_info['tax_query'])) {
         // tax_query from the main query will limit ads with featured-order taxonomy
         $sponsored_content_query_args['tax_query'] = $query_info['tax_query'];
      }

      $sponsored_content_query = new \WP_Query($sponsored_content_query_args);
      $sponsored_content_posts = $sponsored_content_query->posts;

      if (count($sponsored_content_posts) === 0) {
         return $posts;
      } elseif (count($posts) === 0) {
         $max_posts = isset($query_info['posts_per_page']) ? (int) $query_info['posts_per_page'] : -1;

         if ($max_posts > 0) {
            return array_slice($sponsored_content_posts, 0, $max_posts);
         }

         return $sponsored_content_posts;
      }

      $sponsored_content_slots = range(0, count($posts) - 1);
      shuffle($sponsored_content_slots);
      $sponsored_content_slots = array_slice($sponsored_content_slots, 0, count($sponsored_content_posts));

      $posts_with_sponsored_content = [];
      $sponsored_index = 0;

      foreach ($posts as $post_index => $post) {
         if ($sponsored_index < count($sponsored_content_posts) && in_array($post_index, $sponsored_content_slots)) {
            $posts_with_sponsored_content[] = $sponsored_content_posts[$sponsored_index];
            $sponsored_index++;
         }

         $posts_with_sponsored_content[] = $post;
      }

      return $posts_with_sponsored_content;
   }

   public function get_sponsored_content_label($label, $post_id)
   {
      if (get_post_type($post_id) !== 'sponsored_content') {
         return $label;
      }

      $sponsor_label = get_post_meta($post_id, 'bx_obastidor_sponsor_label', true);
      if ($sponsor_label) {
         return $sponsor_label;
      }

      return __('Conteúdo Patrocinado', 'bx-obastidor');
   }

   public function get_sponsored_content_label_link($label_link, $post_id)
   {
      if (get_post_type($post_id) !== 'sponsored_content') {
         return $label_link;
      }

      $external_link = get_post_meta($post_id, 'bx_obastidor_external_link', true);
      if ($external_link) {
         return $external_link;
      }

      return $label_link;
   }
}
