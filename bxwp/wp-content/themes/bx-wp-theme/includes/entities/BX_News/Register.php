<?php

namespace BX_Obastidor\BX_News;

if (!defined('ABSPATH')) {
   exit;
}

class Register
{
   public function __construct()
   {
      \add_action('init', [$this, 'register_taxonomy']);
      \add_action('init', [$this, 'create_default_taxonomy_terms']);
      \add_action('add_meta_boxes', [$this, 'replace_featured_news_order_metabox']);
      \add_action('init', [$this, 'register_post_fields']);
      \add_action('wp_ajax_load_more_news', [$this, 'load_more_news']);
      \add_action('wp_ajax_nopriv_load_more_news', [$this, 'load_more_news']);
      \add_filter('excerpt_more', '__return_false');
      \add_action('rest_api_init', [$this, 'register_rest_routes']);
   }

   public function register_taxonomy()
   {
      $labels = [
         'name'          => esc_html__('Nível de Destaque', 'bx-obastidor'),
         'singular_name' => esc_html__('Nível de Destaque', 'bx-obastidor'),
         'menu_name'     => esc_html__('Destaque', 'bx-obastidor'),
      ];

      $args = [
         'labels'            => $labels,
         'description'       => esc_html__('Nível de Destaque da Notícia', 'bx-obastidor'),
         'public'            => true,
         'hierarchical'      => true,
         'show_ui'           => false,
         'show_in_rest'      => true,
         'show_admin_column' => true,
         'meta_box_cb'       => false,
      ];

      \register_taxonomy('featured-news-order', ['post'], $args);
   }

   public function create_default_taxonomy_terms()
   {
      foreach (Utils::get_featured_news_terms() as $term) {
         if (\term_exists($term['slug'], 'featured-news-order')) {
            continue;
         }

         \wp_insert_term($term['name'], 'featured-news-order', [
            'slug'        => $term['slug'],
            'description' => $term['description']
         ]);
      }
   }

   public function replace_featured_news_order_metabox()
   {
      \remove_meta_box('featured-news-orderdiv', 'post', 'side');

      \add_meta_box(
         'featured-news-order',
         esc_html__('Destaque', 'bx-obastidor'),
         [$this, 'featured_news_order_metabox'],
         'post',
         'side',
         'high'
      );
   }

   public function featured_news_order_metabox()
   {
      global $post;

      $current_term = \wp_get_object_terms($post->ID, 'featured-news-order', ['fields' => 'ids']);

      $selected = $current_term[0] ?? 0;

      \wp_dropdown_categories([
         'taxonomy'         => 'featured-news-order',
         'name'             => 'tax_input[featured-news-order]',
         'id'               => 'featured-news-order',
         'selected'         => $selected,
         'hierarchical'     => true,
         'depth'            => 1,
         'show_option_none' => esc_html__('Nenhum', 'bx-obastidor'),
         'hide_empty'       => false,
         'value_field'      => 'term_id',
         'orderby'          => 'slug',
         'order'            => 'ASC'
      ]);
   }

   public function register_post_fields()
   {
      if (!function_exists('acf_add_local_field_group')) {
         return;
      }

      \acf_add_local_field_group(array(
         'key'    => 'group_bx_post_exclusive',
         'title'  => esc_html__('Conteúdo Exclusivo', 'bx-obastidor'),
         'fields' => array(
            array(
               'key'                   => 'field_post_exclusive',
               'label'                 => false,
               'name'                  => 'bx_obastidor_news_exclusive',
               'type'                  => 'true_false',
               'instructions'          => '',
               'instruction_placement' => 'field',
               'required'              => 0,
               'default_value'         => 0,
               'ui'                    => 1,
               'ui_on_text'            => 'Sim',
               'ui_off_text'           => 'Não'
            )
         ),
         'location' => array(
            array(
               array(
                  'param'    => 'post_type',
                  'operator' => '==',
                  'value'    => 'post',
               ),
            ),
         ),
         'menu_order'            => 0,
         'position'              => 'side',
         'style'                 => 'default',
         'label_placement'       => 'top',
         'instruction_placement' => 'label',
         'hide_on_screen'        => '',
         'active'                => true,
         'description'           => '',
         'show_in_rest'          => 0,
      ));
   }

   public function load_more_news()
   {
      if (false === check_ajax_referer('defaultNonce', false, false)) {
         \wp_send_json_error([
            'messages' => esc_html__('Nonce inválido.', 'bx-obastidor')
         ], \WP_Http::FORBIDDEN);
      }

      $query_args = [];

      if (isset($_GET['query_args'])) {
         $query_args = json_decode(stripslashes($_GET['query_args']), true);
      }

      $query_info = []; // will receive query_vars
      $posts = Utils::get_not_featured_posts($query_args, $query_info);

      error_log(var_export(compact('query_info'), true));

      if (empty($posts)) {
         \wp_send_json_error([
            'messages' => esc_html__('Nenhuma notícia encontrada.', 'bx-obastidor')
         ], \WP_Http::NOT_FOUND);
      }

      ob_start();

      foreach ($posts as $post) {
         $News = new BX_News($post);
         get_component('news-list-item', ['post_object' => $News->get_post_object()]);
      }

      $html = ob_get_clean();

      wp_send_json_success([
         'html'         => $html,
         'current_page' => $query_info['paged'] ?? 1,
         'max_pages'    => $query_info['max_num_pages']
      ]);
   }

   public function register_rest_routes()
   {
      register_rest_route(
         'bxwp/v1',
         '/view-counter',
         [
            'methods'             => 'POST',
            'callback'            => [Utils::class, 'set_view_event'],
            'permission_callback' => '__return_true',
         ]
      );
   }
}
