<?php

/**
 * Name: BX_News
 * Type: BX_News
 * Taxonomies: category, tag, featured-news-order
 * Custom Fields: bx_obastidor_news_exclusive
 * Description: Post padrão.
 */

namespace BX_Obastidor\BX_News;

if (!defined('ABSPATH')) {
   exit;
}

class BX_News extends \BX_Obastidor\BX_Post\BX_Post
{
   public function get_excerpt(int $excerpt_size = 20, string $excerpt_more = '...')
   {

      if (empty($excerpt_size)) {
         return \get_the_excerpt($this->ID);
      }

      return \wp_trim_words(\get_the_excerpt($this->ID), $excerpt_size, $excerpt_more);
   }

   public function has_label()
   {
      if ($this->is_exclusive()) {
         return true;
      }
      if ($this->get_post_type() === 'sponsored_content') {
         return true;
      }
      if (!empty($this->get_category())) {
         return true;
      }
      return $this->is_exclusive() || $this->get_post_type() === 'sponsored_content' || !empty($this->get_category());
   }

   public function get_category()
   {
      $categories = \get_the_category($this->ID);

      if (!is_array($categories) || empty($categories)) {
         return;
      }

      return new \BX_Obastidor\BX_Taxonomy\BX_Taxonomy($categories[0]);
   }

   public function is_exclusive()
   {
      return (bool) \get_field('bx_obastidor_news_exclusive', $this->ID);
   }

   public function get_post_object()
   {
      return apply_filters('bx_news_get_post_object', $this, $this->post);
   }

}
