<?php

namespace BX_Obastidor\BX_News;

if (!defined('ABSPATH')) {
   exit;
}

class Utils
{
   public static function get_featured_news_terms()
   {
      return [
         [
            'name'        => esc_html__('Principal', 'bx-obastidor'),
            'slug'        => 'principal',
            'description' => ''
         ],
         [
            'name'        => esc_html__('Secundário', 'bx-obastidor'),
            'slug'        => 'secundario',
            'description' => ''
         ],
         [
            'name'        => esc_html__('Não listado', 'bx-obastidor'),
            'slug'        => 'unlisted',
            'description' => ''
         ]
      ];
   }

   public static function get_featured_posts(
      $featured_label = 'principal',
      $override_query_args = [],
      &$query_info = []
   ) {
      // %NOT-FEATURED% is a special case indicating that get_not_featured_posts was invoked
      if ($featured_label === '%NOT-FEATURED%') {
         $featured_tax_query = [
            [
               'taxonomy' => 'featured-news-order',
               'terms'    => 'unlisted',
               'operator' => 'NOT IN',
               'field'    => 'slug'
            ]
         ];
      } else {
         $featured_tax_query = [
            [
               'taxonomy' => 'featured-news-order',
               'field'    => 'slug',
               'terms'    => $featured_label
            ]
         ];
      }

      // restrict to override_query_args tax_query if provided honoring both
      if (!empty($override_query_args['tax_query'])) {
         $featured_tax_query[] = $override_query_args['tax_query'];
         $featured_tax_query['relation'] = 'AND';
      }

      $query_args = [
         'post_type'      => 'post',
         'orderby'        => 'date',
         'order'          => 'DESC',
         'tax_query'      => $featured_tax_query
      ];

      $query = new \WP_Query(wp_parse_args($override_query_args, $query_args));

      $query_info = $query->query_vars;
      $query_info['max_num_pages'] = $query->max_num_pages;

      return apply_filters(
         'bx_news_get_featured',
         $query->posts,
         $featured_label,
         $query_info,
         $query
      );
   }

   public static function get_not_featured_posts(
      $override_query_args = [],
      &$query_info = [],
   ) {
      return self::get_featured_posts('%NOT-FEATURED%', $override_query_args, $query_info);
   }

   public static function set_view_event($request)
   {
      $response = [];

      try {
         $viewed_posts = get_option('bxwp_viewed_posts', []);

         $post_ID = $request->get_param('postId');

         $viewed_posts[$post_ID]   = $viewed_posts[$post_ID] ?? [];
         $viewed_posts[$post_ID][] = time();

         update_option('bxwp_viewed_posts', $viewed_posts);

         $response['success'] = true;
      } catch (\Exception $e) {
         $response['success'] = false;
         $response['error']   = $e->getMessage();
      }

      return rest_ensure_response($response);
   }

   public static function get_most_readed_posts($limit = 5)
   {
      $filter_period  = 60 * 60;
      $archive_period = 60 * 60 * 24 * 7;

      $most_readed_posts = get_transient('bxwp_viewed_posts');

      if (!empty($most_readed_posts)) {
         return $most_readed_posts;
      }

      $posts_views = get_option('bxwp_viewed_posts', []);

      $cleaned_views = [];

      foreach ($posts_views as $post_id => $views) {
         $valid_views = [];
         foreach ($views as $view_entry) {
            if ($view_entry > time() - $archive_period) {
               $valid_views[] = $view_entry;
            }
         }

         if (!empty($valid_views)) {
            $cleaned_views[$post_id] = $valid_views;
         }
      }

      if (!empty($cleaned_views)) {
         update_option('bxwp_viewed_posts', $cleaned_views);
      }

      $most_readed_posts = [];

      foreach ($cleaned_views as $post_id => $views) {
         $post_object = get_post($post_id);

         if (!$post_object || !is_a($post_object, 'WP_Post') || $post_object->post_status !== 'publish') {
            continue;
         }

         $recent_views = [];
         foreach ($views as $view_entry) {
            if ($view_entry > time() - $filter_period) {
               $recent_views[] = $view_entry;
            }
         }

         if (empty($recent_views)) {
            continue;
         }

         $most_readed_posts[] = [
            'post_ID'     => $post_object->ID,
            'views'       => $recent_views,
            'views_count' => count($recent_views),
         ];
      }

      if (empty($most_readed_posts)) {
         return [];
      }

      $views_count = array_column($most_readed_posts, 'views_count');
      array_multisort($views_count, SORT_DESC, $most_readed_posts);

      $most_readed_posts = array_slice($most_readed_posts, 0, $limit);

      if (count($most_readed_posts) === $limit) {
         set_transient('bxwp_viewed_posts', $most_readed_posts, 60 * 30);
      }

      return $most_readed_posts;
   }

}
