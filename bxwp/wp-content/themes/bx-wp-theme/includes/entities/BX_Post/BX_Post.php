<?php

/**
 * Name: Post
 * Type: Post
 * Taxonomies: category, tag
 * Description: Post padrão.
 */

namespace BX_Obastidor\BX_Post;

use BX_Obastidor\Author\Author;
use BX_Obastidor\BX_Taxonomy\BX_Taxonomy;

if (!defined('ABSPATH')) {
   exit;
}

class BX_Post
{
   protected $post;
   protected $ID = 0;

   public function __construct($post = null)
   {
      if (is_null($post)) {
         $this->post = \get_post();
      } elseif (is_numeric($post)) {
         $this->post = \get_post($post);
      } elseif (is_a($post, 'WP_Post')) {
         $this->post = $post;
      }

      $this->ID = (int) $this->post->ID;
   }

   public function get_the_ID()
   {
      return $this->ID;
   }

   public function get_author()
   {
      return new Author(\get_post_field('post_author', $this->ID));
   }

   public function get_title()
   {
      return \apply_filters('the_title', $this->post->post_title, $this->ID);
   }

   public function has_thumbnail()
   {
      return \has_post_thumbnail($this->ID);
   }

   public function get_thumbnail($size = 'medium', $attrs = [])
   {
      return \get_the_post_thumbnail($this->post, $size, $attrs);
   }

   public function get_thumbnail_url($size = 'medium')
   {
      return \get_the_post_thumbnail_url($this->post, $size);
   }

   public function get_thumbnail_caption()
   {
      return \get_the_post_thumbnail_caption($this->ID);
   }

   public function get_link()
   {
      return \get_permalink($this->post);
   }

   public function get_terms($taxonomy)
   {
      return \get_the_terms($this->ID, $taxonomy);
   }

   public function get_post_terms($taxonomy = 'post_tag', $args = [])
   {
      return \wp_get_post_terms($this->ID, $taxonomy, $args);
   }

   public function has_term($term_slug, $taxonomy = 'post_tag')
   {
      $post_terms_list = $this->get_terms($taxonomy);

      if (empty($post_terms_list)) {
         return false;
      }

      foreach ($post_terms_list as $post_term) {
         if ($post_term->slug === $term_slug) {
            return true;
         }
      }

      return false;
   }

   public function get_date($format = 'd \d\e F, Y')
   {
      return \get_the_date($format, $this->ID);
   }

   public function get_excerpt()
   {
      return \get_the_excerpt($this->ID);
   }

   public function has_excerpt()
   {
      return \has_excerpt($this->ID);
   }

   public function get_content()
   {
      return \apply_filters('the_content', $this->post->post_content);
   }

   public function get_reading_time()
   {
      $post_content = \get_the_content();

      if (empty($post_content)) {
         return;
      }

      $reading_time = ceil(str_word_count(strip_tags($post_content)) / 228);

      return esc_html(sprintf(_n('%s minuto', '%s minutos', $reading_time, 'bx-bastidor'), $reading_time));
   }

   public function get_share_buttons($nav_class = '', $list_class = '', $link_class = '')
   {
      $title = rawurlencode($this->get_title());
      $url   = esc_url($this->get_link());

      $share_links = [
         [
            'url'       => 'https://wa.me/?text=' . $title . ' ' . $url,
            'title'     => esc_html__('Compartilhar no WhatsApp', 'bx-bastidor'),
            'icon_name' => 'whatsapp',
         ],
         [
            'url'       => 'https://x.com/intent/tweet?text=' . $title . '&url=' . $url,
            'title'     => esc_html__('Compartilhar no X', 'bx-bastidor'),
            'icon_name' => 'x',
         ],
         [
            'url'       => 'https://www.facebook.com/sharer/sharer.php?u=' . $url . '&quote=' . $title,
            'title'     => esc_html__('Compartilhar no Facebook', 'bx-bastidor'),
            'icon_name' => 'facebook',
         ],
         // [
         //    'url'       => 'https://www.linkedin.com/shareArticle?mini=true&url=' . $url . '&title=' . $title,
         //    'title'     => esc_html__('Compartilhar no LinkedIn', 'bx-bastidor'),
         //    'icon_name' => 'linkedin',
         // ],
         // [
         //    'title'     => esc_html__('Copiar url da notícia', 'bx-bastidor'),
         //    'icon_name' => 'copy',
         // ],
         // [
         //    'title'     => esc_html__('Imprimir notícia', 'bx-bastidor'),
         //    'icon_name' => 'print',
         // ],
      ];

      $content = '<nav class="' . $nav_class . '" aria-label="' . esc_attr__('Compartilhar conteúdo', 'bx-bastidor') . '">';

      $content .= '<ul class="' . $list_class . '">';

      foreach ($share_links as $link) {
         $content .= '<li>';

         if ($link['icon_name'] === 'print') {
            $content .= '<a href="#" title="' . $link['title'] . '" class="' . $link_class . '" onclick="window.print()" target="_blank">';
         } else if ($link['icon_name'] === 'copy') {
            $content .= '<a href="#" title="' . $link['title'] . '" class="' . $link_class . '" js-copy-url>';
         } else {
            $content .= '<a href="' . $link['url'] . '" title="' . $link['title'] . '" class="' . $link_class . '" target="_blank">';
         }

         $content .= render_svg("social-media/{$link['icon_name']}", 'fill-neutral-700', false);
         $content .= '</a>';
         $content .= '</li>';
      }

      $content .= '</ul>';
      $content .= '</nav>';

      return $content;
   }

   public function get_authors()
   {
      if (!function_exists('get_coauthors')) {
         return [$this->get_author()];
      }

      $coauthors = get_coauthors($this->ID);
      $authors = [];

      foreach ($coauthors as $wp_author) {
         $authors[] = new Author($wp_author->ID);
      }

      return $authors;
   }

   public function get_post_type()
   {
      return $this->post->post_type;
   }
}
