<?php

namespace BX_Obastidor\News;

if (!defined('ABSPATH')) {
   exit;
}

class Register
{
   public function __construct()
   {
      \add_action('init', [$this, 'register_taxonomy']);
      \add_action('init', [$this, 'register_meta_fields']);
      \add_action('init', [$this, 'create_default_taxonomy_terms']);
      \add_action('save_post', [$this, 'limit_featured_posts_number'], 10, 2);
      \add_action('wp_ajax_load_more_news', [$this, 'load_more_news']);
      \add_action('wp_ajax_nopriv_load_more_news', [$this, 'load_more_news']);
      \add_filter('excerpt_more', '__return_false');
      \add_action('rest_api_init', [$this, 'register_rest_routes']);
   }

   public function register_taxonomy()
   {
      $labels = [
         'name'          => esc_html__('Nível de Destaque', 'bx-obastidor'),
         'singular_name' => esc_html__('Nível de Destaque', 'bx-obastidor'),
         'menu_name'     => esc_html__('Destaque', 'bx-obastidor'),
      ];

      $args = [
         'labels'            => $labels,
         'public'            => true,
         'hierarchical'      => true,
         'show_ui'           => false,
         'show_in_rest'      => true,
         'show_admin_column' => true
      ];

      $post_type = apply_filters('bx_taxonomy_post_type_featured-news-order', ['post']);

      \register_taxonomy('featured-news-order', $post_type, $args);
   }

   public function register_meta_fields()
   {
      \register_meta(
         'post',
         'bx_obastidor_exclusive_content',
         [
            'show_in_rest' => true,
            'single'       => true,
            'type'         => 'boolean'
         ]
      );
   }

   public function create_default_taxonomy_terms()
   {
      foreach (Utils::get_featured_news_terms() as $term) {
         if (\term_exists($term['slug'], 'featured-news-order')) {
            continue;
         }

         \wp_insert_term($term['name'], 'featured-news-order', [
            'slug'        => $term['slug'],
            'description' => $term['description']
         ]);
      }
   }

   public function load_more_news()
   {
      if (false === check_ajax_referer('defaultNonce', 'nonce', false)) {
         \wp_send_json_error([
            'messages' => esc_html__('Nonce inválido.', 'bx-obastidor')
         ], \WP_Http::FORBIDDEN);
      }

      $query_args = [];

      if (isset($_GET['query_args'])) {
         $query_args = json_decode(stripslashes($_GET['query_args']), true);
      }

      $query_info = []; // will receive query_vars
      $posts = Utils::get_not_featured_posts($query_args, $query_info);

      if (empty($posts)) {
         \wp_send_json_error([
            'messages' => esc_html__('Nenhuma notícia encontrada.', 'bx-obastidor')
         ], \WP_Http::NOT_FOUND);
      }

      ob_start();

      foreach ($posts as $post) {
         $News = new News($post);
         get_component('news-list-item', ['post_object' => $News->get_post_object()]);
      }

      $html = ob_get_clean();

      wp_send_json_success([
         'html'         => $html,
         'current_page' => $query_info['paged'] ?? 1,
         'max_pages'    => $query_info['max_num_pages']
      ]);
   }

   public function register_rest_routes()
   {
      register_rest_route(
         'bxwp/v1',
         '/view-counter',
         [
            'methods'             => 'POST',
            'callback'            => [Utils::class, 'set_view_event'],
            'permission_callback' => [$this, 'check_view_counter_nonce'],
         ]
      );
   }

   public function check_view_counter_nonce($request)
   {
      $nonce = $request->get_header('X-WP-Nonce');

      if (empty($nonce)) {
         return false;
      }

      return wp_verify_nonce($nonce, 'wp_rest');
   }

   public function limit_featured_posts_number($post_id, $post)
   {
      $post_types = ['post', 'sponsored_content'];

      if (!in_array($post->post_type, $post_types)) {
         return;
      }

      $featured_terms = Utils::get_featured_news_terms();
      $post_terms = wp_get_post_terms($post_id, 'featured-news-order', ['fields' => 'slugs']);

      foreach ($featured_terms as $term) {

         if (!isset($term['max_posts']) || $term['max_posts'] < 1) {
            continue;
         }

         if (!in_array($term['slug'], $post_terms)) {
            continue;
         }

         $posts_with_term = get_posts([
            'post_type'      => $post_types,
            'numberposts'    => -1,
            'post_status'    => 'publish',
            'orderby'        => 'date',
            'order'          => 'ASC',
            'fields'         => 'ids',
            'tax_query'      => [
               [
                  'taxonomy' => 'featured-news-order',
                  'field'    => 'slug',
                  'terms'    => $term['slug']
               ]
            ]
         ]);

         if (count($posts_with_term) > $term['max_posts']) {

            $posts_to_remove = array_diff($posts_with_term, [$post_id]);

            $posts_to_remove = array_slice(
               $posts_to_remove,
               0,
               count($posts_with_term) - $term['max_posts'] // mantém o limite de posts
            );

            foreach ($posts_to_remove as $post_to_remove_id) {
               wp_remove_object_terms($post_to_remove_id, $term['slug'], 'featured-news-order');
            }
         }
      }
   }
}
