<?php

namespace BX_Obastidor\News;

if (!defined('ABSPATH')) {
   exit;
}

class Register
{
   public function __construct()
   {
      \add_action('init', [$this, 'register_taxonomy']);
      \add_action('init', [$this, 'register_meta_fields']);
      \add_action('init', [$this, 'create_default_taxonomy_terms']);
      \add_action('wp_ajax_load_more_news', [$this, 'load_more_news']);
      \add_action('wp_ajax_nopriv_load_more_news', [$this, 'load_more_news']);
      \add_filter('excerpt_more', '__return_false');
      \add_action('rest_api_init', [$this, 'register_rest_routes']);
      \add_filter('manage_post_posts_columns', [$this, 'add_featured_news_order_column']);
      \add_action('manage_post_posts_custom_column', [$this, 'render_featured_news_order_column'], 10, 2);
      \add_action('admin_init', [$this, 'toggle_featured_news_order']);
      \add_action('set_object_terms', [$this, 'limit_featured_posts'], 10, 6);
   }

   public function register_taxonomy()
   {
      $labels = [
         'name'          => esc_html__('Nível de Destaque', 'bx-obastidor'),
         'singular_name' => esc_html__('Nível de Destaque', 'bx-obastidor'),
         'menu_name'     => esc_html__('Destaque', 'bx-obastidor'),
      ];

      $args = [
         'labels'       => $labels,
         'public'       => true,
         'hierarchical' => true,
         'show_ui'      => false,
         'show_in_rest' => true
      ];

      $post_type = apply_filters('bx_taxonomy_post_type_featured-news-order', ['post']);

      \register_taxonomy('featured-news-order', $post_type, $args);
   }

   public function register_meta_fields()
   {
      \register_meta(
         'post',
         'bx_obastidor_exclusive_content',
         [
            'show_in_rest' => true,
            'single'       => true,
            'type'         => 'boolean'
         ]
      );
   }

   public function create_default_taxonomy_terms()
   {
      foreach (Utils::get_featured_news_terms() as $term) {
         if (\term_exists($term['slug'], 'featured-news-order')) {
            continue;
         }

         \wp_insert_term($term['name'], 'featured-news-order', [
            'slug'        => $term['slug'],
            'description' => $term['description']
         ]);
      }
   }

   public function load_more_news()
   {
      if (false === check_ajax_referer('defaultNonce', 'nonce', false)) {
         \wp_send_json_error([
            'messages' => esc_html__('Nonce inválido.', 'bx-obastidor')
         ], \WP_Http::FORBIDDEN);
      }

      $query_args = [];

      if (isset($_GET['query_args'])) {
         $query_args = json_decode(stripslashes($_GET['query_args']), true);
      }

      $query_info = []; // will receive query_vars
      $posts = Utils::get_news_posts($query_args, $query_info);

      if (empty($posts)) {
         \wp_send_json_error([
            'messages' => esc_html__('Nenhuma notícia encontrada.', 'bx-obastidor')
         ], \WP_Http::NOT_FOUND);
      }

      ob_start();

      foreach ($posts as $post) {
         get_component('news-list-item', ['post_object' => new News($post)]);
      }

      $html = ob_get_clean();

      wp_send_json_success([
         'html'         => $html,
         'current_page' => $query_info['paged'] ?? 1,
         'max_pages'    => $query_info['max_num_pages']
      ]);
   }

   public function register_rest_routes()
   {
      register_rest_route('bx-obastidor/v1', '/view-counter', [
         'methods'             => 'POST',
         'callback'            => [Utils::class, 'set_view_event'],
         'permission_callback' => [\BX_Obastidor\Utils\Utils::class, 'check_rest_nonce'],
         'args'                => [
            'postId' => [
               'required' => true
            ]
         ]
      ]);
   }

   public function limit_featured_posts($post_id, $terms, $tt_ids, $taxonomy, $append, $old_tt_ids)
   {
      if ($taxonomy !== 'featured-news-order') {
         return;
      }
      if (count($terms) > 1) {
         return;
      }
      
      $term = get_term_by('id', $terms[0], 'featured-news-order');

      if (count($terms) < 1) {
         delete_post_meta(
            $post_id,
            'bx_obastidor_set_featured_' . $term->slug,
         );
         return;
      }

      $featured_slugs = array_column(
         Utils::get_featured_news_terms(),
         'slug'
      );

      $featured_term_index = array_search(
         $term->slug,
         $featured_slugs
      );

      $downgrade_to = $featured_slugs[$featured_term_index + 1] ?? false;

      $featured_max_posts = array_column(
         Utils::get_featured_news_terms(),
         'max_posts',
         'slug'
      );

      update_post_meta(
         $post_id,
         'bx_obastidor_set_featured_' . $term->slug,
         current_time('mysql')
      );

      $featured_ids = get_posts([
         'post_type'    => 'post',
         'numberposts'  => -1,
         'post_status'  => 'publish',
         'post__not_in' => [$post_id],
         'tax_query'    => [
            [
               'taxonomy' => 'featured-news-order',
               'field'    => 'slug',
               'terms'    => $term->slug
            ]
         ],
         'meta_query' => [
            'relation' => 'OR',
            [
               'key'     => 'bx_obastidor_set_featured_' . $term->slug,
               'compare' => 'NOT EXISTS'
            ],
            [
               'key'     => 'bx_obastidor_set_featured_' . $term->slug,
               'compare' => 'EXISTS'
            ]
         ],
         'orderby' => [
            'meta_value_num' => 'ASC',
            'date'          => 'ASC'
         ],
         'meta_key' => 'bx_obastidor_set_featured_' . $term->slug,
         'fields' => 'ids'
      ]);

      $featured_ids_count = count($featured_ids) + 1; // count the current post
      $downgrade_ids_count = max(0, $featured_ids_count - ($featured_max_posts[$term->slug] ?? 0));

      $ids_to_downgrade = array_slice(
         $featured_ids,
         0,
         $downgrade_ids_count
      );

      foreach ($ids_to_downgrade as $id_to_downgrade) {
         delete_post_meta($id_to_downgrade, 'bx_obastidor_set_featured_' . $term->slug);
         // remove the action to avoid infinite loop
         remove_action('set_object_terms', [$this, 'limit_featured_posts'], 10, 6);
         wp_remove_object_terms($id_to_downgrade, $term->slug, 'featured-news-order');
         // and re-add action afterwards
         add_action('set_object_terms', [$this, 'limit_featured_posts'], 10, 6);

         if ($downgrade_to) {
            $downgrade_to_term = get_term_by('slug', $downgrade_to, 'featured-news-order');
            wp_set_object_terms($id_to_downgrade, $downgrade_to_term->term_id, 'featured-news-order', true);
         }
      }
   }

   public function add_featured_news_order_column($columns)
   {
      if (current_user_can('editor') || current_user_can('administrator')) {
         $columns['featured-news-order'] = 'Destaque';
      }

      return $columns;
   }

   public function render_featured_news_order_column($column, $post_id)
   {
      if ($column !== 'featured-news-order') {
         return;
      }

      $featured_terms = array_column(
         Utils::get_featured_news_terms(),
         'name',
         'slug'
      );

      $post_active_terms = array_column(
         wp_get_post_terms($post_id, 'featured-news-order'),
         'slug'
      );

      $post_inactive_terms = array_diff(
         array_keys($featured_terms),
         $post_active_terms
      );

      foreach ($post_active_terms as $post_active_term) {
         if (empty($featured_terms[$post_active_term])) {
            continue;
         }

?>
         <a href="<?php echo add_query_arg(['post_id' => $post_id, 'toggle_featured_term' => $post_active_term]); ?>" style="display: block;" title="<?php printf(esc_attr__('Remover destaque %s', 'bx-obastidor'), $featured_terms[$post_active_term]); ?>">
            <?php echo $featured_terms[$post_active_term]; ?>
         </a>
      <?php

      }

      foreach ($post_inactive_terms as $post_inactive_term) {
         if (empty($featured_terms[$post_inactive_term])) {
            continue;
         }

      ?>
         <a href="<?php echo add_query_arg(['post_id' => $post_id, 'toggle_featured_term' => $post_inactive_term]); ?>" style="display: block; color: #aaaaaa !important;" title="<?php printf(esc_attr__('Destacar em %s', 'bx-obastidor'), $featured_terms[$post_inactive_term]); ?>">
            <small><?php printf(esc_html__('destacar em %s', 'bx-obastidor'), $featured_terms[$post_inactive_term]); ?></small>
         </a>
<?php

      }
   }

   public function toggle_featured_news_order()
   {
      if (!isset($_GET['post_id']) || !isset($_GET['toggle_featured_term'])) {
         return;
      }

      if (!current_user_can('editor') && !current_user_can('administrator')) {
         return;
      }

      $post_id              = $_GET['post_id'];
      $toggle_featured_term = $_GET['toggle_featured_term'];

      $post_terms    = wp_get_post_terms($post_id, 'featured-news-order', ['fields' => 'slugs']);
      $featured_term = get_term_by('slug', $toggle_featured_term, 'featured-news-order');

      if (in_array($toggle_featured_term, $post_terms)) {
         wp_remove_object_terms($post_id, $toggle_featured_term, 'featured-news-order');
      } else {
         wp_set_post_terms($post_id, $featured_term->term_id, 'featured-news-order', true);
      }

      $redirect_url = remove_query_arg(['post_id', 'toggle_featured_term']);
      wp_redirect($redirect_url);
      exit;
   }
}
