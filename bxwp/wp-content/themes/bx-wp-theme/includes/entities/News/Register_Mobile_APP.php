<?php

namespace BX_Obastidor\News;

if (!defined('ABSPATH')) {
   exit;
}

define('BX_OBASTIDOR_MOBILE_API_POSTS_PER_PAGE', 15);

class Register_Mobile_APP
{
   public function __construct()
   {
      add_action('rest_api_init', [$this, 'register_routes']);
      add_action('init', [$this, 'add_rewrite_rules']);
      add_action('query_vars', [$this, 'add_query_vars']);
      add_action('parse_request', [$this, 'endpoint_parse_request']);
   }

   public function register_routes()
   {
      register_rest_route('bx-obastidor/v1', '/mobile-app', [
         'methods'             => 'GET',
         'callback'            => [$this, 'mobile_app_endpoints'],
         'permission_callback' => '__return_true',
      ]);
   }

   public function add_rewrite_rules()
   {
      add_rewrite_rule(
         '^API/APP/news$',
         'index.php?custom_route=/bx-obastidor/v1/mobile-app&endpoint=news',
         'top'
      );

      add_rewrite_rule(
         '^API/APP/news/(\d+)$',
         'index.php?custom_route=/bx-obastidor/v1/mobile-app&endpoint=news&post_id=$matches[1]',
         'top'
      );

      add_rewrite_rule(
         '^API/APP/news/editorias$',
         'index.php?custom_route=/bx-obastidor/v1/mobile-app&endpoint=categories',
         'top'
      );

      add_rewrite_rule(
         '^API/APP/news/editorias/(.+)$',
         'index.php?custom_route=/bx-obastidor/v1/mobile-app&endpoint=categories&category_slug=$matches[1]',
         'top'
      );

      add_rewrite_rule(
         '^API/APP/news/pesquisa$',
         'index.php?custom_route=/bx-obastidor/v1/mobile-app&endpoint=search',
         'top'
      );

      add_rewrite_rule(
         '^API/APP/news/mais_lidas$',
         'index.php?custom_route=/bx-obastidor/v1/mobile-app&endpoint=most-read',
         'top'
      );

      add_rewrite_rule(
         '^API/APP/devices$',
         'index.php?custom_route=/bx-obastidor/v1/mobile-app&endpoint=devices',
         'top'
      );
   }

   public function add_query_vars($query_vars)
   {
      $query_vars[] = 'custom_route';
      $query_vars[] = 'endpoint';
      $query_vars[] = 'post_id';
      $query_vars[] = 'category_slug';
      $query_vars[] = 'query';

      return $query_vars;
   }

   public function endpoint_parse_request($wp)
   {
      $custom_route = $wp->query_vars['custom_route'] ?? null;

      if ($custom_route !== '/bx-obastidor/v1/mobile-app') {
         return;
      }

      $request = new \WP_REST_Request($_SERVER['REQUEST_METHOD'], $custom_route);

      $request->set_query_params([
         ...$wp->query_vars,
         'endpoint'      => $wp->query_vars['endpoint'] ?? '',
         'post_id'       => $wp->query_vars['post_id'] ?? null,
         'category_slug' => $wp->query_vars['category_slug'] ?? null,
         'query'         => $wp->query_vars['query'] ?? null,
      ]);

      $request->set_body_params($_POST);
      $request->set_headers(wp_get_nocache_headers());

      $rest_server = rest_get_server();
      $response    = $rest_server->dispatch($request);

      status_header($response->get_status());
      header('Content-Type: application/json');
      echo json_encode($response->get_data());
      exit;
   }

   public function mobile_app_endpoints($request)
   {
      $endpoint      = $request->get_param('endpoint');
      $post_id       = $request->get_param('post_id');
      $category_slug = $request->get_param('category_slug');
      $search        = $request->get_param('query');

      switch ($endpoint) {
         case 'devices':
            return rest_ensure_response([]);

         case 'categories':
            return $this->handle_categories_endpoint($category_slug, $request);

         case 'news':
            return $this->handle_news_endpoint($post_id, $request);

         case 'most-read':
            return $this->handle_most_read_endpoint($request);

         case 'search':
            return $this->handle_search_endpoint($search, $request);

         default:
            return rest_ensure_response([
               'success' => false,
               'message' => esc_html__('Invalid endpoint', 'bx-obastidor'),
            ]);
      }
   }

   public function handle_categories_endpoint($category_slug, $request)
   {
      if (empty($category_slug)) {
         $categories = get_terms(['taxonomy' => 'category']);

         $data = [];
         foreach ($categories as $category) {
            $data[] = [
               'id'    => $category->term_id,
               'title' => $category->name,
            ];
         }

         return rest_ensure_response(['data' => $data]);
      }

      return rest_ensure_response($this->get_api_dataset([
         'paged'     => max(1, (int) $request->get_param('page')),
         'tax_query' => [
            [
               'taxonomy' => 'category',
               'field'    => 'slug',
               'terms'    => $category_slug,
            ],
         ],
      ]));
   }

   public function handle_news_endpoint($post_id, $request)
   {
      if (empty($post_id)) {
         return rest_ensure_response($this->get_api_dataset([
            'paged' => max(1, (int) $request->get_param('page')),
         ]));
      }

      $query_args = [
         'post__in' => [$post_id],
      ];

      $dataset = $this->get_api_dataset($query_args, [
         'content_blocks' => true,
         'is_single'      => true
      ]);

      $content = $dataset->data['data']['datasets']['content'];

      if (empty($content)) {
         return rest_ensure_response('erro');
      }

      return rest_ensure_response([
         'data' => [
            'news' => $content
         ]
      ]);
   }

   public function handle_most_read_endpoint($request)
   {
      $most_readed_posts = Utils::get_most_readed_posts(10, 60 * 60 * 8);

      return rest_ensure_response($this->get_api_dataset([
         'paged'    => max(1, (int) $request->get_param('page')),
         'post__in' => array_column($most_readed_posts, 'post_ID'),
      ]));
   }

   public function handle_search_endpoint($search, $request)
   {
      return rest_ensure_response($this->get_api_dataset([
         'paged' => max(1, (int) $request->get_param('page')),
         's'     => $search,
      ]));
   }

   public function get_api_dataset($extra_query_args, $extra_dataset_args = [])
   {
      $default_query_args = [
         'post_type'      => 'post',
         'posts_per_page' => BX_OBASTIDOR_MOBILE_API_POSTS_PER_PAGE,
         'orderby'        => 'date',
         'order'          => 'DESC',
         'post_status'    => 'publish'
      ];

      $query_args = wp_parse_args($extra_query_args, $default_query_args);
      $query = new \WP_Query($query_args);

      $current_page   = min((int) ($extra_query_args['paged'] ?? 1), max(1, $query->max_num_pages));
      $offset         = ($current_page - 1) * BX_OBASTIDOR_MOBILE_API_POSTS_PER_PAGE;

      $base_url = remove_query_arg('page', home_url($_SERVER['REQUEST_URI']));

      $content = [];
      foreach ($query->posts as $post) {
         $Post_object = new News($post);

         $content[] = $Post_object->get_mobile_api_data($extra_dataset_args);
      }

      return rest_ensure_response([
         'data' => [
            'datasets' => [
               'content' => $content,
            ]
         ],
         'pagination' => [
            'current_page'   => $current_page,
            'first_page_url' => add_query_arg('page', 1, $base_url),
            'from'           => $query->found_posts > 0 ? $offset + 1 : 0,
            'last_page'      => max(1, $query->max_num_pages),
            'last_page_url'  => add_query_arg('page', max(1, $query->max_num_pages), $base_url),
            'next_page_url'  => $current_page < $query->max_num_pages ? add_query_arg('page', $current_page + 1, $base_url) : null,
            'path'           => $base_url,
            'per_page'       => BX_OBASTIDOR_MOBILE_API_POSTS_PER_PAGE,
            'prev_page_url'  => $current_page > 1 ? add_query_arg('page', $current_page - 1, $base_url) : null,
            'to'             => $query->found_posts > 0 ? min($offset + BX_OBASTIDOR_MOBILE_API_POSTS_PER_PAGE, $query->found_posts) : 0,
            'total'          => $query->found_posts,
         ]
      ]);
   }
}
