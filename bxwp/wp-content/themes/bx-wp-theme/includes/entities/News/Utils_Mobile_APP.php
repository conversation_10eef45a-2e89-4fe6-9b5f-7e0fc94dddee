<?php

namespace BX_Obastidor\News;

if (!defined('ABSPATH')) {
   exit;
}

class Utils_Mobile_APP
{
   public static function get_formatted_blocks($blocks)
   {
      $formatted_blocks = [];
      $block_counter = 1;

      foreach ($blocks as $block) {
         $formatted_block = self::format_block($block);

         if (!empty($formatted_block)) {
            $formatted_blocks[(string)$block_counter] = $formatted_block;
            $block_counter++;
         }
      }

      return $formatted_blocks;
   }

   public static function format_block($block)
   {
      if ($block['blockName'] === 'core/image') {
         return self::format_image_block($block);
      }

      if ($block['blockName'] === 'core/embed') {
         return self::format_embed_block($block);
      }

      return self::format_text_block($block);
   }

   public static function format_image_block($block)
   {
      $image_author    = '';
      $image_caption   = '';
      $image_url       = '';
      $image_mime_type = 'image/jpeg';

      if (isset($block['attrs']['id'])) {
         $attachment_src = wp_get_attachment_image_src($block['attrs']['id'], 'thumbnail');

         if ($attachment_src && isset($attachment_src[0])) {
            $image_url = $attachment_src[0];
            $image_mime_type = get_post_mime_type($block['attrs']['id']) ?: 'image/jpeg';

            $attachment_post = get_post($block['attrs']['id']);
            if ($attachment_post) {
               $image_author  = $attachment_post->post_title;
               $image_caption = $attachment_post->post_excerpt;
            }
         }
      } else {
         $dom = new \DOMDocument();
         \libxml_use_internal_errors(true);

         $dom->loadHTML($block['innerHTML']);

         $img_elements = $dom->getElementsByTagName('img');
         if ($img_elements->length > 0) {
            $img_element     = $img_elements->item(0);
            $image_url       = $img_element->getAttribute('src');
            $image_mime_type = self::get_mime_type_from_url($image_url);
         }

         $figcaption_elements = $dom->getElementsByTagName('figcaption');
         if ($figcaption_elements->length > 0) {
            $figcaption_element = $figcaption_elements->item(0);
            $image_caption = trim($figcaption_element->textContent);
         }
      }

      return [
         'type'       => 'image',
         'type_id'    => 2,
         'content'    => null,
         'img_url'    => $image_url,
         'mime_type'  => $image_mime_type,
         'properties' => [
            'author'  => $image_author,
            'caption' => $image_caption,
         ],
      ];
   }

   public static function format_embed_block($block)
   {
      $url = isset($block['attrs']['url'])
         ? $block['attrs']['url']
         : self::get_attribute($block['innerHTML'], 'iframe', 'src');

      $oembed = _wp_oembed_get_object();
      $oembed_data = $oembed->get_data($url);

      if (!$oembed_data) {
         return [
            'type'    => 'text',
            'type_id' => 1,
            'content' => sprintf('<p><a href="%s">%s</a></p>', $url, $url),
         ];
      }

      if ($oembed_data->provider_name === 'Vimeo' && !empty($oembed_data->video_id)) {
         return [
            'type'       => 'vimeo',
            'type_id'    => 3,
            'content'    => null,
            'properties' => [
               'vimeo_id' => $oembed_data->video_id,
            ],
         ];
      }

      if ($oembed_data->provider_name === 'YouTube') {
         preg_match(
            '/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/',
            $block['attrs']['url'],
            $matches
         );

         if (!empty($matches[1])) {
            return [
               'type'       => 'youtube',
               'type_id'    => 4,
               'content'    => null,
               'properties' => [
                  'youtube_id' => $matches[1],
               ],
            ];
         }
      }

      $html = $oembed_data->html;

      $dom = new \DOMDocument();
      @$dom->loadHTML($html, \LIBXML_HTML_NOIMPLIED | \LIBXML_HTML_NODEFDTD);

      if ($iframe = $dom->getElementsByTagName('iframe')->item(0)) {

         // action with powers to modify the iframe object directly
         do_action_ref_array('bx_obastidor_iframe_' . $oembed_data->provider_name, [&$iframe]);

         $html = $dom->saveHTML();
      }

      return [
         'type'    => 'embed',
         'type_id' => 5,
         'content' => null,
         'code'    => trim($html),
      ];
   }

   public static function format_text_block($block)
   {
      $content = trim(render_block($block), "\n");

      if (empty($content)) {
         return null;
      }

      $dom = new \DOMDocument();
      @$dom->loadHTML('<?xml encoding="UTF-8">' . $content, \LIBXML_HTML_NOIMPLIED | \LIBXML_HTML_NODEFDTD);

      $links = $dom->getElementsByTagName('a');

      foreach ($links as $link) {
         $href = $link->getAttribute('href');

         if (filter_var($href, \FILTER_VALIDATE_URL)) {
            $href_data = parse_url($href);

            if (!$href_data || !isset($href_data['scheme']) || !in_array($href_data['scheme'], ['http', 'https'])) {
               continue;
            }

            $home_url_data = parse_url(home_url());
            if ($home_url_data && isset($home_url_data['host']) && $href_data['host'] === $home_url_data['host']) {
               $internal_url = $href;
            }
         } else {
            $internal_url = get_site_url() . '/' .  trim($href, '/');
         }

         if (!isset($internal_url)) {
            continue;
         }

         $post_id = url_to_postid($internal_url);

         if (!$post_id) {
            continue;
         }

         $mobile_url = get_site_url() . '/news-id-' . $post_id;

         $link->setAttribute('href', $mobile_url);
      }

      $content = $dom->saveHTML($dom->documentElement);

      return [
         'type'    => 'text',
         'type_id' => 1,
         'content' => $content,
      ];
   }

   public static function get_formatted_image_data($post_object, $is_single)
   {
      $featured_image_id = get_post_thumbnail_id($post_object->ID);
      $default_image_url = get_stylesheet_directory_uri() . BX_DEFAULT_FEATURED_IMAGE;

      $image_url        = $default_image_url;
      $image_srcset     = $default_image_url;
      $image_mime_type  = 'image/jpeg';
      $image_media_type = 'image';

      if ($featured_image_id) {
         $image_src = wp_get_attachment_image_src($featured_image_id, 'thumbnail');

         if ($image_src && isset($image_src[0])) {
            $image_url        = $image_src[0];
            $image_srcset     = wp_get_attachment_image_srcset($featured_image_id, 'full');
            $image_mime_type  = get_post_mime_type($featured_image_id) ?? 'image/jpeg';
            $image_media_type = explode('/', $image_mime_type)[0] ?? 'image';
         }
      }

      $url_key    = $is_single ? 'img_url' : 'URL';
      $srcset_key = $is_single ? 'img_srcset' : 'Srcset';

      return [
         $url_key     => $image_url,
         $srcset_key  => $image_srcset,
         'author'     => $featured_image_id ? get_post($featured_image_id)->post_title : null,
         'mime_type'  => $image_mime_type,
         'media_type' => $image_media_type,
      ];
   }

   private static function get_mime_type_from_url($url)
   {
      if (!function_exists('download_url')) {
         require_once ABSPATH . 'wp-admin/includes/file.php';
      }

      $temp_file_path = \download_url($url);

      if (is_wp_error($temp_file_path)) {
         return 'image/jpeg';
      }

      $file_type = wp_check_filetype($temp_file_path);
      $mime_type = $file_type['type'] ?? 'image/jpeg';
      unlink($temp_file_path);

      return $mime_type;
   }

   private static function get_attribute($html, $tag, $attribute)
   {
      $dom = new \DOMDocument();
      @$dom->loadHTML($html, \LIBXML_HTML_NOIMPLIED | \LIBXML_HTML_NODEFDTD);
      $element = $dom->getElementsByTagName($tag)->item(0);

      return $element ? $element->getAttribute($attribute) : null;
   }
}
