<?php

/**
 * Name: News
 * Type: BX_Post
 * Taxonomies: category, tag, featured_news_order
 * Post Meta: bx_obastidor_exclusive_content
 * Description: Post padrão com funcionalidades específicas para notícias.
 */

namespace BX_Obastidor\News;

if (!defined('ABSPATH')) {
   exit;
}

if (!defined('BX_DEFAULT_FEATURED_IMAGE')) {
   define('BX_DEFAULT_FEATURED_IMAGE', '/assets/images/news-default-thumbnail.jpg');
}

class News extends \BX_Obastidor\BX_Post\BX_Post
{
   public function get_excerpt(int $excerpt_size = 20, string $excerpt_more = '...')
   {
      if (empty($excerpt_size)) {
         return \get_the_excerpt($this->ID);
      }

      return \wp_trim_words(\get_the_excerpt($this->ID), $excerpt_size, $excerpt_more);
   }

   public function has_label()
   {
      if ($this->is_exclusive()) {
         return true;
      }

      if ($this->get_post_type() === 'sponsored_content') {
         return true;
      }

      if (!empty($this->get_category())) {
         return true;
      }

      return false;
   }

   public function get_label()
   {
      // allow to short cut get label returning a string
      $label = apply_filters('bx_news_get_label', false, $this->ID);

      if ($label) {
         return $label;
      }

      if ($this->is_exclusive()) {
         return 'Exclusivo';
      }

      $category = $this->get_category();

      if (!$category) {
         return '';
      }

      return $category->get_name();
   }

   public function get_label_link()
   {
      $label_link = apply_filters('bx_news_get_label_link', false, $this->ID);

      if ($label_link) {
         return $label_link;
      }

      if ($this->is_exclusive()) {
         return '';
      }

      $category = $this->get_category();

      if (!$category) {
         return '';
      }

      return $category->get_link();
   }

   public function get_category()
   {
      if (is_category()) {
         $current_category = get_queried_object();
         if ($current_category && is_object($current_category)) {
            return new \BX_Obastidor\BX_Taxonomy\BX_Taxonomy($current_category);
         }
      }

      $categories = \get_the_category($this->ID);

      if (!is_array($categories) || empty($categories)) {
         return;
      }

      if (function_exists('yoast_get_primary_term_id') && !empty(yoast_get_primary_term_id('category', $this->ID))) {
         $first_post_category = get_category(yoast_get_primary_term_id('category', $this->ID));
      } else {
         $first_post_category = $categories[0];
      }

      return new \BX_Obastidor\BX_Taxonomy\BX_Taxonomy($first_post_category);
   }

   public function is_exclusive()
   {
      return (bool) get_post_meta($this->ID, 'bx_obastidor_exclusive_content', true);
   }

   public function get_mobile_api_data($args = [])
   {
      $content_blocks = $args['content_blocks'] ?? false;
      $is_single      = $args['is_single'] ?? false;

      $image_author = $this->has_thumbnail()
         ? preg_replace(
            '/^Foto: /',
            '',
            $this->get_thumbnail_title()
         )
         : __('O Bastidor', 'bx-obastidor');

      $post_data = [
         'id'            => $this->get_the_ID(),
         'active'        => $this->get_status() === 'publish' ? 1 : 0,
         'title'         => $this->get_title(),
         'section_title' => $this->get_label(),
         'author_name'   => $this->get_author()->get_name(),
         'author_email'  => $this->get_author()->get_email(),
         'properties'    => [
            'exclusivo'     => $this->is_exclusive(),
            'image_author'  => $image_author,
            'image_caption' => $this->get_thumbnail_caption(),
         ],
         'date_published' => $this->get_date('d/m/Y \– H:i'),
         'url'            => wp_make_link_relative($this->get_link()),
         'image'          => Utils_Mobile_APP::get_formatted_image_data($this->post, $is_single)
      ];

      if ($content_blocks) {
         $post_data['blocks'] = Utils_Mobile_APP::get_formatted_blocks(
            $this->get_blocks(),
            $this->get_thumbnail_title(),
            $this->get_thumbnail_caption(),
         );
      }

      return $post_data;
   }
}
