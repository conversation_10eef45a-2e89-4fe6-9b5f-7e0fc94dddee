<?php

/**
 * Name: News
 * Type: BX_Post
 * Taxonomies: category, tag, featured_news_order
 * Post Meta: bx_obastidor_exclusive_content
 * Description: Post padrão com funcionalidades específicas para notícias.
 */

namespace BX_Obastidor\News;

if (!defined('ABSPATH')) {
   exit;
}

class News extends \BX_Obastidor\BX_Post\BX_Post
{
   public function get_excerpt(int $excerpt_size = 20, string $excerpt_more = '...')
   {
      if (empty($excerpt_size)) {
         return \get_the_excerpt($this->ID);
      }

      return \wp_trim_words(\get_the_excerpt($this->ID), $excerpt_size, $excerpt_more);
   }

   public function has_label()
   {
      if ($this->is_exclusive()) {
         return true;
      }

      if ($this->get_post_type() === 'sponsored_content') {
         return true;
      }

      if (!empty($this->get_category())) {
         return true;
      }

      return false;
   }

   public function get_label()
   {
      // allow to short cut get label returning a string
      $label = apply_filters('bx_news_get_label', false, $this->ID);

      if ($label) {
         return $label;
      }

      if ($this->is_exclusive()) {
         return 'Exclusivo';
      }

      $category = $this->get_category();

      if (!$category) {
         return '';
      }

      return $category->get_name();
   }

   public function get_label_link()
   {
      $label_link = apply_filters('bx_news_get_label_link', false, $this->ID);

      if ($label_link) {
         return $label_link;
      }

      if ($this->is_exclusive()) {
         return '';
      }

      $category = $this->get_category();

      if (!$category) {
         return '';
      }

      return $category->get_link();
   }

   public function get_category()
   {
      $categories = \get_the_category($this->ID);

      if (!is_array($categories) || empty($categories)) {
         return;
      }

      return new \BX_Obastidor\BX_Taxonomy\BX_Taxonomy($categories[0]);
   }

   public function is_exclusive()
   {
      return (bool) get_post_meta($this->ID, 'bx_obastidor_exclusive_content', true);
   }

   public function get_post_object()
   {
      return apply_filters('bx_news_get_post_object', $this, $this->post);
   }
}
