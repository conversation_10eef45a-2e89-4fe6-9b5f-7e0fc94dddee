<?php

namespace BX_Obastidor\CommonAds;

if (!defined('ABSPATH')) {
   exit;
}

/**
 * Name: CommonAds
 * Type: News
 * Taxonomies: category, tag, featured_news_order
 * Post Meta: bx_obastidor_unpublish_date, bx_obastidor_external_link
 * Description: Posts regulares com capacidade de agendamento de publicação e despublicação.
 */
class CommonAds extends \BX_Obastidor\News\News
{
   public function unpublish()
   {
      if (in_array($this->post->post_status, ['publish', 'private'])) {
         $this->post->post_status = 'draft';
         \wp_update_post($this->post);
      }
   }
}
