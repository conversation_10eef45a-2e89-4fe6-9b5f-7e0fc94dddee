<?php

namespace BX_Obastidor\CommonAds;

if (!defined('ABSPATH')) {
   exit;
}

class Utils
{
   public static function schedule_unpublishing_post($post_id)
   {
      $end_date = get_post_meta($post_id, 'bx_obastidor_unpublish_date', true);

      if (!$end_date) {
         return;
      }

      $unscheduling_posts = get_option('bx_unscheduling_posts', []);

      $unscheduling_posts[$post_id] = [
         'timestamp' => strtotime($end_date),
      ];

      update_option('bx_unscheduling_posts', $unscheduling_posts);
   }
}
