<?php

namespace BX_Obastidor\CommonAds;

if (!defined('ABSPATH')) {
   exit;
}

class Register
{
   public function __construct()
   {
      \add_action('init', [$this, 'unpublish_expired_content']);

      \add_filter('manage_ad_banner_posts_columns', [$this, 'add_post_date_columns']);
      \add_filter('manage_edit-ad_banner_sortable_columns', [$this, 'make_date_columns_sortable']);
      \add_action('manage_ad_banner_posts_custom_column', [$this, 'add_post_date_columns_content'], 20, 2);

      \add_filter('manage_sponsored_content_posts_columns', [$this, 'add_post_date_columns']);
      \add_filter('manage_edit-sponsored_content_sortable_columns', [$this, 'make_date_columns_sortable']);
      \add_action('manage_sponsored_content_posts_custom_column', [$this, 'add_post_date_columns_content'], 20, 2);
   }

   public function unpublish_expired_content()
   {
      $current_timestamp = current_time('Y-m-d\TH:i:s'); // ISO format for DateTimePicker compatibility

      $next_run = get_transient('bx_unscheduling_next_run');

      if ($next_run && $next_run > $current_timestamp) {
         return;
      }

      $query = new \WP_Query([
         'post_type'   => ['ad_banner', 'sponsored_content'],
         'meta_query'  => [
            [
               'key'     => 'bx_obastidor_unpublish_date',
               'value'   => $current_timestamp,
               'compare' => '<=',
               'type'    => 'DATETIME'
            ],
         ],
         'fields' => 'ids',
      ]);

      $unscheduling_posts = $query->posts;

      foreach ($unscheduling_posts as $post_id) {
         $Ad = new \BX_Obastidor\CommonAds\CommonAds($post_id);
         $Ad->unpublish();
      }

      $dateTime = new \DateTime('+1 minute', new \DateTimeZone(wp_timezone_string()));
      $next_run = $dateTime->format('Y-m-d\TH:i:s');

      set_transient('bx_unscheduling_next_run', $next_run); // limits execution to once per minute
   }

   public function add_post_date_columns($columns)
   {
      unset($columns['date']);
      $columns['start_date'] = __('Início', 'bx-obastidor');
      $columns['end_date']   = __('Fim', 'bx-obastidor');

      return $columns;
   }

   public function make_date_columns_sortable($columns)
   {
      $columns['start_date'] = 'start_date';
      $columns['end_date']   = 'end_date';

      return $columns;
   }

   public function add_post_date_columns_content($column_name, $post_id)
   {
      if ($column_name === 'start_date') {
         $start_date = get_the_date('Y-m-d H:i:s', $post_id);

         if ($start_date) {
            echo date('d/m/Y H:i', strtotime($start_date));
         } else {
            echo '-';
         }
      }

      if ($column_name === 'end_date') {
         $end_date = get_post_meta($post_id, 'bx_obastidor_unpublish_date', true);

         if ($end_date) {
            echo date('d/m/Y H:i', strtotime($end_date));
         } else {
            echo '-';
         }
      }
   }
}
