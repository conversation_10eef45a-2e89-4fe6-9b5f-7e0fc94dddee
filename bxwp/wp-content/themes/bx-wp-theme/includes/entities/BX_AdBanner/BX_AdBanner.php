<?php
namespace BX_Obastidor\BX_AdBanner;

if (!defined('ABSPATH')) {
   exit;
}

/**
 * Ad Banner
 *
 * Banners for site ads
 */
class BX_AdBanner extends \BX_Obastidor\BX_SponsoredContent\BX_SponsoredContent
{
   /**
    * Unpublish the post.
    */
   public function unpublish()
   {
      $this->post->post_status = 'draft';
      \wp_update_post($this->post);
   }

   /**
    * Get the sponsor name.
    *
    * @return string
    */
   public function get_sponsor_name()
   {
      return \get_field('bx_obastidor_sponsored_content_sponsor', $this->ID) ?? 'Conteúdo Patrocinado';
   }

   /**
    * Get the sponsor link.
    *
    * @return string
    */
   public function get_sponsor_link()
   {
      return \get_field('bx_obastidor_sponsored_content_external_link', $this->ID);
   }

}
