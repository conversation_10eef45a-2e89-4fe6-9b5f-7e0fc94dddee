<?php
namespace BX_Obastidor\BX_AdBanner;

if (!defined('ABSPATH')) {
   exit;
}

class Utils
{
   public static function get_ad_banners_by_slot($slot, $override_query = [])
   {
      $slot_id = is_numeric($slot)
         ? $slot
         : \get_term_by('slug', $slot, 'ad_banner_slot')->term_id;

      $default_query_args = [
         'post_type' => 'ad_banner',
         'status' => 'publish',
         'orderby' => 'date',
         'order' => 'DESC',
         'tax_query' => [
            [
               'taxonomy' => 'ad_banner_slot',
               'field' => 'term_id',
               'terms' => $slot_id,
            ]
         ],
      ];

      $parsed_query_args = \wp_parse_args(
         $override_query,
         $default_query_args
      );

      $posts = \get_posts($parsed_query_args);

      foreach ($posts as $post) {
         $slots = array_map(function ($term) {
            return $term->slug;
         }, get_the_terms($post, 'ad_banner_slot'));
         $slug = $post->post_name;
         $status = $post->post_status;
      }
      return $posts;
   }

   public static function get_random_ad_banner_by_slot($slot) {
      if (empty($slot)) {
         return null;
      }

      if (is_numeric($slot)) {
         $slot_id = $slot;
      } else {
         $term = \get_term_by('slug', $slot, 'ad_banner_slot');
         if (empty($term)) {
            throw new \Exception(sprintf(__('%s is not a valid slot', 'bx-obastidor'), $slot));
         }
         $slot_id = $term->term_id;
      }

      $post = \get_posts([
         'post_type' => 'ad_banner',
         'status' => 'publish',
         'orderby' => 'rand',
         'posts_per_page' => 1,
         'tax_query' => [
            [
               'taxonomy' => 'ad_banner_slot',
               'field' => 'term_id',
               'terms' => $slot_id,
            ]
         ],
      ]);

      return count($post) > 0 ? new BX_AdBanner($post[0]) : null;
   }
}
