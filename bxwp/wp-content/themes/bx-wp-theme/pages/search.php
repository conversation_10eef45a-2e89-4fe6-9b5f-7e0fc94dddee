<?php

if (!defined('ABSPATH')) {
   exit;
}

get_component('header');

?>

<main class="container flex flex-col">

   <section class="flex flex-col gap-4 border-b border-neutral-300 py-8">
      <h1 class="text-[2rem] lg:text-[2.6rem] font-primary">
         <?php printf(esc_html__('Resultado da busca por "%s"', 'bx-obastidor'), '<span class="font-bold">' . get_search_query() . '</span>'); ?>
      </h1>

      <p class="text-primary-400 text-lg">
         <?php printf(esc_html(_n('Foi encontrado %s resultado', 'Foram encontrados %s resultados', $wp_query->found_posts, 'bx-obastidor')), $wp_query->found_posts); ?>
      </p>
   </section>

   <section class="flex flex-col md:flex-row gap-8 py-8">
      <div class="md:w-2/3 md:border-r md:border-neutral-300 md:pr-8">
         <h2 class="sr-only"><?php esc_html_e('Últimas Notícias', 'bx-obastidor'); ?></h2>
         <?php

         get_component('news-list', [
            'query_args' => [
               's' => get_search_query()
            ]
         ]);

         ?>
      </div>

      <aside class="md:w-1/3">
         <?php

         get_component('most-readed-widget');

         ?>
      </aside>
   </section>

</main>
<?php

get_component('footer');
