<?php

if (!defined('ABSPATH')) {
   exit;
}

use BX_Obastidor\Author\Author;

$Author = new Author(get_the_author_meta('ID'));

get_component('header');

?>

<main class="flex flex-col">

   <section class="bg-neutral-100 py-10">
      <div class="container font-secondary flex flex-col lg:flex-row lg:items-center gap-8">
         <div class="lg:w-1/2 flex items-start gap-4">
            <div class="w-24 h-24 rounded-full overflow-hidden">
               <?php echo $Author->get_avatar(); ?>
            </div>
            <div>
               <h1 class="text-[2rem] lg:text-[2.6rem] font-primary font-bold">
                  <?php echo $Author->get_name(); ?>
               </h1>
               <a href="mailto:<?php echo $Author->get_email(); ?>" class="text-neutral-500 text-sm">
                  <?php echo $Author->get_email(); ?>
               </a>
               <?php

               $social_links = $Author->get_social_links();

               if (!empty($social_links)) {

               ?>
                  <ul class="flex items-center gap-2 mt-3">
                     <?php

                     foreach ($Author->get_social_links() as $social_network => $url) {
                        if (empty($url)) {
                           continue;
                        }

                     ?>
                        <li>
                           <a href="<?php echo esc_url($url); ?>" class="flex items-center justify-center bg-neutral-300 p-1.5 rounded-md transition-all" target="_blank" title="<?php echo esc_attr(ucfirst($social_network)); ?>">
                              <?php echo render_svg('social-media/' . $social_network, 'w-5 h-5'); ?>
                           </a>
                        </li>
                     <?php

                     }

                     ?>
                  </ul>
               <?php

               }

               ?>
            </div>
         </div>
         <?php

         $description = $Author->get_description();

         if (!empty($description)) {

         ?>
            <div class="lg:w-1/2">
               <p class="text-neutral-500 text-sm">
                  <?php echo $description; ?>
               </p>
            </div>
         <?php

         }

         ?>
      </div>
   </section>

   <section class="container flex flex-col md:flex-row gap-8 py-8">
      <div class="md:w-2/3 md:border-r md:border-neutral-300 md:pr-8">
         <h2 class="font-primary text-[1.7rem] font-bold underline mb-6"><?php esc_html_e('Últimas Notícias', 'bx-obastidor'); ?></h2>
         <?php

         get_component('news-list', ['query_args' => ['author' => $Author->user_ID]]);

         ?>
      </div>

      <aside class="md:w-1/3">
         <?php

         get_component('most-readed-widget');

         ?>
      </aside>
   </section>

</main>
<?php

get_component('footer');
