<?php

if (!defined('ABSPATH')) {
   exit;
}

use BX_Obastidor\BX_Taxonomy\BX_Taxonomy;

$TaxTerm = new BX_Taxonomy(get_queried_object());

$query_args = [
   'tax_query' => [
      [
         'taxonomy' => $TaxTerm->get_taxonomy(),
         'field'    => 'term_id',
         'terms'    => $TaxTerm->get_the_ID()
      ]
   ]
];

get_component('header');

?>

<main class="container flex flex-col">

   <?php
   if ($TaxTerm->get_taxonomy() === 'category') {
      get_component('ad-banner', ['slot' => 'section-top', 'container_classes' => 'mt-6']);
   }
   ?>

   <section class="flex flex-col md:flex-row gap-8 border-b border-neutral-300 py-8">
      <h1 class="text-[2.6rem] font-primary font-bold">
         <?php echo $TaxTerm->get_name(); ?>
      </h1>
      <?php

      $description = $TaxTerm->get_description();

      if (!empty($description)) {

      ?>
         <p class="text-primary-400 text-sm">
            <?php echo $description; ?>
         </p>
      <?php

      }

      ?>
   </section>

   <section class="flex flex-col md:flex-row gap-8 py-8">
      <div class="md:w-2/3 md:border-r md:border-neutral-300 md:pr-8">
         <h2 class="sr-only"><?php esc_html_e('Últimas Notícias', 'bx-obastidor'); ?></h2>
         <?php

         get_component('news-list', ['query_args' => $query_args]);

         ?>
      </div>

      <aside class="md:w-1/3">
      <?php

         get_component('most-readed-widget');
         if ($TaxTerm->get_taxonomy() === 'category') {
            get_component('ad-banner', ['slot' => 'section-maislidas-after', 'container_classes' => 'my-2']);
         }

      ?>
      </aside>
   </section>

</main>
<?php

get_component('footer');
