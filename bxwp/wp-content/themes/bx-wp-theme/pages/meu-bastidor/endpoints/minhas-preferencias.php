<?php

use BX_Obastidor\BX_Taxonomy\BX_Taxonomy;
use BX_Obastidor\Author\Author;

if (!defined('ABSPATH')) {
   exit;
}

get_component('header');

$Author = new Author(get_current_user_id());

?>

<main class="container" x-data="accountModals">

   <article class="py-8 lg:py-16">
      <header>
         <h1 class="text-[2.6rem] font-primary font-bold mb-4">
            <?php esc_html_e('Minhas preferências', 'bx-obastidor'); ?>
         </h1>
      </header>

      <div class="flex flex-col gap-4 md:w-2/3">

         <p>
            <?php esc_html_e('E-mail cadastrado:', 'bx-bastidor'); ?>
            <span class="text-highlight-500"><?php echo $Author->get_email(); ?></span>
         </p>
         <form>
            <h2 class="text-xl font-primary font-semibold mb-7">
               <?php esc_html_e('Escolha os assuntos que você quer seguir:', 'bx-bastidor'); ?>
            </h2>
            <div class="flex flex-col gap-7 max-w-xs">
               <?php

               $categories = get_categories([
                  'exclude'    => get_cat_ID('Sem categoria'),
                  'orderby'    => 'name',
                  'order'      => 'ASC',
                  'hide_empty' => false,
               ]);

               foreach ($categories as $category) {
                  $Category = new BX_Taxonomy($category);

               ?>
                  <label class="toggle-container">
                     <input type="checkbox" class="toggle-input" name="categories[]" value="<?php esc_attr_e($Category->get_the_ID()) ?>">
                     <div class="toggle-switch"></div>
                     <span class="toggle-label"><?php esc_html_e($Category->get_name()); ?></span>
                  </label>
               <?php

               }

               ?>
            </div>
         </form>
         <form>
            <h2 class="text-xl font-primary font-semibold mb-7">
               <?php esc_html_e('Escolha quais notificações você receber:', 'bx-bastidor'); ?>
            </h2>
            <div class="flex flex-col gap-7 max-w-xs mb-8">
               <label class="toggle-container">
                  <input type="checkbox" id="assunto-politica" class="toggle-input">
                  <div class="toggle-switch"></div>
                  <span class="toggle-label"><?php esc_html_e('Alertas e notícias exclusivas', 'bx-bastidor'); ?></span>
               </label>

               <label class="toggle-container">
                  <input type="checkbox" id="assunto-justica" class="toggle-input">
                  <div class="toggle-switch"></div>
                  <span class="toggle-label"><?php esc_html_e('Destaques do dia', 'bx-bastidor'); ?></span>
               </label>

               <label class="toggle-container">
                  <input type="checkbox" id="assunto-economia" class="toggle-input" checked>
                  <div class="toggle-switch"></div>
                  <span class="toggle-label"><?php esc_html_e('Destaques da semana', 'bx-bastidor'); ?></span>
               </label>

            </div>

            <div class="mt-4">
               <button type="submit" class="btn-primary"><?php esc_html_e('Salvar', 'bx-bastidor'); ?></button>

               <a href="<?php echo home_url('meu-bastidor'); ?>" class="btn-primary"><?php esc_html_e('Ir para Meu Bastidor', 'bx-bastidor'); ?></a>
            </div>
         </form>

         <div class="flex gap-8">
            <a href="<?php echo home_url(); ?>"><?php esc_html_e('Voltar ao início', 'bx-bastidor'); ?></a>
            <button id="account-remove-link" class="ml-auto" x-on:click.prevent="openRemovalModal()"><?php esc_html_e('Remover Cadastro', 'bx-bastidor'); ?></button>
            <a href="<?php echo wp_logout_url(home_url()); ?>"><?php esc_html_e('Sair', 'bx-bastidor'); ?></a>
         </div>

      </div>

   </article>

   <div x-show="showRemovalModal" x-cloak class="fixed left-0 top-0 flex w-[100vw] h-[100vh] items-center justify-center bg-black/50 z-50">
      <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
         <figure class="flex items-center justify-center mb-8">
            <?php render_svg('alert', 'w-32 h-32 text-warning-500'); ?>
         </figure>
         <h1 class="text-center text-3xl font-primary font-bold">
            <?php esc_html_e('Remover Cadastro', 'bx-bastidor'); ?>
         </h1>
         <p class="text-center text-lg my-8">
            <?php esc_html_e('Tem certeza que deseja remover seu cadastro?', 'bx-bastidor'); ?>
         </p>
         <div class="flex justify-center gap-4">
            <button x-on:click="deleteAccount()" x-bind:disabled="isLoading" class="btn-primary !bg-red-500 disabled:opacity-50 disabled:cursor-not-allowed">
               <span x-show="!isLoading"><?php esc_html_e('Sim', 'bx-bastidor'); ?></span>
               <span x-show="isLoading"><?php esc_html_e('Removendo...', 'bx-bastidor'); ?></span>
            </button>
            <button x-on:click="closeRemovalModal()" class="btn-primary !bg-neutral-500">
               <?php esc_html_e('Não', 'bx-bastidor'); ?>
            </button>
         </div>
      </div>
   </div>

</main>
<?php

get_component('footer');
