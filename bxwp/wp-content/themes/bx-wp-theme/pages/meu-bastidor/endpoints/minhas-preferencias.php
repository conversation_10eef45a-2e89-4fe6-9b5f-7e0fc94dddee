<?php

if (!defined('ABSPATH')) {
   exit;
}

use BX_Obastidor\BX_Taxonomy\BX_Taxonomy;
use BX_Obastidor\Author\Author;

$Author = new Author(get_current_user_id());

$newsletters = BX_Obastidor\Newsletter\Utils::get_newsletters_lists();

get_component('header');

?>

<main class="container flex font-secondary" x-data="meuBastidorData">

   <article class="flex flex-col w-full gap-4 py-8">

      <header>
         <h1 class="text-2xl font-bold mb-4">
            <?php esc_html_e('Minhas preferências', 'bx-obastidor'); ?>
         </h1>
      </header>

      <form x-on:submit.prevent="savePreferences()" class="flex flex-col gap-4 md:w-2/3">

         <fieldset class="flex flex-col md:flex-row gap-4 w-full">
            <legend class="mb-6">
               <?php esc_html_e('Configurações da Conta', 'bx-bastidor'); ?>
               <br>
               <span class="text-neutral-600">
                  <?php esc_html_e('Gerencie suas informações básicas e altere sua senha sempre que precisar', 'bx-bastidor'); ?>
               </span>
            </legend>

            
            <label class="w-full md:w-1/2">
               
               <span
                  x-cloak x-show="!isValid.first_name"
                  class="text-xs text-alert-500 bg-white p-1 ml-2 mb-[-0.75em] z-10"
               >
                  <?php esc_html_e('Nome', 'bx-bastidor');?>
               </span>

               <input
                  name="first_name"
                  type="text"
                  placeholder="<?php
                     esc_html_e('Nome', 'bx-bastidor');
                  ?>"
                  class="w-full text-xs py-4 px-2 border border-neutral-300 rounded-md"
                  required
                  x-on:keydown="setFieldValidity($el)"
                  x-on:blur="setFieldValidity($el)"
                  x-model="preferences.current_user.first_name"
                  :class="{'border-alert-500': !isValid.first_name}"
               />

               <span
                  x-cloak x-show="!isValid.first_name"
                  class="text-xs text-alert-500 p-1 ml-2"
               >
                  <?php esc_html_e('Este campo é obrigatório', 'bx-bastidor');?>
               </span>

            </label>

            <label class="w-full md:w-1/2">
               
               <span
                  x-cloak x-show="!isValid.last_name"
                  class="text-xs text-alert-500 bg-white p-1 ml-2 mb-[-0.75em] z-10"
               >
                  <?php esc_html_e('Sobrenome', 'bx-bastidor');?>
               </span>

               <input
                  name="last_name"
                  type="text"
                  placeholder="<?php
                     esc_html_e('Sobrenome', 'bx-bastidor');
                  ?>"
                  class="w-full text-xs py-4 px-2 border border-neutral-300 rounded-md"
                  x-on:keydown="setFieldValidity($el)"
                  x-on:blur="setFieldValidity($el)"
                  x-model="preferences.current_user.last_name"
                  :class="{'border-alert-500': !isValid.last_name}"
                  required
               />

               <span x-cloak x-show="!isValid.last_name" class="text-xs text-alert-500 p-1 ml-2">
                  <?php esc_html_e('Este campo é obrigatório', 'bx-bastidor');?>
               </span>

            </label>

         </fieldset>

         <fieldset class="flex flex-col md:flex-row gap-4 w-full">
            
            <label class="w-full md:w-1/2">
               
               <span
                  x-cloak x-show="!isValid.email"
                  class="text-xs text-alert-500 bg-white p-1 ml-2 mb-[-0.75em] z-10"
               >
                  <?php esc_html_e('E-mail', 'bx-bastidor');?>
               </span>

               <input
                  name="email"
                  type="email"
                  placeholder="<?php
                     esc_html_e('E-mail', 'bx-bastidor');
                  ?>"
                  class="w-full text-xs py-4 px-2 border border-neutral-300 rounded-md"
                  x-on:keydown="setFieldValidity($el)"
                  x-on:blur="setFieldValidity($el)"
                  x-model="preferences.current_user.email"
                  :class="{'border-alert-500': !isValid.email}"
                  disabled
               />

               <span x-cloak x-show="!isValid.email" class="text-xs text-alert-500 p-1 ml-2">
                  <?php esc_html_e('Digite um e-mail válido', 'bx-bastidor');?>
               </span>

            </label>

            <label class="w-full md:w-1/2">
               
               <span
                  x-cloak x-show="!isValid.password"
                  class="text-xs text-alert-500 bg-white p-1 ml-2 mb-[-0.75em] z-10"
               >
                  <?php esc_html_e('Senha', 'bx-bastidor');?>
               </span>

               <input
                  name="password"
                  type="password"
                  placeholder="<?php
                     esc_html_e('Senha', 'bx-bastidor');
                  ?>"
                  class="w-full text-xs py-4 px-2 border border-neutral-300 rounded-md"
                  x-on:keydown="setFieldValidity($el)"
                  x-on:blur="setFieldValidity($el)"
                  x-model="preferences.current_user.password"
                  :class="{'border-alert-500': !isValid.password}"
                  minlength="8"
                  pattern="(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).*"
                  required
               />

               <span
                  x-cloak x-show="!isValid.password"
                  class="text-xs text-alert-500 p-1 ml-2"
               >
               <?php esc_html_e('Digite uma senha válida com pelo menos 8 caracteres, uma letra maiúscula, uma letra minúscula e um número.', 'bx-bastidor');?>
               </span>

            </label>
         
         </fieldset>
         
         <fieldset class="flex flex-wrap gap-4 w-full mt-6">
            
            <legend class="mb-6">
               <?php esc_html_e('Assuntos de interesse', 'bx-bastidor'); ?>
            </legend>
            
            <?php
               $categories = get_categories(
                  [
                     'exclude'    => get_cat_ID('Sem categoria'),
                     'orderby'    => 'name',
                     'order'      => 'ASC',
                     'hide_empty' => false,
                  ]
               );
               
               foreach ($categories as $category) {

                  $Category = new BX_Taxonomy($category);

                  ?>
            <label class="toggle-container">
               <input type="checkbox" class="toggle-input" x-bind:disabled="isLoading" x-bind:checked="preferences.categories.includes(<?php esc_attr_e($Category->get_the_ID()) ?>)" x-on:change="toggleCategory(<?php esc_attr_e($Category->get_the_ID()) ?>)">
               <div class="toggle-checkbox">
                  <?php render_svg('check', 'w-4 h-4 text-white'); ?>
               </div>
               <span class="toggle-label"><?php esc_html_e($Category->get_name()); ?></span>
            </label>
                  <?php

                  }

                  ?>
         </fieldset>
         
         <fieldset class="flex flex-wrap gap-4 w-full mt-6">
            
            <legend class="mb-6">
               <?php esc_html_e('Newsletters cadastradas', 'bx-bastidor'); ?>
            </legend>
            
            <?php
            foreach ($newsletters as $newsletter_slug => $newsletter_label) {
               ?>
            <label class="toggle-container">
               <input type="checkbox" id="assunto-<?php esc_attr_e($newsletter_slug); ?>" class="toggle-input" x-bind:disabled="isLoading" x-bind:checked="preferences.notifications.includes('<?php esc_attr_e($newsletter_slug); ?>')" x-on:change="toggleNotification('<?php esc_attr_e($newsletter_slug); ?>')">
               <div class="toggle-checkbox">
                  <?php render_svg('check', 'w-4 h-4 text-white'); ?>
               </div>
               <span class="toggle-label"><?php esc_html_e($newsletter_label); ?></span>
            </label>
               <?php
            }
            ?>
         </fieldset>

         <fieldset class="flex gap-4 w-full mt-6">
         
            <legend class="mb-6">
               <?php esc_html_e('Remover conta', 'bx-bastidor'); ?>
               <br>
               <span class="text-neutral-600">
                  <?php esc_html_e('Ao fazer isso você perderá o acesso ao Meu bastidor, newsletters, conteúdo exclusivo e personalizado', 'bx-bastidor'); ?>
               </span>
            </legend>
            
            <button
               type="button"
               id="account-remove-link"
               class="p-4 rounded-md border-[1px] border-primary-500 text-primary-500"
               x-on:click.prevent="openRemovalModal()"
            >
               <?php esc_html_e('Remover', 'bx-bastidor'); ?>
            </button>
               
         </fieldset>

         <fieldset class="mt-6">
         
            <button
               type="submit"
               class="p-4 rounded-md bg-primary-500 text-white"
               x-bind:disabled="!hasChanges"
               x-bind:class="!hasChanges ? 'opacity-50' : ''"
            >
               <span x-text="isLoading ? '<?php esc_html_e('Salvando...', 'bx-bastidor'); ?>' : '<?php esc_html_e('Salvar alterações', 'bx-bastidor'); ?>'"></span>
            </button>
            
            <button
               type="button"
               x-on:click="resetForm()"
               class="p-4 text-primary-500"
            >
               <?php esc_html_e('Cancelar', 'bx-bastidor'); ?>
            </button>

         </fieldset>
      
      </form>

   </article>

   <div
      class="fixed left-0 top-0 flex w-[100vw] h-[100vh] items-center justify-center bg-black/50 z-50"
      x-transition:enter="transition ease-out duration-300"
      x-transition:enter-start="opacity-0"
      x-transition:enter-end="opacity-100"
      x-transition:leave="transition ease-in duration-200"
      x-transition:leave-start="opacity-100"
      x-transition:leave-end="opacity-0"
      x-show="showRemovalModal"
      x-cloak
   >
      <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-lg">
         <figure class="flex items-center justify-center mb-8">
            <?php render_svg('sad', 'w-8 h-8 text-alert-500'); ?>
         </figure>
         <h1 class="text-center font-bold">
            <?php esc_html_e('Tem certeza de que deseja excluir sua conta?', 'bx-bastidor'); ?>
         </h1>
         <p class="text-center text-sm text-neutral-600 my-8">
            <?php esc_html_e('Essa ação é permanente e não pode ser desfeita. Todos os seus dados serão apagados e não será possível recuperá-los.', 'bx-bastidor'); ?>
         </p>
         <div class="flex justify-center gap-4">
            <button
               class="p-4 rounded-md border-[1px] border-alert-500 text-alert-500"
               x-on:click="deleteAccount()"
               x-bind:disabled="isLoading"
            >
               <span x-text="isLoading ? '<?php esc_html_e('Removendo...', 'bx-bastidor'); ?>' : '<?php esc_html_e('Excluir conta', 'bx-bastidor'); ?>'"></span>
            </button>
            <button
               class="p-4 rounded-md bg-primary-500 text-white"
               x-on:click="closeRemovalModal()"
            >
               <?php esc_html_e('Cancelar', 'bx-bastidor'); ?>
            </button>
         </div>
      </div>
   </div>

</main>
<?php

get_component('footer');
