<?php

if (!defined('ABSPATH')) {
   exit;
}

$confirmation = $_GET['confirmation'] ?? '';

$confirmation_data = [];

if (!empty($confirmation)) {
   $confirmation_data = get_transient('bx_obastidor_confirmation_' . $confirmation);

   if ($confirmation_data) {
      //delete_transient('bx_obastidor_confirmation_' . $confirmation);
   }
}

$action  = $confirmation_data['action'] ?? '';
$title   = $confirmation_data['title'] ?? __('Confirmação', 'bx-obastidor');
$message = $confirmation_data['message'] ?? __('Ação realizada com sucesso.', 'bx-obastidor');

$message_class = '';

if ($action === 'success') {
   $message_class = 'text-green-600';
} else if ($action === 'error') {
   $message_class = 'text-red-600';
}

get_component('header');

?>

<main class="container">

   <article class="py-8 lg:py-16">
      <header>
         <h1 class="text-[2rem] lg:text-[2.6rem] font-primary font-bold mb-4">
            <?php echo esc_html($title); ?>
         </h1>
      </header>

      <div class="flex flex-col gap-4 md:w-2/3">

         <p class="text-lg <?php echo esc_attr($message_class); ?>">
            <?php echo esc_html($message); ?>
         </p>

         <p>
            <a href="<?php echo home_url(); ?>"><?php esc_html_e('Voltar ao início', 'bx-bastidor'); ?></a>
         </p>

      </div>

   </article>

</main>

<?php

get_component('footer');
