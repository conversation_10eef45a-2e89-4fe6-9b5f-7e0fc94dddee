<?php

if (!defined('ABSPATH')) {
   exit;
}

get_component('header');

$redirect_to = home_url('meu-bastidor');
$login_option = $_GET['option'] ?? 'login';

?>

<main class="container" x-data="meuBastidorLoginData">

   <article class="flex items-center justify-center py-8 lg:py-16 text-neutral-600">
   
      <div class="flex flex-col w-full max-w-[400px] bg-white rounded-lg py-8 px-4">

      <?php

         if ($login_option === 'conclusion') {
            get_page_component('meu-bastidor/endpoints', 'conclusion');
         } else {
            get_page_component('meu-bastidor/endpoints', 'form');
         }
      ?>

      </div>

   </article>

</main>
