<?php

if (!defined('ABSPATH')) {
   exit;
}

?>
<form class="flex flex-col gap-4 my-5 " method="post">
   <header class="flex flex-col gap-0 mb-4">
      <h2 class="text-2xl text-primary-500 font-bold">
         <?php esc_html_e('Bem-vindo de volta!', 'bx-bastidor'); ?>
      </h2>
      <p>
         <?php esc_html_e('Acesse seu ambiente exclusivo e fique por dentro.', 'bx-bastidor'); ?>
      </p>
   </header>
   <fieldset class="flex flex-col gap-4">
      <label class="flex flex-col">
         <span x-cloak x-show="!formFields.magic_link_login.valid" class="text-xs text-alert-500 bg-white p-1 ml-2 mb-[-0.75em] z-10 w-fit">
            <?php esc_html_e('E-mail', 'bx-bastidor');?>
         </span>
         <input
            name="magic_link_login" type="email" placeholder="<?php
               esc_html_e('Digite o seu e-mail para receber o link de acesso rápido', 'bx-bastidor');
            ?>"
            class="text-xs py-4 px-2 border border-neutral-300 rounded-md"
            x-on:input="checkFormValidity()"
            x-model="formFields.magic_link_login.value"
            :class="{'border-alert-500': !formFields.magic_link_login.valid}"
            :required="formFields.user_login.value === ''"
         />
         <span x-cloak x-show="!formFields.magic_link_login.valid" class="text-xs text-alert-500 p-1 ml-2">
            <?php esc_html_e('Digite um e-mail válido', 'bx-bastidor');?>
         </span>
      </label>
   </fieldset>
   <fieldset class="flex flex-col gap-4 border-t border-neutral-300">
      <legend class="text-center px-4 mb-4">
         <?php esc_html_e('ou', 'bx-bastidor'); ?>
      </legend>
      <label class="flex flex-col">
         <span x-cloak x-show="!formFields.user_login.valid" class="text-xs text-alert-500 bg-white p-1 ml-2 mb-[-0.75em] z-10 w-fit">
            <?php esc_html_e('E-mail', 'bx-bastidor');?>
         </span>
         <input
            name="user_login" type="email" placeholder="<?php
               esc_html_e('E-mail', 'bx-bastidor');
            ?>"
            class="text-xs py-4 px-2 border border-neutral-300 rounded-md"
            x-on:input="checkFormValidity()"
            x-model="formFields.user_login.value"
            :class="{'border-alert-500': !formFields.user_login.valid}"
            :required="formFields.magic_link_login.value === ''"
         />
         <span x-cloak x-show="!formFields.user_login.valid" class="text-xs text-alert-500 p-1 ml-2">
            <?php esc_html_e('Digite um e-mail válido', 'bx-bastidor');?>
         </span>
      </label>

      <label class="flex flex-col">
         <span x-cloak x-show="!formFields.user_password.valid" class="text-xs text-alert-500 bg-white p-1 ml-2 mb-[-0.75em] z-10 w-fit">
            <?php esc_html_e('Senha', 'bx-bastidor');?>
         </span>
            <input
               name="user_password" type="password" placeholder="<?php
                  esc_html_e('Senha', 'bx-bastidor');
               ?>"
               class="text-xs py-4 px-2 border border-neutral-300 rounded-md"
               x-on:input="checkFormValidity()"
               x-model="formFields.user_password.value"
               :class="{'border-alert-500': !formFields.user_password.valid}"
               :required="formFields.magic_link_login.value === '' && formFields.user_login.valid"
            />
         <span x-cloak x-show="!formFields.user_password.valid" class="text-xs text-alert-500 p-1 ml-2">
            <?php esc_html_e('Digite uma senha válida', 'bx-bastidor');?>
         </span>
      </label>

   </fieldset>
   
   <input name="bx_obastidor_login_option" type="hidden" value="login" />
   <input name="_nonce" type="hidden" value="<?php echo wp_create_nonce('bx_obastidor_login'); ?>" />

   <button type="submit" class="text-white bg-primary-500 p-4 my-2 rounded-md">
      <?php esc_html_e('Acessar o Meu Bastidor', 'bx-bastidor'); ?>
   </button>

</form>
