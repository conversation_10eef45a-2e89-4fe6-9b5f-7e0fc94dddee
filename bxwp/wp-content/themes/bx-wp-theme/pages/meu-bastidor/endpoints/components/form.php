<?php

if (!defined('ABSPATH')) {
   exit;
}

$login_option = $_GET['option'] ?? 'login';

?>



<ul class="flex justify-around gap-4 mb-8">
   <li class="flex justify-center w-full text-xl<?php echo $login_option === 'login' ? ' font-bold text-primary-500 border-b-2 border-primary-500' : ''; ?> pb-2">
      <a href="<?php echo home_url('meu-bastidor/login?option=login');?>" class="hover:no-underline">
         <?php esc_html_e('Acessar conta', 'bx-bastidor'); ?>
      </a>
   </li>
   <li class="flex justify-center w-full text-xl<?php echo $login_option === 'register' ? ' font-bold text-primary-500 border-b-2 border-primary-500' : ''; ?> pb-2">
      <a href="<?php echo home_url('meu-bastidor/login?option=register');?>" class="hover:no-underline">
         <?php esc_html_e('Cadastrar', 'bx-bastidor'); ?>
      </a>
   </li>
</ul>

<div class="flex flex-col gap-4">
   <?php
      if ($login_option === 'login') {
         get_page_component('meu-bastidor/endpoints', 'login-form');
      } else {
         get_page_component('meu-bastidor/endpoints', 'register-form');
      }
      ?>
</div>
