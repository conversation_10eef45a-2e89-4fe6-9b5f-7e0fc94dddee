<?php

if (!defined('ABSPATH')) {
   exit;
}

$error_message = $_GET['bx_obastidor_magic_link_error'] ?? false;

?>
<form class="flex flex-col gap-4 my-5" method="post">
   <input name="email" type="email" required placeholder="Digite seu e-mail" class="border-b border-neutral-300 bg-transparent focus:outline-none focus:border-neutral-500 py-2 px-0 text-[1.2rem] placeholder-neutral-500 font-serif" />
   <input type="hidden" name="action" value="bx_obastidor_send_magic_link" />
   <input type="hidden" name="nonce" value="<?php echo wp_create_nonce('bx_obastidor_send_magic_link'); ?>" />
   <button type="submit" class="btn-primary"><?php esc_html_e('Enviar', 'bx-bastidor'); ?></button>
   <?php

   if ($error_message) {

   ?>
      <p class="text-red-600">
         <?php esc_html_e($error_message); ?>
      </p>
   <?php

   }

   ?>
</form>
