<?php

if (!defined('ABSPATH')) {
   exit;
}

?>
<form class="flex flex-col gap-4 my-5 " method="post">

    <header class="flex flex-col gap-0 mb-4">
        <h2 class="text-2xl text-primary-500 font-bold">
            <?php esc_html_e('Faça parte do Meu Bastidor!', 'bx-bastidor'); ?>
        </h2>
        <p>
            <?php esc_html_e('Crie sua conta e tenha acesso aos bastidores das notícias.', 'bx-bastidor'); ?>
        </p>
    </header>

    <fieldset class="flex flex-col gap-4">
        <label class="flex flex-col">
            <span x-cloak x-show="!formFields.user_name.valid" class="text-xs text-alert-500 bg-white p-1 ml-2 mb-[-0.75em] z-10 w-fit">
                <?php esc_html_e('Nome', 'bx-bastidor');?>
            </span>
            <input
                name="user_name" type="text"
                placeholder="<?php esc_html_e('Nome', 'bx-bastidor');?>"
                class="text-xs py-4 px-2 border border-neutral-300 rounded-md"
                x-on:input="setFieldValidity($el)"
                x-model="formFields.user_name.value"
                :required="formFields.user_name.edited"
                :class="{'border-alert-500': !formFields.user_name.valid}"
            />
            <span x-cloak x-show="!formFields.user_name.valid" class="text-xs text-alert-500 p-1 ml-2">
                <?php esc_html_e('Este campo é obrigatório', 'bx-bastidor');?>
            </span>
        </label>
        <label class="flex flex-col">
            <span x-cloak x-show="!formFields.user_lastname.valid" class="text-xs text-alert-500 bg-white p-1 ml-2 mb-[-0.75em] z-10 w-fit">
                <?php esc_html_e('Sobrenome', 'bx-bastidor');?>
            </span>
            <input
                name="user_lastname" type="text"
                placeholder="<?php esc_html_e('Sobrenome', 'bx-bastidor');?>"
                class="text-xs py-4 px-2 border border-neutral-300 rounded-md"
                x-on:input="setFieldValidity($el)"
                x-model="formFields.user_lastname.value"
                :required="formFields.user_lastname.edited"
                :class="{'border-alert-500': !formFields.user_lastname.valid}"
            />
            <span x-cloak x-show="!formFields.user_lastname.valid" class="text-xs text-alert-500 p-1 ml-2">
                <?php esc_html_e('Este campo é obrigatório', 'bx-bastidor');?>
            </span>
        </label>
        <label class="flex flex-col">
            <span x-cloak x-show="!formFields.user_email.valid" class="text-xs text-alert-500 bg-white p-1 ml-2 mb-[-0.75em] z-10 w-fit">
                <?php esc_html_e('E-mail', 'bx-bastidor');?>
            </span>
            <input
                name="user_email" type="email"
                placeholder="<?php esc_html_e('E-mail', 'bx-bastidor');?>"
                class="text-xs py-4 px-2 border border-neutral-300 rounded-md"
                x-on:input="setFieldValidity($el)"
                x-model="formFields.user_email.value"
                :required="formFields.user_email.edited"
                :class="{'border-alert-500': !formFields.user_email.valid}"
            />
            <span x-cloak x-show="!formFields.user_email.valid" class="text-xs text-alert-500 p-1 ml-2">
                <?php esc_html_e('Digite um e-mail válido', 'bx-bastidor');?>
            </span>
        </label>
        <label class="flex flex-col">
            <span x-cloak x-show="!formFields.user_password.valid" class="text-xs text-alert-500 bg-white p-1 ml-2 mb-[-0.75em] z-10 w-fit">
                <?php esc_html_e('Senha', 'bx-bastidor');?>
            </span>
            <input
                name="user_password" type="password"
                placeholder="<?php esc_html_e('Senha', 'bx-bastidor');?>"
                class="text-xs py-4 px-2 border border-neutral-300 rounded-md"
                pattern="^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9]).*$"
                minlength="7"
                x-on:input="setFieldValidity($el)"
                :required="formFields.user_password.edited"
                :class="{'border-alert-500': !formFields.user_password.valid}"
            />
            <span x-cloak x-show="!formFields.user_password.valid" class="text-xs text-alert-500 p-1 ml-2">
                <?php esc_html_e('Digite uma senha válida com pelo menos 8 caracteres, uma letra maiúscula, uma letra minúscula e um número.', 'bx-bastidor');?>
            </span>
        </label>
    </fieldset>

   <input name="bx_obastidor_login_option" type="hidden" value="register" />
   <input name="_nonce" type="hidden" value="<?php echo wp_create_nonce('bx_obastidor_login'); ?>" />

   <button type="submit" class="text-white bg-primary-500 p-4 my-2 rounded-md">
      <?php esc_html_e('Criar o Meu Bastidor', 'bx-bastidor');?>
   </button>

</form>
