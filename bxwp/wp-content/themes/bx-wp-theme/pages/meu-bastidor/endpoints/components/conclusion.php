<?php

if (!defined('ABSPATH')) {
   exit;
}


$confirmation = $_GET['confirmation'] ?? '';

$confirmation_data = [];

if (!empty($confirmation)) {
   $confirmation_data = get_transient('bx_obastidor_confirmation_' . $confirmation);

   if ($confirmation_data) {
      delete_transient('bx_obastidor_confirmation_' . $confirmation);
   }
   else {
    $confirmation_data = [];
   }
}

$action  = $confirmation_data['action'] ?? '';
$title   = $confirmation_data['title'] ?? __('Erro!', 'bx-obastidor');
$message = $confirmation_data['message'] ?? '';
$icon    = $confirmation_data['icon'] ?? 'inbox';
$ctas    = $confirmation_data['cta'] ?? [];

if (! is_array($ctas)) {
    $ctas = [];
}
elseif (array_key_exists('url', $ctas) || array_key_exists('caption', $ctas)) {
    $ctas = [$ctas];
}

if ($action === 'success') {
   $message_class = 'text-green-600';
} else if ($action === 'error') {
   $message_class = 'text-red-600';
} else {
    $message_class = '';
}

?>

<header>

    <?php if ($icon) {
        ?>
        <figure class="flex justify-center items-center mb-4">
            <?php echo render_svg($icon); ?>
        </figure>
        <?php
    }?>
    
    <h2 class="text-2xl text-primary-500 font-bold">
        <?php echo esc_html($title); ?>
    </h2>

</header>

<?php if ($message) { ?>
    
<p>
    <?php echo esc_html($message); ?>
</p>

<?php } ?>

<?php foreach ($ctas as $cta) { ?>

<a
    class="text-white text-center bg-primary-500 hover:bg-highlight-500 hover:no-underline p-4 mt-6 rounded-md"
    href="<?php echo esc_url($cta['url']); ?>"
>
      <?php esc_html_e($cta['caption'], 'bx-bastidor'); ?>
</a>

<?php } ?>
