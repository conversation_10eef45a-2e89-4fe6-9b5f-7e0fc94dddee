<?php

if (!defined('ABSPATH')) {
   exit;
}

$feed_sections = (array) get_user_meta(get_current_user_id(), 'bx_obastidor_customized_feed_sections', true);

get_component('header');

?>

<main class="container flex flex-col">

   <section class="flex flex-col md:flex-row gap-8 py-8">
      <div class="md:w-2/3 md:border-r md:border-neutral-300 md:pr-8">
         <h1 class="text-[2rem] lg:text-[2.6rem] font-primary font-bold mb-4">
            <?php esc_html_e('Notícias do seu Bastidor', 'bx-obastidor'); ?>
         </h1>
         <?php

         get_component('news-list', [
            'query_args' => [
               'tax_query' => [
                  [
                     'taxonomy' => 'category',
                     'field'    => 'term_id',
                     'terms'    => $feed_sections,
                  ],
               ],
            ]
         ]);

         ?>
      </div>

      <aside class="md:w-1/3">
      </aside>
   </section>

</main>
<?php

get_component('footer');
