<?php

if (!defined('ABSPATH')) {
   exit;
}

use BX_Obastidor\BX_News\BX_News;
use BX_Obastidor\BX_News\Utils;

$Single = new BX_News();

$Category = $Single->get_category();

$featured_posts = [
   ...Utils::get_featured_posts('principal'),
   ...Utils::get_featured_posts('secundario'),
];

get_component('header');

?>

<main class="container flex flex-col">

   <?php

   get_component('ad-banner', ['slot' => 'news-top', 'container_classes' => 'my-2']);

   ?>

   <div class="flex flex-col md:flex-row gap-6 border-b border-neutral-300 py-8">
      <div class="md:w-2/3 md:border-r md:border-neutral-300 md:pr-6">
         <article>
            <header>
               <?php

               if (!empty($Category)) {

               ?>
                  <div class="text-sm mb-4 uppercase">
                     <?php get_component('news-label', ['post_object' => $Single]); ?>
                  </div>
               <?php

               }

               ?>
               <h1 class="text-[2.6rem] font-primary font-bold mb-4">
                  <?php echo $Single->get_title(); ?>
               </h1>

               <div class="flex flex-col gap-2">
                  <div class="flex -space-x-2" aria-hidden="true">
                     <?php

                     $authors = $Single->get_authors();
                     $author_labels = [];

                     foreach ($authors as $index => $Author) {
                        $author_labels[] = sprintf(
                           '<li class="%s"><a href="%s" class="inline-block font-secondary text-sm font-bold text-primary-400 !no-underline">%s</a></li>',
                           $index > 0 ? 'before:content-[\'\'] before:inline-block before:w-1 before:h-1 before:rounded-full before:bg-primary-400 before:mx-2 before:align-middle' : '',
                           $Author->get_link(),
                           $Author->get_name()
                        );
                     ?>
                        <picture class="rounded-full overflow-hidden border-2 border-white" title="<?php echo esc_attr($Author->get_name()); ?>">
                           <?php echo $Author->get_avatar(40); ?>
                        </picture>
                     <?php

                     }

                     ?>
                  </div>
                  <ul class="flex items-center text-sm">
                     <?php echo implode('', $author_labels); ?>
                  </ul>
               </div>

               <time class="block font-mono text-primary-400 text-sm mb-8" datetime="<?php echo $Single->get_date('c'); ?>">
                  <?php printf(esc_html__('Publicada em %s', 'bx-obastidor'), $Single->get_date('d/m/Y à\s H:i')); ?>
               </time>
               <?php

               if ($Single->has_thumbnail()) {

               ?>
                  <figure class="border border-double border-neutral-300 p-2 mb-4">
                     <?php

                     echo $Single->get_thumbnail('large', [
                        'alt'   => $Single->get_title(),
                        'class' => 'w-full h-full aspect-video object-cover'
                     ]);

                     $caption = $Single->get_thumbnail_caption();

                     if (!empty($caption)) {

                     ?>
                        <figcaption class="font-mono text-primary-400 text-sm pt-2 px-2">
                           <?php echo esc_html($caption); ?>
                        </figcaption>
                     <?php

                     }

                     ?>
                  </figure>
               <?php

               }

               ?>
            </header>

            <div class="post-content">
               <?php

               echo $Single->get_content();

               ?>
            </div>

            <div class="flex flex-col lg:flex-row items-start lg:items-center gap-8 mt-10">
               <?php

               $tags = $Single->get_terms('post_tag');

               if (!empty($tags)) {

               ?>
                  <ul class="flex flex-wrap items-center gap-2">
                     <?php

                     foreach ($tags as $tag) {

                     ?>
                        <li>
                           <a href="<?php echo esc_url(get_term_link($tag)); ?>" class="inline-block bg-highlight-100 text-highlight-800 border border-highlight-800 px-2 py-1 rounded-full font-mono text-sm leading-none tracking-wider uppercase !no-underline">
                              <?php echo $tag->name; ?>
                           </a>
                        </li>
                     <?php

                     }

                     ?>
                  </ul>
               <?php

               }

               ?>
               <div class="lg:ml-auto">
                  <p class="font-mono text-sm tracking-wider font-medium mb-3 uppercase"><?php esc_html_e('Compartilhar', 'bx-obastidor'); ?></p>
                  <?php

                  echo $Single->get_share_buttons('', 'flex items-center gap-2', 'flex items-center justify-center bg-neutral-100 p-2 rounded-md  transition-all');

                  ?>
               </div>
            </div>
         </article>

         <?php
         get_component(
            'ad-banner',
            [
               'slot' => 'news-bottom',
               'container_classes' => 'my-2'
            ]
         );
         ?>

         <section class="mt-12">
            <h2 class="font-primary text-[1.7rem] font-bold underline mb-6"><?php esc_html_e('Últimas Notícias', 'bx-obastidor'); ?></h2>
            <?php

            get_component('news-list', ['query_args' => [
               'post__not_in' => [
                  $Single->get_the_ID(),
                  ...array_column($featured_posts, 'ID'),
               ]
            ]]);

            ?>
         </section>
      </div>

      <aside class="md:w-1/3">
         <ul>
            <?php

            get_component('news-featured-secondary', ['posts' => $featured_posts]);

            ?>
         </ul>
         <?php

         get_component('ad-banner', ['slot' => 'news-sidebar', 'container_classes' => 'my-2']);

         ?>
      </aside>
   </div>

</main>

<?php

get_component('footer');
