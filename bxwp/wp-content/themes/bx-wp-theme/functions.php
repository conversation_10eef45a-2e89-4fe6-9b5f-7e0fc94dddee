<?php

if (!defined('ABSPATH')) {
   echo 'Inicie WordPress';

   exit;
}

require implode(DIRECTORY_SEPARATOR, [__DIR__, 'includes', 'global', 'required-plugins.php']);

if (!class_exists('bx_sntls_Utils')) {
   if (is_admin()) {
      error_log('Instale BX Essentials');
   } else {
      echo 'Instale BX Essentials';
   }

   return;
}

require implode(DIRECTORY_SEPARATOR, [__DIR__, 'includes', 'global', '_index.php']);
require implode(DIRECTORY_SEPARATOR, [__DIR__, 'includes', 'hooks', '_index.php']);

bx_sntls_Utils::load_class('AutoLoader');

$loader = new bx_sntls_AutoLoader();
$loader->add_namespace('BX_Obastidor', get_theme_file_path('includes/entities'));
$loader->add_namespace('BX_Obastidor', get_theme_file_path('includes/generic'));

new BX_Obastidor\News\Register();
new BX_Obastidor\News\Register_Mobile_APP();
new BX_Obastidor\CommonAds\Register();
new BX_Obastidor\SponsoredContent\Register();
new BX_Obastidor\AdBanner\Register();
new BX_Obastidor\Newsletter\Register();
new BX_Obastidor\Author\Register();
