import {registerPlugin} from '@wordpress/plugins'
import {PluginDocumentSettingPanel} from '@wordpress/edit-post'
import {PanelBody, ToggleControl} from '@wordpress/components'
import {__} from '@wordpress/i18n'
import {useSelect} from '@wordpress/data'
import {useEntityProp} from '@wordpress/core-data'

const postTypes = ['post'];

const ExclusiveContentPanel = () => {
   const postType = useSelect(
      select => select('core/editor').getEditedPostAttribute('type')
   )

   if (!postType || !postTypes.includes(postType)) {
      return null
   }

   const [meta, setMeta] = useEntityProp('postType', postType, 'meta')
   const isExclusive = meta?.bx_obastidor_exclusive_content || false;

   const updateExclusiveContent = (value) => {
      setMeta({...meta, bx_obastidor_exclusive_content: value})
   }

   console.log({meta, postType, isExclusive})

    return (
       <PluginDocumentSettingPanel name="exclusive-content-panel" title={__('Exclusivo', 'bx-obastidor')}>
          <PanelBody>
             <ToggleControl
                label={__('Conteúdo exclusivo', 'bx-obastidor')}
                checked={isExclusive}
                onChange={updateExclusiveContent}
             />
          </PanelBody>
       </PluginDocumentSettingPanel>
    )
 }

 registerPlugin('exclusive-content-panel', {
    render: ExclusiveContentPanel,
    icon: null,
 })
