import {registerPlugin} from '@wordpress/plugins'
import {PluginDocumentSettingPanel} from '@wordpress/edit-post'
import {PanelBody, TextControl} from '@wordpress/components'
import {__} from '@wordpress/i18n'
import {useSelect, useDispatch} from '@wordpress/data'
import {isURL} from '@wordpress/url'

const postTypes = ['ad_banner', 'sponsored_content'];

const AdBannerLinkPanel = () => {
    const post = useSelect((select) => select('core/editor').getCurrentPost(), [])
    const postType = post?.type
    const currentPostBlocks = useSelect((select) => select('core/editor').getBlocks(), [])
    const {updateBlock} = useDispatch('core/block-editor')
 
    if (!post || !postTypes.includes(postType)) {
       return null
    }
 
    const getImageBlock = () => {
       const imageBlock = currentPostBlocks.find((b) => b.name === 'core/image')
       return imageBlock
    }
 
    const getLink = () => {
       const imageBlock = getImageBlock()
       return imageBlock?.attributes?.href || ''
    }
 
    const updateLink = (value) => {
       const imageBlock = getImageBlock()
       if (imageBlock) {
          updateBlock(imageBlock.clientId, {
             ...imageBlock,
             attributes: {
                ...imageBlock.attributes,
                href: value,
             },
          })
       }
    }
 
    return (
       <PluginDocumentSettingPanel name="ad-banner-link-panel" title={__('Link do Banner', 'bx-obastidor')}>
          <PanelBody>
             <TextControl
                type="url"
                label={__('Link do Banner', 'bx-obastidor')}
                value={getLink()}
                onChange={updateLink}
                help={getLink() && !isURL(getLink()) ? 'URL inválida' : ''}
                className={getLink() && !isURL(getLink()) ? 'has-error' : ''}
             />
          </PanelBody>
       </PluginDocumentSettingPanel>
    )
 }

 registerPlugin('ad-banner-link-panel', {
    render: AdBannerLinkPanel,
    icon: null,
 })
  