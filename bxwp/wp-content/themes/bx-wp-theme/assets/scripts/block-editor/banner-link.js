import {registerPlugin} from '@wordpress/plugins'
import {PluginDocumentSettingPanel} from '@wordpress/edit-post'
import {PanelBody, TextControl} from '@wordpress/components'
import {__} from '@wordpress/i18n'
import {useSelect, useDispatch} from '@wordpress/data'
import {isURL} from '@wordpress/url'
import {useEntityProp} from '@wordpress/core-data'

const postTypes = ['ad_banner', 'sponsored_content']

const AdBannerLinkPanel = () => {
   const post = useSelect((select) => select('core/editor').getCurrentPost(), [])
   const postType = post?.type
   const currentPostBlocks = useSelect((select) => select('core/editor').getBlocks(), [])
   const {updateBlock} = useDispatch('core/block-editor')

   if (!post || !postTypes.includes(postType)) {
      return null
   }
   const [postMeta, setPostMeta] = useEntityProp('postType', postType, 'meta')

   const getImageBlock = () => {
      const imageBlock = currentPostBlocks.find((b) => b.name === 'core/image')
      const imageBlockClassName = imageBlock?.attributes?.className || ''

      if (imageBlockClassName.includes('ad-banner-image')) {
         return imageBlock
      }

      return null
   }

   const updateLink = (value) => {
      setPostMeta({...postMeta, bx_obastidor_external_link: value})

      const imageBlock = getImageBlock()

      if (imageBlock) {
         updateBlock(imageBlock.clientId, {
            ...imageBlock,
            attributes: {
               ...imageBlock.attributes,
               href: value,
            },
         })
      }
   }

   const url = postMeta?.bx_obastidor_external_link || ''

   const getPanelTitle = () => {
      return postType === 'sponsored_content'
         ? __('Link do Patrocinador', 'bx-obastidor')
         : __('Link do Banner', 'bx-obastidor')
   }

   return (
      <PluginDocumentSettingPanel name="ad-banner-link-panel" title={getPanelTitle()}>
         <PanelBody>
            <TextControl
               type="url"
               label={getPanelTitle()}
               value={url}
               onChange={updateLink}
               help={url && !isURL(url) ? 'URL inválida' : ''}
               className={url && !isURL(url) ? 'has-error' : ''}
            />
         </PanelBody>
      </PluginDocumentSettingPanel>
   )
}

registerPlugin('ad-banner-link-panel', {
   render: AdBannerLinkPanel,
   icon: null,
})
