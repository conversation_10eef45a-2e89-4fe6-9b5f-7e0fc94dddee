import {registerPlugin} from '@wordpress/plugins'
import {PluginDocumentSettingPanel} from '@wordpress/edit-post'
import {Modal, PanelBody, TextControl, DateTimePicker, SelectControl, CheckboxControl, Button, ButtonGroup} from '@wordpress/components'
import {__} from '@wordpress/i18n'
import {useSelect, useDispatch} from '@wordpress/data'
import {useState, useEffect} from '@wordpress/element'
import {useEntityProp} from '@wordpress/core-data'

const postTypes = ['ad_banner', 'sponsored_content'];

const AdBannerValidityPanel = () => {
   const post = useSelect((select) => select('core/editor').getCurrentPost(), [])
   const postType = post?.type
   const postId = post?.id

   if (!postTypes.includes(postType)) {
      return null
   }

   const [postMeta, setPostMeta] = useEntityProp('postType', postType, 'meta', postId)
   const [postPublishedDate, setPostPublishedDate] = useEntityProp('postType', postType, 'date', postId)

   const [isStartDateFocused, setIsStartDateFocused] = useState(false)
   const [isEndDateFocused, setIsEndDateFocused] = useState(false)

   // Estados temporários para os modais
   const [tempStartDate, setTempStartDate] = useState('')
   const [tempEndDate, setTempEndDate] = useState('')

   // Inicializar estados temporários quando abrir os modais
   useEffect(() => {
      if (isStartDateFocused) {
         setTempStartDate(postPublishedDate || '')
      }
   }, [isStartDateFocused, postPublishedDate])

   useEffect(() => {
      if (isEndDateFocused) {
         setTempEndDate(postMeta?.bx_obastidor_unpublish_date || '')
      }
   }, [isEndDateFocused, postMeta?.bx_obastidor_unpublish_date])

   const handleStartDateConfirm = () => {
      setPostPublishedDate(tempStartDate)
      setIsStartDateFocused(false)
   }

   const handleStartDateCancel = () => {
      setTempStartDate(postPublishedDate || '')
      setIsStartDateFocused(false)
   }

   const handleEndDateConfirm = () => {
      const endDate = new Date(tempEndDate)
      if (isInvalidEndDate(endDate)) {
         return;
      }
      setPostMeta({...postMeta, bx_obastidor_unpublish_date: tempEndDate})
      setIsEndDateFocused(false)
   }

   const handleEndDateCancel = () => {
      setTempEndDate(postMeta?.bx_obastidor_unpublish_date || '')
      setIsEndDateFocused(false)
   }

   const formatDate = (dateString) => {
      if (!dateString) return ''
      return new Date(dateString).toLocaleString('pt-BR')
   }

   const isInvalidEndDate = (endDate) => {
      const startDate = new Date(postPublishedDate)
      const startDateWithoutTime = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate())
      const endDateWithoutTime = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate())
      return endDateWithoutTime < startDateWithoutTime
   }

   return (
      <PluginDocumentSettingPanel
         name="ad-banner-validity-panel"
         title={__('Vigência do Anúncio', 'bx-obastidor')}
         className="ad-banner-validity-panel">
         <PanelBody>
            {isStartDateFocused && (
               <Modal
                  title={__('Data de Início', 'bx-obastidor')}
                  onRequestClose={handleStartDateCancel}
                  className="date-range-modal">
                  <div style={{padding: '20px'}}>
                     <DateTimePicker
                        currentDate={tempStartDate}
                        onChange={setTempStartDate}
                        label={__('Data de Início', 'bx-obastidor')}
                     />
                     <div style={{marginTop: '20px', textAlign: 'right'}}>
                        <ButtonGroup>
                           <Button
                              isSecondary
                              onClick={handleStartDateCancel}>
                              {__('Cancelar', 'bx-obastidor')}
                           </Button>
                           <Button
                              isPrimary
                              onClick={handleStartDateConfirm}>
                              {__('Confirmar', 'bx-obastidor')}
                           </Button>
                        </ButtonGroup>
                     </div>
                  </div>
               </Modal>
            )}
            {isEndDateFocused && (
               <Modal
                  title={__('Data de Término', 'bx-obastidor')}
                  onRequestClose={handleEndDateCancel}
                  className="date-range-modal">
                  <div style={{padding: '20px'}}>
                     <DateTimePicker
                        currentDate={tempEndDate}
                        onChange={setTempEndDate}
                        label={__('Data de Término', 'bx-obastidor')}
                        isInvalidDate={isInvalidEndDate}
                     />
                     <div style={{marginTop: '20px', textAlign: 'right'}}>
                        <ButtonGroup>
                           <Button
                              isSecondary
                              onClick={handleEndDateCancel}>
                              {__('Cancelar', 'bx-obastidor')}
                           </Button>
                           <Button
                              isPrimary
                              onClick={handleEndDateConfirm}>
                              {__('Confirmar', 'bx-obastidor')}
                           </Button>
                        </ButtonGroup>
                     </div>
                  </div>
               </Modal>
            )}
            <TextControl
               label={__('Data de Início', 'bx-obastidor')}
               value={formatDate(postPublishedDate)}
               onFocus={() => setIsStartDateFocused(true)}
               help={__('Deixe em branco para exibir imediatamente.', 'bx-obastidor')}
               readOnly
            />
            <TextControl
               label={__('Data de Término', 'bx-obastidor')}
               value={formatDate(postMeta?.bx_obastidor_unpublish_date)}
               onFocus={() => setIsEndDateFocused(true)}
               help={__('Deixe em branco para exibir indefinidamente.', 'bx-obastidor')}
               readOnly
            />
         </PanelBody>
      </PluginDocumentSettingPanel>
   )
}

registerPlugin('ad-banner-validity-panel', {
   render: AdBannerValidityPanel,
   icon: null,
})
