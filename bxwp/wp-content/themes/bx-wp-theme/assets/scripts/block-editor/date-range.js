import {registerPlugin} from '@wordpress/plugins'
import {PluginDocumentSettingPanel} from '@wordpress/edit-post'
import {Modal, PanelBody, TextControl, DateTimePicker, SelectControl, CheckboxControl} from '@wordpress/components'
import {__} from '@wordpress/i18n'
import {useSelect, useDispatch} from '@wordpress/data'
import {useState, useEffect} from '@wordpress/element'
import {useEntityProp} from '@wordpress/core-data'

const postTypes = ['ad_banner', 'sponsored_content'];

const AdBannerValidityPanel = () => {
   const post = useSelect((select) => select('core/editor').getCurrentPost(), [])
   const postType = post?.type
   const postId = post?.id

   if (!postTypes.includes(postType)) {
      return null
   }

   const [meta, setMeta] = useEntityProp('postType', postType, 'meta', postId)
   const [date, setDate] = useEntityProp('postType', postType, 'date', postId)

   const [isStartDateFocused, setIsStartDateFocused] = useState(false)
   const [isEndDateFocused, setIsEndDateFocused] = useState(false)

   const onChangeStartDate = (value) => {
      setDate(value)
   }

   const onChangeEndDate = (value) => {
      setMeta({...meta, bx_obastidor_unpublish_date: value})
   }

   const formatDate = (dateString) => {
      if (!dateString) return ''
      return new Date(dateString).toLocaleString('pt-BR')
   }

   return (
      <PluginDocumentSettingPanel
         name="ad-banner-validity-panel"
         title={__('Vigência do Anúncio', 'bx-obastidor')}
         className="ad-banner-validity-panel">
         <PanelBody>
            {isStartDateFocused && (
               <Modal title={__('Data de Início', 'bx-obastidor')} onRequestClose={() => setIsStartDateFocused(false)}>
                  <DateTimePicker
                     currentDate={date}
                     onChange={onChangeStartDate}
                     label={__('Data de Início', 'bx-obastidor')}
                  />
               </Modal>
            )}
            {isEndDateFocused && (
               <Modal title={__('Data de Término', 'bx-obastidor')} onRequestClose={() => setIsEndDateFocused(false)}>
                  <DateTimePicker
                     currentDate={meta?.bx_obastidor_unpublish_date || ''}
                     onChange={onChangeEndDate}
                     label={__('Data de Término', 'bx-obastidor')}
                  />
               </Modal>
            )}
            <TextControl
               label={__('Data de Início', 'bx-obastidor')}
               value={formatDate(date)}
               onFocus={() => setIsStartDateFocused(true)}
               help={__('Deixe em branco para exibir imediatamente.', 'bx-obastidor')}
               readOnly
            />
            <TextControl
               label={__('Data de Término', 'bx-obastidor')}
               value={formatDate(meta?.bx_obastidor_unpublish_date)}
               onFocus={() => setIsEndDateFocused(true)}
               help={__('Deixe em branco para exibir indefinidamente.', 'bx-obastidor')}
               readOnly
            />
         </PanelBody>
      </PluginDocumentSettingPanel>
   )
}

registerPlugin('ad-banner-validity-panel', {
   render: AdBannerValidityPanel,
   icon: null,
})
