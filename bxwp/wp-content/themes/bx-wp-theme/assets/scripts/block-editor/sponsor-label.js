import {registerPlugin} from '@wordpress/plugins'
import {PluginDocumentSettingPanel} from '@wordpress/edit-post'
import {PanelBody, TextControl} from '@wordpress/components'
import {__} from '@wordpress/i18n'
import {useSelect} from '@wordpress/data'
import {useEntityProp} from '@wordpress/core-data'

const postTypes = ['sponsored_content'];

const SponsorLabelPanel = () => {
   const postType = useSelect(
      select => select('core/editor').getEditedPostAttribute('type')
   )

   if (!postType || !postTypes.includes(postType)) {
      return null
   }

   const [meta, setMeta] = useEntityProp('postType', postType, 'meta')

   const updateSponsorLabel = (value) => {
      setMeta({...meta, bx_obastidor_sponsor_label: value})
   }

   const sponsorLabel = meta?.bx_obastidor_sponsor_label || ''

    return (
       <PluginDocumentSettingPanel name="sponsor-label-panel" title={__('Patrocinador', 'bx-obastidor')}>
          <PanelBody>
             <TextControl
                label={__('Patrocinador', 'bx-obastidor')}
                value={sponsorLabel}
                onChange={updateSponsorLabel}
             />
          </PanelBody>
       </PluginDocumentSettingPanel>
    )
 }

 registerPlugin('sponsor-label-panel', {
    render: SponsorLabelPanel,
    icon: null,
 })
