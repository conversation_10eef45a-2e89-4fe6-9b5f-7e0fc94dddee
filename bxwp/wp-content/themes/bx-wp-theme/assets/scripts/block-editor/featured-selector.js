import {registerPlugin} from '@wordpress/plugins'
import {useSelect} from '@wordpress/data'
import {__} from '@wordpress/i18n'
import {PanelBody} from '@wordpress/components'
import {PluginDocumentSettingPanel} from '@wordpress/editor'
import {CheckboxControl, SelectControl} from '@wordpress/components'
import {useEntityProp} from '@wordpress/core-data'

const postTypes = {
   sponsored_content: 'multiple',
   post: 'single',
}

const FeaturedSelectorPanel = () => {
   const postId = useSelect((select) => select('core/editor').getCurrentPostId())
   const postType = useSelect((select) => select('core/editor').getCurrentPostType())

   const selectType = postTypes[postType] || null

   if (!postId || !selectType) {
      return null
   }

   const userRoles = bxObastidorEditor?.userRoles || {}
   const isEditor = userRoles.isEditor || false
   const isAdministrator = userRoles.isAdministrator || false

   const canEditExclusiveContent = isEditor || isAdministrator

   if (!canEditExclusiveContent) {
      return null
   }

   const [currentFeaturedNewsOrderIds, setCurrentFeaturedNewsOrderIds] = useEntityProp(
      'postType',
      postType,
      'featured-news-order',
   )

   const featuredNewsOrderTerms = useSelect(
      (select) => select('core').getEntityRecords('taxonomy', 'featured-news-order', {per_page: -1}) || [],
   )

   const unlistedId = featuredNewsOrderTerms?.find((term) => term.slug === 'unlisted')?.id

   const toggleFeaturedNewsOrderId = (id) => {
      const newFeaturedNewsOrderIds = (currentFeaturedNewsOrderIds || []).includes(id)
         ? (currentFeaturedNewsOrderIds || []).filter((i) => i !== id)
         : [...(currentFeaturedNewsOrderIds || []), id]
      setCurrentFeaturedNewsOrderIds(newFeaturedNewsOrderIds)
   }

   const setSingleFeaturedNewsOrderId = (id) => {
      const currentIds = currentFeaturedNewsOrderIds || []
      const hasUnlisted = unlistedId && currentIds.includes(unlistedId)

      let newFeaturedNewsOrderIds = id ? [id] : []

      if (hasUnlisted) {
         newFeaturedNewsOrderIds = [...newFeaturedNewsOrderIds, unlistedId]
      }

      setCurrentFeaturedNewsOrderIds(newFeaturedNewsOrderIds)
   }

   return (
      <PluginDocumentSettingPanel name="featured-selector-panel" title={__('Conteúdo em destaque', 'bx-obastidor')}>
         <PanelBody title={__('Exibir como destaque em', 'bx-obastidor')}>
            {selectType === 'multiple' &&
               featuredNewsOrderTerms
                  .filter((term) => term.slug !== 'unlisted')
                  .map((term) => (
                     <CheckboxControl
                        key={term.id}
                        label={term.name}
                        checked={(currentFeaturedNewsOrderIds || []).includes(term.id)}
                        onChange={() => {
                           toggleFeaturedNewsOrderId(term.id)
                        }}
                     />
                  ))}
            {selectType === 'single' && (
               <SelectControl
                  value={currentFeaturedNewsOrderIds?.find((id) => id !== unlistedId) || ''}
                  onChange={(value) => {
                     setSingleFeaturedNewsOrderId(value)
                  }}
                  options={[
                     {
                        label: __('Escolha uma opção', 'bx-obastidor'),
                        value: '',
                     },
                  ].concat(
                     featuredNewsOrderTerms
                        .filter((term) => term.slug !== 'unlisted')
                        .map((term) => ({
                           label: term.name,
                           value: term.id,
                        })),
                  )}
               />
            )}
         </PanelBody>
      </PluginDocumentSettingPanel>
   )
}

registerPlugin('featured-selector-panel', {
   render: FeaturedSelectorPanel,
   icon: null,
})
