import {registerPlugin} from '@wordpress/plugins'
import {useEntityProp} from '@wordpress/core-data'
import {useSelect} from '@wordpress/data'
import {__} from '@wordpress/i18n'
import {PanelBody} from '@wordpress/components'
import {PluginDocumentSettingPanel} from '@wordpress/edit-post'

const postTypes = ['ad_banner', 'sponsored_content']

const FeaturedSelectorPanel = () => {
    const postId = useSelect(select => select('core/editor').getCurrentPostId())
    const postType = useSelect(select => select('core/editor').getCurrentPostType())

    if (!postId || !postTypes.includes(postType)) {
        return null
    }

    const post = useSelect(select => select('core/editor').getCurrentPost())

    const featuredNewsOrderTerms = useSelect(select => select('core').getEntityRecords('taxonomy', 'featured-news-order', {per_page: -1}))
    
    const currentFeaturedNewsOrderIds = useSelect(select => select('core/editor').getEditedPostAttribute('featured-news-order'))

    const unlistedId = featuredNewsOrderTerms?.find(term => term.slug === 'unlisted')?.id

    console.log({unlistedId, featuredNewsOrderTerms, currentFeaturedNewsOrderIds})

    return (
        <PluginDocumentSettingPanel name="featured-selector-panel" title={__('Conteúdo em destaque', 'bx-obastidor')}>
            <PanelBody
                title={__('Conteúdo em destaque', 'bx-obastidor')}
            >
                {unlistedId && <CheckboxControl
                    label={__('Omitir nas listas de notícias', 'bx-obastidor')}
                    checked={currentFeaturedNewsOrderIds.includes(unlistedId)}
                    onChange={() => {toggleFeaturedNewsOrderId(unlistedId)}}
                />}
            </PanelBody>
        </PluginDocumentSettingPanel>
    )
}

registerPlugin('featured-selector-panel', {
    render: FeaturedSelectorPanel,
    icon: null,
})
  