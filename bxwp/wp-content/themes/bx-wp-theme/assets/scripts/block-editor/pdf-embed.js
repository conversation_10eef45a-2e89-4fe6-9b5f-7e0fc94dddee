const {registerBlockType, registerBlockVariation} = wp.blocks
const {__} = wp.i18n
const {InspectorControls, useBlockProps, MediaUpload, MediaUploadCheck} = wp.blockEditor
const {PanelBody, TextControl, Placeholder} = wp.components
const {Button} = wp.components
const {addFilter} = wp.hooks
const {useState, useEffect} = wp.element

registerBlockVariation('core/embed', {
   name: 'bx-obastidor/embed-pdf-variation',
   title: '',
   icon: 'media-document',
   description: __(
      'Use arquivos PDF de seu computador ou escolha de biblioteca de mídia para incluir no conteúdo.',
      'bx-obastidor',
   ),
   keywords: ['pdf', 'documento', 'arquivo'],
   patterns: ['/.*.pdf/i'],
   attributes: {
      providerNameSlug: 'obastidor-docs',
      attachment_id: {
         type: 'number',
         default: 0,
      },
      caption: {
         type: 'string',
         default: '',
      },
   },
})

addFilter('blocks.registerBlockType', 'bx-obastidor/embed-pdf-variation', (settings, name) => {
   if (name !== 'core/embed') {
      return settings
   }

   const {edit: originalEdit} = settings

   settings.edit = (props) => {
      const [attachmentId, setAttachmentId] = useState(0)
      const blockProps = useBlockProps()
      const {
         attributes: {providerNameSlug, url, caption},
         setAttributes,
      } = props

      if (providerNameSlug !== 'obastidor-docs') {
         return originalEdit(props)
      }

      function onSelectFile(media) {
         wp.media
            .attachment(media.id)
            .fetch()
            .then((att) => {
               setAttachmentId(media.id)
               setAttributes({
                  url: att.url,
                  caption: att.title,
               })
            })
      }

      function removeFile() {
         setAttributes({
            url: '',
            caption: '',
         })
      }

      return (
         <div {...blockProps}>
            {!url ? (
               <Placeholder
                  icon="media-document"
                  label={__('PDF Embed', 'bx-obastidor')}
                  instructions={__('Faça upload de um arquivo PDF ou escolha da biblioteca de mídia.', 'bx-obastidor')}>
                  <MediaUploadCheck>
                     <MediaUpload
                        onSelect={onSelectFile}
                        allowedTypes={['application/pdf']}
                        value={attachmentId}
                        render={({open}) => (
                           <Button onClick={open} isPrimary>
                              {__('Selecionar PDF', 'bx-obastidor')}
                           </Button>
                        )}
                     />
                  </MediaUploadCheck>
               </Placeholder>
            ) : (
               <Placeholder
                  icon="media-document"
                  label={__('Arquivo PDF', 'bx-obastidor')}
                  style={{marginBottom: '10px'}}>
                  <div style={{display: 'flex', gap: '10px'}}>
                     <MediaUploadCheck>
                        <MediaUpload
                           onSelect={onSelectFile}
                           allowedTypes={['application/pdf']}
                           value={attachmentId}
                           render={({open}) => (
                              <Button onClick={open} isSecondary>
                                 {__('Alterar arquivo', 'bx-obastidor')}
                              </Button>
                           )}
                        />
                     </MediaUploadCheck>
                     <Button onClick={removeFile} isDestructive>
                        {__('Remover', 'bx-obastidor')}
                     </Button>
                  </div>

                  <iframe
                     src={url}
                     width="100%"
                     height="768px"
                     style={{border: '1px solid #ddd', borderRadius: '4px'}}
                  />
               </Placeholder>
            )}
         </div>
      )
   }
   return settings
})
