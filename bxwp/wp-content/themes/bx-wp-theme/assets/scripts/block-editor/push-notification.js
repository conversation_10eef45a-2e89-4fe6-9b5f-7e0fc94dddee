import {registerPlugin} from '@wordpress/plugins'
import {PluginDocumentSettingPanel} from '@wordpress/edit-post'
import {MediaUploadCheck, MediaUpload} from '@wordpress/block-editor'
import {
   Button,
   Modal,
   Card,
   CardMedia,
   CardBody,
   CardFooter,
   CustomSelectControl,
   TextControl,
   TextareaControl,
   Notice,
} from '@wordpress/components'
import {__} from '@wordpress/i18n'
import {useSelect} from '@wordpress/data'
import {useState, useEffect, useMemo} from '@wordpress/element'
import apiFetch from '@wordpress/api-fetch'

const PushNotificationPanel = () => {
   const post = useSelect((select) => select('core/editor').getCurrentPost(), [])
   const postType = post?.type
   const postId = post?.id

   if (!['post', 'page'].includes(postType) || !postId) {
      return null
   }

   const currentPostView = useSelect((select) => {
      const postData = select('core').getEntityRecord('postType', postType, postId, {context: 'view'}) || null
      if (!postData) {
         return null
      }

      if (!postData.featured_media) {
         return postData
      }

      const featuredImage = select('core').getMedia(postData.featured_media)
      if (!featuredImage) {
         return postData
      }

      return {
         ...postData,
         featured_image_url: featuredImage.sizes?.thumbnail?.url || featuredImage.source_url,
      }
   })

   const [isModalOpen, setIsModalOpen] = useState(false)
   const [isFcmSettingsComplete, setIsFcmSettingsComplete] = useState(false)
   const [isLoading, setIsLoading] = useState(false)
   const [pushTitle, setPushTitle] = useState('')
   const [pushBody, setPushBody] = useState('')
   const [pushImage, setPushImage] = useState('')
   const [pushNewsId, setPushNewsId] = useState('')
   const [pushUrl, setPushUrl] = useState('')
   const [editingField, setEditingField] = useState(false)

   const pushSettingsAdminUrl = '/wp-admin/options-general.php?page=push-notification-settings'

   const testTopicKey = useMemo(() => '_certainly_unexistent_test_' + Math.random().toString(36).substring(2, 10), [])

   const topicOptions = useMemo(
      () => [
         {key: 'all', name: 'Todos'},
         {key: testTopicKey, name: 'Teste'},
      ],
      [testTopicKey],
   )
   const [pushTopic, setPushTopic] = useState(testTopicKey)

   const sendPushNotification = () => {
      setIsLoading(true)

      const message = {
         title: pushTitle,
         body: pushBody,
         image: pushImage || '',
         news_id: pushNewsId,
         topic: pushTopic,
         url: pushUrl || '',
      }

      return apiFetch({
         path: '/bx-obastidor/v1/send-push-notification',
         method: 'POST',
         data: message,
      })
         .then((data) => {
            console.log({data})
            setIsLoading(false)
            setIsModalOpen(false)
            return data
         })
         .catch((error) => {
            console.error('Erro ao enviar notificação:', error)
            setIsLoading(false)
            throw error
         })
   }

   const fetchFcmConfig = () => {
      setIsLoading(true)
      apiFetch({path: '/bx-obastidor/v1/check-fcm-settings'})
         .then((data) => {
            setIsFcmSettingsComplete(data.isSetupComplete)
            setIsLoading(false)
         })
         .catch((error) => {
            console.error('Erro ao buscar configurações do FCM:', error)
            setIsLoading(false)
         })
   }

   useEffect(() => {
      fetchFcmConfig()
   }, [])

   const stripHtml = (html) => {
      if (!html) {
         return ''
      }

      const tmp = document.createElement('div')
      tmp.innerHTML = html

      return tmp.textContent || tmp.innerText || ''
   }

   useEffect(() => {
      if (currentPostView) {
         setPushTitle(currentPostView.title.rendered || '')
         setPushBody(stripHtml(currentPostView.excerpt.rendered || ''))
         setPushImage(currentPostView.featured_image_url || '')
         setPushUrl(currentPostView.link || '')
      }
   }, [currentPostView])

   return (
      <PluginDocumentSettingPanel
         name="push-notification-panel"
         title={__('Notificação Push', 'bx-obastidor')}
         className="push-notification-panel">
         {(isFcmSettingsComplete && (
            <Button isSecondary onClick={() => setIsModalOpen(true)}>
               {__('Enviar Notificação', 'bx-obastidor')}
            </Button>
         )) || (
            <Notice status="warning" isDismissible={false}>
               <p>
                  {__(
                     'A configuração do FCM está incompleta. Por favor, configure o FCM antes de enviar notificações.',
                     'bx-obastidor',
                  )}
               </p>
               <p>
                  <Button isLink href={pushSettingsAdminUrl}>
                     {__('Abrir configurações', 'bx-obastidor')}
                  </Button>
               </p>
            </Notice>
         )}
         {isModalOpen && (
            <Modal
               title={
                  (pushTopic === testTopicKey ? '[' + __('TESTE', 'bx-obastidor') + ']' : '') +
                  ' ' +
                  __('Enviar Notificação', 'bx-obastidor')
               }
               onRequestClose={() => setIsModalOpen(false)}
               size="medium">
               <Card>
                  <CardMedia>
                     <MediaUploadCheck>
                        <MediaUpload
                           label={__('Imagem', 'bx-obastidor')}
                           onSelect={(media) => setPushImage(media.sizes?.thumbnail?.url || media.url)}
                           allowedTypes={['image']}
                           render={({open}) =>
                              pushImage ? (
                                 <img src={pushImage} alt={pushTitle} />
                              ) : (
                                 <Button onClick={open}>{__('Selecionar Imagem', 'bx-obastidor')}</Button>
                              )
                           }
                        />
                     </MediaUploadCheck>
                  </CardMedia>
                  <CardBody isBorderless={true}>
                     <TextControl label={__('Título', 'bx-obastidor')} value={pushTitle} onChange={setPushTitle} />
                     <TextareaControl label={__('Mensagem', 'bx-obastidor')} value={pushBody} onChange={setPushBody} />
                     <TextControl label={__('URL', 'bx-obastidor')} value={pushUrl} onChange={setPushUrl} />
                  </CardBody>
                  <CardFooter isBorderless={false}>
                     <div style={{width: '100%'}}>
                        <CustomSelectControl
                           label={__('Tópico', 'bx-obastidor')}
                           value={topicOptions.find((option) => option.key === pushTopic)}
                           onChange={({selectedItem}) => setPushTopic(selectedItem.key)}
                           options={topicOptions}
                        />
                     </div>
                  </CardFooter>
                  <CardFooter isBorderless={true}>
                     <p>
                        {pushTopic === testTopicKey ? (
                           <span className="components-notice is-warning">
                              {__('Notificação de teste e não será entregue aos usuários.', 'bx-obastidor')}
                           </span>
                        ) : (
                           __('Ao confirmar, todos os usuários do O Bastidor serão notificados.', 'bx-obastidor')
                        )}
                     </p>
                  </CardFooter>
                  <CardFooter isBorderless={true}>
                     <Button isPrimary onClick={() => sendPushNotification()}>
                        {__('Confirmar Envio', 'bx-obastidor')}
                     </Button>
                  </CardFooter>
               </Card>
            </Modal>
         )}
      </PluginDocumentSettingPanel>
   )
}

registerPlugin('push-notification-panel', {
   render: PushNotificationPanel,
   icon: null,
})
