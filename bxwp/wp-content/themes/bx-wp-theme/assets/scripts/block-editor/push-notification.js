import {registerPlugin} from '@wordpress/plugins'
import {PluginDocumentSettingPanel} from '@wordpress/editor'
import {MediaUploadCheck, MediaUpload} from '@wordpress/block-editor'
import {
   Button,
   Modal,
   Card,
   CardMedia,
   CardBody,
   CardFooter,
   Spinner,
   TextControl,
   TextareaControl,
   Notice,
} from '@wordpress/components'
import {__} from '@wordpress/i18n'
import {useSelect} from '@wordpress/data'
import {useState, useEffect} from '@wordpress/element'
import apiFetch from '@wordpress/api-fetch'

const PushNotificationPanel = () => {
   const post = useSelect((select) => select('core/editor').getCurrentPost(), [])
   const postType = post?.type
   const postId = post?.id

   if (postType !== 'post' || !postId) {
      return null
   }

   const userRoles = bxObastidorEditor?.userRoles || {}
   const isAdministrator = userRoles.isAdministrator || false

   const canEditExclusiveContent = isAdministrator

   if (!canEditExclusiveContent) {
      return null
   }

   const currentPostView = useSelect((select) => {
      const postData = select('core').getEntityRecord('postType', postType, postId, {context: 'view'}) || null
      if (!postData) {
         return null
      }

      if (!postData.featured_media) {
         return postData
      }

      const featuredImage = select('core').getMedia(postData.featured_media)
      if (!featuredImage) {
         return postData
      }

      return {
         ...postData,
         featured_image_url: featuredImage.sizes?.thumbnail?.url || featuredImage.source_url,
      }
   })

   const [isModalOpen, setIsModalOpen] = useState(false)
   const [isFcmSettingsComplete, setIsFcmSettingsComplete] = useState(false)
   const [isLoading, setIsLoading] = useState(false)
   const [pushTitle, setPushTitle] = useState('')
   const [pushBody, setPushBody] = useState('')
   const [pushImage, setPushImage] = useState('')
   const [pushNewsId, setPushNewsId] = useState('')
   const [pushUrl, setPushUrl] = useState('')

   const pushSettingsAdminUrl = '/wp-admin/options-general.php?page=push-notification-settings'

   const sendPushNotification = () => {
      setIsLoading(true)

      const message = {
         title: pushTitle,
         body: pushBody,
         image: pushImage || '',
         news_id: pushNewsId,
         topic: 'all',
         url: pushUrl || '',
      }

      console.log({message})

      return apiFetch({
         path: '/bx-obastidor/v1/send-push-notification',
         method: 'POST',
         data: message,
      })
         .then((data) => {
            console.log({data})
            setIsLoading(false)
            setIsModalOpen(false)
            return data
         })
         .catch((error) => {
            console.error('Erro ao enviar notificação:', error)
            setIsLoading(false)
            throw error
         })
   }

   const fetchFcmConfig = () => {
      setIsLoading(true)
      apiFetch({path: '/bx-obastidor/v1/check-fcm-settings'})
         .then((data) => {
            setIsFcmSettingsComplete(data.isSetupComplete)
            setIsLoading(false)
         })
         .catch((error) => {
            console.error('Erro ao buscar configurações do FCM:', error)
            setIsLoading(false)
         })
   }

   useEffect(() => {
      fetchFcmConfig()
   }, [])

   const stripHtml = (html) => {
      if (!html) {
         return ''
      }

      const tmp = document.createElement('div')
      tmp.innerHTML = html

      return tmp.textContent || tmp.innerText || ''
   }

   useEffect(() => {
      if (currentPostView) {
         setPushTitle(currentPostView.title.rendered || '')
         setPushBody(stripHtml(currentPostView.excerpt.rendered || ''))
         setPushImage(currentPostView.featured_image_url || '')
         setPushUrl(currentPostView.link || '')
         setPushNewsId(currentPostView.id || '')
      }
   }, [currentPostView])

   console.log({
      title: pushTitle,
      body: pushBody,
      image: pushImage,
      url: pushUrl,
      news_id: pushNewsId,
   })

   return (
      isLoading ? (
         <div className="push-notification-panel-loading">
            <Spinner />
         </div>
      ) : (
      <PluginDocumentSettingPanel
         name="push-notification-panel"
         title={__('Notificação Push', 'bx-obastidor')}
         className="push-notification-panel">
         {(isFcmSettingsComplete && (
            <Button
               variant="secondary"
               onClick={() => setIsModalOpen(true)}
               icon="bell"
               style={{width: '100%', justifyContent: 'center'}}>
               {__('Enviar Notificação', 'bx-obastidor')}
            </Button>
         )) || (
            <Notice status="warning" isDismissible={false}>
               <p>
                  {__(
                     'A configuração do FCM está incompleta. Por favor, configure o FCM antes de enviar notificações.',
                     'bx-obastidor',
                  )}
               </p>
               <p>
                  <Button variant="link" href={pushSettingsAdminUrl}>
                     {__('Abrir configurações', 'bx-obastidor')}
                  </Button>
               </p>
            </Notice>
         )}
         {isModalOpen && (
            <Modal
               title={__('Enviar Notificação', 'bx-obastidor')}
               onRequestClose={() => setIsModalOpen(false)}
               size="medium">
               <Card>
                  <CardMedia>
                     <MediaUploadCheck>
                        <MediaUpload
                           label={__('Imagem', 'bx-obastidor')}
                           onSelect={(media) => setPushImage(media.sizes?.thumbnail?.url || media.url)}
                           allowedTypes={['image']}
                           render={({open}) =>
                              pushImage ? (
                                 <img src={pushImage} alt={pushTitle} />
                              ) : (
                                 <Button onClick={open}>{__('Selecionar Imagem', 'bx-obastidor')}</Button>
                              )
                           }
                        />
                     </MediaUploadCheck>
                  </CardMedia>
                  <CardBody isBorderless={true}>
                     <TextControl
                        label={__('Título', 'bx-obastidor')}
                        value={pushTitle}
                        onChange={setPushTitle}
                        __next40pxDefaultSize={true}
                        __nextHasNoMarginBottom={true}
                     />
                     <TextareaControl
                        label={__('Mensagem', 'bx-obastidor')}
                        value={pushBody}
                        onChange={setPushBody}
                        __next40pxDefaultSize={true}
                        __nextHasNoMarginBottom={true}
                     />
                  </CardBody>
                  <CardFooter isBorderless={true}>
                     <p>
                        {
                           __('Ao confirmar, todos os usuários do O Bastidor serão notificados.', 'bx-obastidor')
                        }
                     </p>
                  </CardFooter>
                  <CardFooter isBorderless={true}>
                     <Button variant="primary" onClick={() => sendPushNotification()}>
                        {__('Confirmar Envio', 'bx-obastidor')}
                     </Button>
                  </CardFooter>
               </Card>
            </Modal>
         )}
      </PluginDocumentSettingPanel>
      )
   )
}

registerPlugin('push-notification-panel', {
   render: PushNotificationPanel,
   icon: null,
})
