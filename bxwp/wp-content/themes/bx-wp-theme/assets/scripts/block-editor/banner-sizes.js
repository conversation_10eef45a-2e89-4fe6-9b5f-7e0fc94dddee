import {registerPlugin} from '@wordpress/plugins'
import {PluginDocumentSettingPanel} from '@wordpress/edit-post'
import {PanelBody, SelectControl} from '@wordpress/components'
import {__} from '@wordpress/i18n'
import {useSelect, useDispatch} from '@wordpress/data'
import {useState} from '@wordpress/element'

const postTypes = ['ad_banner'];

const AdBannerSizesPanel = () => {
    const post = useSelect((select) => select('core/editor').getCurrentPost(), [])
    const postId = post?.id
    const postType = post?.type
    const currentPostBlocks = useSelect((select) => select('core/editor').getBlocks(), [])

    const [selectedSizeData, setSelectedSizeData] = useState(null)
    const [selectedSizeId, setSelectedSizeId] = useState(post?.ad_banner_size?.[0] || null)

    const {editEntityRecord} = useDispatch('core')
    const {updateBlock} = useDispatch('core/block-editor')

    if (!post || !postTypes.includes(postType)) {
       return null
    }

    const adBannerSizes =
       useSelect((select) => {
          return select('core').getEntityRecords('taxonomy', 'ad_banner_size', {
             per_page: -1,
             hide_empty: false,
          })
       }, []) || []

    const sizeOptions = adBannerSizes
       ? adBannerSizes.map((term) => ({
            label: term.name,
            value: term.id,
         }))
       : []

    const updateSelectedSizeId = (value) => {
       setSelectedSizeId(parseInt(value) || null)

       const {
          meta: {ad_banner_size_width: width, ad_banner_size_height: height},
       } = adBannerSizes.find((s) => s.id === parseInt(value)) || {
         meta: {
            ad_banner_size_width: undefined,
            ad_banner_size_height: undefined,
         }
       }

       const imageBlock = currentPostBlocks.find((b) => b.name === 'core/image')

       if (imageBlock) {
          updateBlock(imageBlock.clientId, {
             ...imageBlock,
             attributes: {
                ...imageBlock.attributes,
                width: width ? width + 'px' : undefined,
                height: height ? height + 'px' : undefined,
             },
          })
       }

       editEntityRecord('postType', 'ad_banner', postId, {
          ad_banner_size: value ? [parseInt(value)] : [],
       })
    }

    return (
       <PluginDocumentSettingPanel name="ad-banner-sizes-panel" title={__('Tamanho do Banner', 'bx-obastidor')}>
          <PanelBody>
             <SelectControl
                label={__('Selecione o tamanho', 'bx-obastidor')}
                value={selectedSizeId}
                options={[
                   {
                      label: __('Selecione o tamanho', 'bx-obastidor'),
                      value: '',
                   },
                   ...sizeOptions,
                ]}
                onChange={updateSelectedSizeId}
                required
             />
          </PanelBody>
       </PluginDocumentSettingPanel>
    )
 }

 registerPlugin('ad-banner-sizes-panel', {
    render: AdBannerSizesPanel,
    icon: null,
 })
