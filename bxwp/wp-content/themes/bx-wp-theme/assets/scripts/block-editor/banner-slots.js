import {registerPlugin} from '@wordpress/plugins'
import {PluginDocumentSettingPanel} from '@wordpress/edit-post'
import {PanelBody, CheckboxControl} from '@wordpress/components'
import {__} from '@wordpress/i18n'
import {useSelect, useDispatch} from '@wordpress/data'
import {useEntityProp} from '@wordpress/core-data'
import {useEffect} from '@wordpress/element'

const postTypes = ['ad_banner'];

const AdBannerSlotsPanel = () => {
   const post = useSelect((select) => select('core/editor').getCurrentPost(), [])
   const postId = post?.id
   const postType = post?.type

   if (!post || !postTypes.includes(postType)) {
      return null
   }

   const currentBannerSize = useSelect(
      (select) => {
         const editedPost = select('core').getEditedEntityRecord('postType', postType, postId)
         const currentBannerSizeTerm = select('core').getEntityRecord(
            'taxonomy',
            'ad_banner_size',
            editedPost?.ad_banner_size?.[0] || null,
         )
         return currentBannerSizeTerm
      },
      [postId],
   )

   const [adBannerSlots, setAdBannerSlots] = useEntityProp('postType', postType, 'ad_banner_slot')

   const toggleSlot = (slotId) => {
      const currentSlots = adBannerSlots || []
      const newSlots = currentSlots.includes(slotId)
         ? currentSlots.filter((id) => id !== slotId)
         : [...currentSlots, slotId]

      setAdBannerSlots(newSlots)
   }

   const [slots, isLoadingSlots] = useSelect(
      (select) => {
         const adBannerSlotTerms =
            select('core').getEntityRecords('taxonomy', 'ad_banner_slot', {
               per_page: -1,
               hide_empty: false,
            })

         const filteredSlots = adBannerSlotTerms?.filter((slot) => {
            const slotSize = slot.meta?.ad_banner_slot_size
            return slotSize === currentBannerSize?.slug
         })

         return [filteredSlots || [], !filteredSlots]
      },
      [currentBannerSize],
   )

      // Efeito para desmarcar slots que não correspondem ao tamanho atual
   useEffect(() => {
      if (!adBannerSlots || adBannerSlots.length === 0) {
         return
      }

      // Aguardar os slots serem carregados antes de processar
      if (isLoadingSlots) {
         return
      }

      const slotIdsToUncheck = adBannerSlots.filter(slotId => {
         // Encontrar o termo do slot
         const slotTerm = slots.find(slot => slot.id === slotId)
         if (!slotTerm) return true // Se não encontrar o termo, desmarcar

         // Verificar se o tamanho do slot corresponde ao tamanho do banner
         return slotTerm.meta?.ad_banner_slot_size !== currentBannerSize.slug
      })

      if (slotIdsToUncheck.length > 0) {
         slotIdsToUncheck.forEach(slotId => {
            toggleSlot(slotId)
         })
      }
   }, [currentBannerSize, slots, adBannerSlots])

   const {isSaving, isAutosaving} = useSelect((select) => ({
      isSaving: select('core/editor').isSavingPost(),
      isAutosaving: select('core/editor').isAutosavingPost(),
   }))

   const { lockPostSaving, unlockPostSaving } = useDispatch('core/editor');

   useEffect(() => {
      if (isSaving || isAutosaving || adBannerSlots.length === 0) {
         lockPostSaving('missingRequiredField');
      }
      else {
         unlockPostSaving('missingRequiredField');
      }
   }, [isSaving, isAutosaving, adBannerSlots, lockPostSaving, unlockPostSaving])

   return (
      <PluginDocumentSettingPanel name="ad-banner-slots-panel" title={__('Posições', 'bx-obastidor')}>
         <PanelBody>
            {slots.length ? (
               slots.map((slot) => (
                  <CheckboxControl
                     label={slot.name}
                     checked={(adBannerSlots || []).includes(slot.id)}
                     onChange={() => toggleSlot(slot.id)}
                  />
               ))
            ) : (
               <p>{__('Selecione o tamanho do banner para exibir as posições disponíveis.', 'bx-obastidor')}</p>
            )}
         </PanelBody>
      </PluginDocumentSettingPanel>
   )
}

registerPlugin('ad-banner-slots-panel', {
   render: AdBannerSlotsPanel,
   icon: null,
})
