import {registerPlugin} from '@wordpress/plugins'
import {PluginDocumentSettingPanel} from '@wordpress/edit-post'
import {PanelBody, CheckboxControl} from '@wordpress/components'
import {__} from '@wordpress/i18n'
import {useSelect} from '@wordpress/data'
import {useEntityProp} from '@wordpress/core-data'

const postTypes = ['ad_banner'];

const AdBannerSlotsPanel = () => {
   const post = useSelect((select) => select('core/editor').getCurrentPost(), [])
   const postId = post?.id
   const postType = post?.type

   if (!post || !postTypes.includes(postType)) {
      return null
   }

   const currentBannerSize = useSelect(
      (select) => {
         const editedPost = select('core').getEditedEntityRecord('postType', 'ad_banner', postId)
         const currentBannerSizeTerm = select('core').getEntityRecord(
            'taxonomy',
            'ad_banner_size',
            editedPost?.ad_banner_size?.[0] || null,
         )
         return currentBannerSizeTerm
      },
      [postId],
   )

   const slots = useSelect(
      (select) => {
         const bannerAdSlotTerms =
            select('core').getEntityRecords('taxonomy', 'ad_banner_slot', {
               per_page: -1,
               hide_empty: false,
            }) || []

         return bannerAdSlotTerms.filter((slot) => {
            const slotSize = slot.meta?.ad_banner_slot_size
            return slotSize === currentBannerSize?.slug
         })
      },
      [currentBannerSize],
   )

   const [adBannerSlots, setAdBannerSlots] = useEntityProp('postType', 'ad_banner', 'ad_banner_slot')

   const toggleSlot = (slotId) => {
      const currentSlots = adBannerSlots || []
      const newSlots = currentSlots.includes(slotId)
         ? currentSlots.filter((id) => id !== slotId)
         : [...currentSlots, slotId]

      setAdBannerSlots(newSlots)
   }

   return (
      <PluginDocumentSettingPanel name="ad-banner-slots-panel" title={__('Posições', 'bx-obastidor')}>
         <PanelBody>
            {slots.length ? (
               slots.map((slot) => (
                  <CheckboxControl
                     label={slot.name}
                     checked={(adBannerSlots || []).includes(slot.id)}
                     onChange={() => toggleSlot(slot.id)}
                  />
               ))
            ) : (
               <p>{__('Selecione o tamanho do banner para exibir as posições disponíveis.', 'bx-obastidor')}</p>
            )}
         </PanelBody>
      </PluginDocumentSettingPanel>
   )
}

registerPlugin('ad-banner-slots-panel', {
   render: AdBannerSlotsPanel,
   icon: null,
})
