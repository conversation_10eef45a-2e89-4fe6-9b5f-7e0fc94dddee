import {__, _n, sprintf} from '@wordpress/i18n'
import {registerPlugin} from '@wordpress/plugins'
import {PluginDocumentSettingPanel} from '@wordpress/edit-post'
import {<PERSON><PERSON>, Modal, Card, CardBody, CardFooter} from '@wordpress/components'
import {useSelect, useDispatch} from '@wordpress/data'
import {useState, useEffect} from '@wordpress/element'
import apiFetch from '@wordpress/api-fetch'

const PostNewsletterPanel = () => {
   const post = useSelect((select) => select('core/editor').getCurrentPost(), [])
   const postType = post?.type
   const postId = post?.id

   if (postType !== 'post' || !postId) {
      return null
   }

   const userRoles = bxObastidorEditor?.userRoles || {}
   const isAdministrator = userRoles.isAdministrator || false

   const canEditExclusiveContent = isAdministrator

   if (!canEditExclusiveContent) {
      return null
   }

   const [isSendingModalOpen, setIsSendingModalOpen] = useState(false)
   const [recipientsCount, setRecipientsCount] = useState(null)
   const [isLoadingRecipients, setIsLoadingRecipients] = useState(true)
   const [isSendingNewsletter, setIsSendingNewsletter] = useState(false)

   const {createNotice} = useDispatch('core/notices')

   useEffect(() => {
      setIsLoadingRecipients(true)

      apiFetch({
         path: '/bx-obastidor/v1/subscribers?list_name=alerta',
         method: 'GET',
      })
         .then(({data}) => {
            setRecipientsCount(data.length)
         })
         .catch((error) => {
            console.error('Erro ao carregar inscritos:', error)
            setRecipientsCount(0)
         })
         .finally(() => {
            setIsLoadingRecipients(false)
         })
   }, [])

   const sendNewsletter = () => {
      setIsSendingNewsletter(true)
      apiFetch({
         path: '/bx-obastidor/v1/send-newsletter',
         method: 'POST',
         data: {
            list_name: 'alerta',
            post_ids: [postId],
            headers: ['X-BX-OBASTIDOR-MAIL-TYPE: newsletter'],
         },
      })
         .then((data) => {
            setIsSendingModalOpen(false)
            createNotice('success', __('Newsletter enviada com sucesso!', 'bx-obastidor'))
         })
         .catch((error) => {
            createNotice('error', __('Erro ao enviar a newsletter. Tente novamente.', 'bx-obastidor'))
         })
         .finally(() => {
            setIsSendingNewsletter(false)
         })
   }

   return (
      <PluginDocumentSettingPanel name="post-newsletter-panel" title={__('Newsletter', 'bx-obastidor')}>
         <Button
            isSecondary
            onClick={() => setIsSendingModalOpen(true)}
            icon="email-alt"
            style={{width: '100%', justifyContent: 'center'}}>
            {__('Enviar Newsletter', 'bx-obastidor')}
         </Button>

         {isSendingModalOpen && (
            <Modal title={__('Enviar Newsletter', 'bx-obastidor')} onRequestClose={() => setIsSendingModalOpen(false)}>
               <Card isBorderless>
                  <CardBody>
                     <p>
                        {recipientsCount > 0
                           ? sprintf(
                                _n(
                                   'O post será enviado para %d inscrito nos "Alertas e notícias exclusivas"',
                                   'O post será enviado para %d inscritos nos "Alertas e notícias exclusivas"',
                                   recipientsCount,
                                   'bx-obastidor',
                                ),
                                recipientsCount,
                             )
                           : __('Não há inscritos para receber a newsletter.', 'bx-obastidor')}
                     </p>
                  </CardBody>
                  <CardFooter>
                     <Button
                        isPrimary
                        onClick={sendNewsletter}
                        disabled={isLoadingRecipients || recipientsCount === 0 || isSendingNewsletter}>
                        {isSendingNewsletter
                           ? __('Enviando...', 'bx-obastidor')
                           : __('Enviar Newsletter', 'bx-obastidor')}
                     </Button>
                  </CardFooter>
               </Card>
            </Modal>
         )}
      </PluginDocumentSettingPanel>
   )
}

registerPlugin('post-newsletter-panel', {
   render: PostNewsletterPanel,
})
