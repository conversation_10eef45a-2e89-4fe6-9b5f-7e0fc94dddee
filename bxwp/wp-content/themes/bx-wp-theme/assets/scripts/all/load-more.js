document.addEventListener('DOMContentLoaded', function () {
   const newsContainer = document.getElementById('news-container')
   const loadMoreButton = document.getElementById('load-more-news')

   if (!newsContainer || !loadMoreButton) {
      return
   }

   const buttonText = loadMoreButton.querySelector('.button-text')
   const loadingText = loadMoreButton.querySelector('.loading-text')

   function showLoading() {
      loadMoreButton.disabled = true
      buttonText.classList.add('hidden')
      loadingText.classList.remove('hidden')
   }

   function hideLoading() {
      loadMoreButton.disabled = false
      buttonText.classList.remove('hidden')
      loadingText.classList.add('hidden')
   }

   function removeButton() {
      loadMoreButton.remove()
   }

   function showError(message) {
      hideLoading()
      buttonText.textContent = message || ajax.messages.load_more_error
   }

   function loadMoreNews() {
      const queryArgs = JSON.parse(loadMoreButton.dataset.queryArgs)

      showLoading()

      const url = new URL(ajax.ajaxUrl)
      url.searchParams.append('action', 'load_more_news')
      url.searchParams.append('query_args', JSON.stringify(queryArgs))
      url.searchParams.append('_ajax_nonce', ajax.ajaxNonce)

      fetch(url, {
         method: 'GET',
      })
         .then((response) => {
            if (!response.ok) {
               throw new Error(`HTTP error! status: ${response.status}`)
            }

            return response.json()
         })
         .then((data) => {
            if (data.success && data.data.html) {
               newsContainer.insertAdjacentHTML('beforeend', data.data.html)

               queryArgs.paged = data.data.current_page + 1

               loadMoreButton.dataset.queryArgs = JSON.stringify(queryArgs)

               const showLoadMore = queryArgs.paged <= queryArgs.max_num_pages

               if (!showLoadMore) {
                  removeButton()
               } else {
                  hideLoading()
               }
            } else {
               const errorMessage = data.data && data.data.messages ? data.data.messages : ajax.messages.load_more_error
               throw new Error(errorMessage)
            }
         })
         .catch((error) => {
            showError(error.message || ajax.messages.load_more_error)
         })
   }

   loadMoreButton.addEventListener('click', function (e) {
      e.preventDefault()
      loadMoreNews()
   })
})
