const sendAdStats = (bannerId, eventId, targetUrl = null) => {
   Promise.race([
      fetch(`${ajax.restUrl}ad-banner/stats`, {
         method: 'POST',
         headers: {
            'Content-Type': 'application/json',
            'X-WP-Nonce': ajax.restNonce,
         },
         body: JSON.stringify({banner_id: bannerId, event: eventId}),
      }),
      new Promise((resolve) => setTimeout(resolve, 2000)),
   ])
      .catch()
      .finally(() => {
         if (eventId === 'click' && targetUrl) {
            window.location.href = targetUrl
         }
      })
}

const setAdEvent = (eventId) => (event) => {
   const bannerId = event.target.closest('[data-banner-id]').getAttribute('data-banner-id')
   const targetUrl = eventId === 'click' ? event.target.closest('a').href : null

   if (eventId === 'click') {
      event.preventDefault()
   }

   sendAdStats(bannerId, eventId, targetUrl)
}

document.addEventListener('DOMContentLoaded', () => {
   document.addEventListener(
      'load',
      (event) => {
         if (event.target.closest('[data-banner-id]') && event.target.matches('img, iframe, embed')) {
            setAdEvent('view')(event)
         }
      },
      true,
   )

   document.addEventListener(
      'click',
      (event) => {
         if (event.target.closest('[data-banner-id]') && event.target.closest('a')) {
            setAdEvent('click')(event)
         }
      },
      true,
   )
})
