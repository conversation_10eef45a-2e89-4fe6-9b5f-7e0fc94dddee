document.addEventListener('alpine:init', () => {
   window.Alpine.data('meuBastidorData', () => ({
      showRemovalModal: false,
      isLoading: false,
      hasChanges: false,
      originalPreferences: {
         categories: [],
         notifications: [],
         current_user: {
            first_name: '',
            last_name: '',
            email: '',
            password: '',
         },
      },
      preferences: {
         categories: [],
         notifications: [],
         current_user: {
            first_name: '',
            last_name: '',
            email: '',
            password: '',
         },
      },
      isValid: {
         first_name: true,
         last_name: true,
         email: true,
         password: true,
      },

      init() {
         this.getPreferences()
      },

      toggleCategory(categoryId) {
         if (this.preferences.categories.includes(categoryId)) {
            this.preferences.categories = this.preferences.categories.filter((id) => id !== categoryId)
         } else {
            this.preferences.categories.push(categoryId)
         }

         this.checkForChanges()
      },

      toggleNotification(notificationId) {
         if (this.preferences.notifications.includes(notificationId)) {
            this.preferences.notifications = this.preferences.notifications.filter((id) => id !== notificationId)
         } else {
            this.preferences.notifications.push(notificationId)
         }

         this.checkForChanges()
      },

      checkForChanges() {
         const categoriesChanged =
            JSON.stringify(this.preferences.categories.sort()) !==
            JSON.stringify(this.originalPreferences.categories.sort())
         const notificationsChanged =
            JSON.stringify(this.preferences.notifications.sort()) !==
            JSON.stringify(this.originalPreferences.notifications.sort())
         const currentUserChanged =
            this.preferences.current_user.first_name !== this.originalPreferences.current_user.first_name ||
            this.preferences.current_user.last_name !== this.originalPreferences.current_user.last_name ||
            this.preferences.current_user.email !== this.originalPreferences.current_user.email ||
            this.preferences.current_user.password !== this.originalPreferences.current_user.password

         this.hasChanges = categoriesChanged || notificationsChanged || currentUserChanged
      },

      openRemovalModal() {
         this.showRemovalModal = true
      },

      closeRemovalModal() {
         this.showRemovalModal = false
      },

      deleteAccount() {
         this.isLoading = true

         fetch(ajax.restUrl + 'delete-account', {
            method: 'DELETE',
            headers: {
               'Content-Type': 'application/json',
               'X-WP-Nonce': ajax.restNonce,
            },
            body: JSON.stringify({}),
         })
            .then((response) => response.json())
            .then((data) => {
               if (data.success) {
                  const redirectUrl = data.data?.redirect_to || '/'
                  window.location.href = redirectUrl
               }
            })
            .catch((error) => {
               console.error('Erro ao remover conta:', error)
            })
            .finally(() => {
               this.isLoading = false
            })
      },

      getPreferences() {
         fetch(ajax.restUrl + 'get-preferences', {
            method: 'GET',
            headers: {
               'Content-Type': 'application/json',
               'X-WP-Nonce': ajax.restNonce,
            },
         })
            .then((response) => response.json())
            .then((data) => {
               this.preferences = data.data
               this.originalPreferences = {
                  categories: [...data.data.categories],
                  notifications: [...data.data.notifications],
                  current_user: {
                     first_name: data.data.current_user.first_name,
                     last_name: data.data.current_user.last_name,
                     email: data.data.current_user.email,
                     password: data.data.current_user.password,
                  },
               }
            })
            .then(() => {
               document.querySelectorAll('input').forEach((input) => {
                  if (Object.keys(this.isValid).includes(input.name)) {
                     this.setFieldValidity(input)
                  }
               })
            })
            .catch((error) => {
               console.error('Erro ao obter preferências:', error)
            })
      },

      savePreferences() {
         this.isLoading = true

         fetch(ajax.restUrl + 'save-preferences', {
            method: 'POST',
            headers: {
               'Content-Type': 'application/json',
               'X-WP-Nonce': ajax.restNonce,
            },
            body: JSON.stringify({
               categories: this.preferences.categories,
               notifications: this.preferences.notifications,
               current_user: this.preferences.current_user,
            }),
         })
            .then((response) => {
               if (!response.ok) {
                  throw new Error(`HTTP error! status: ${response.status}`)
               }
               return response.json()
            })
            .then((data) => {
               this.originalPreferences = {
                  categories: [...this.preferences.categories],
                  notifications: [...this.preferences.notifications],
                  current_user: {
                     first_name: this.preferences.current_user.first_name,
                     last_name: this.preferences.current_user.last_name,
                     email: this.preferences.current_user.email,
                     password: this.preferences.current_user.password,
                  },
               }
               this.hasChanges = false
            })
            .catch((error) => {
               console.error('Erro ao salvar preferências:', error)
            })
            .finally(() => {
               this.isLoading = false
            })
      },

      setFieldValidity(element) {
         if (!element.name || typeof this.isValid[element.name] === 'undefined') {
            return
         }

         this.isValid[element.name] = element.validity.valid;

         this.checkForChanges()
      },

      resetForm() {
         this.preferences = {
            categories: [...this.originalPreferences.categories],
            notifications: [...this.originalPreferences.notifications],
            current_user: {
               first_name: this.originalPreferences.current_user.first_name,
               last_name: this.originalPreferences.current_user.last_name,
               email: this.originalPreferences.current_user.email,
               password: this.originalPreferences.current_user.password,
            },
         }

         this.checkForChanges()
      }

   }));

   window.Alpine.data('meuBastidorLoginData', () => ({
      formFields: {
         user_name: {
            value: '',
            valid: true
         },
         user_lastname: {
            value: '',
            valid: true
         },
         user_email: {
            value: '',
            valid: true
         },
         user_password: {
            value: '',
            valid: true
         },
         user_login: {
            value: '',
            valid: true
         },
         magic_link_login: {
            value: '',
            valid: true
         }
      },

      setFieldValidity(element) {
         if (!element.name || typeof this.formFields?.[element.name]?.valid === 'undefined') {
            return
         }

         this.formFields[element.name].valid = element.validity.valid;
      },

      checkFormValidity() {
         document.querySelectorAll('input').forEach((input) => {
            if (Object.keys(this.formFields).includes(input.name)) {
               this.setFieldValidity(input)
            }
         })
      },

      init() {
         this.checkFormValidity()
      },

   }))
})
