document.addEventListener('alpine:init', () => {
   window.Alpine.data('accountModals', () => ({
      showRemovalModal: false,
      isLoading: false,

      openRemovalModal() {
         this.showRemovalModal = true
      },

      closeRemovalModal() {
         this.showRemovalModal = false
      },

      deleteAccount() {
         this.isLoading = true

         fetch(ajax.restUrl + 'delete-account', {
            method: 'POST',
            headers: {
               'Content-Type': 'application/json',
               'X-WP-Nonce': ajax.restNonce,
            },
            body: JSON.stringify({}),
         })
            .then((response) => response.json())
            .then((data) => {
               if (data.success) {
                  const redirectUrl = data.data?.redirect_to || '/'
                  window.location.href = redirectUrl
               }
            })
            .catch((error) => {
               console.error('Erro ao remover conta:', error)
            })
            .finally(() => {
               this.isLoading = false
            })
      },
   }))
})
