document.addEventListener('DOMContentLoaded', () => {
   if (!document.body.classList.contains('single')) {
      return
   }

   fetch(ajax.restUrl + 'view-counter', {
      method: 'POST',
      headers: {
         'Content-Type': 'application/json',
         'X-WP-Nonce': ajax.restNonce,
      },
      body: JSON.stringify({
         postId: ajax.postId,
      }),
   }).catch((error) => {
      console.error('Error:', error)
   })
})
