document.addEventListener('DOMContentLoaded', () => {
   if (!document.body.classList.contains('single')) {
      return
   }

   apiFetch('/wp-json/bxwp/v1/view-counter', {
      method: 'POST',
      headers: {
         'Content-Type': 'application/json',
      },
      body: JSON.stringify({
         postId: ajax.postId,
      }),
   }).catch((error) => {
      console.error('Error:', error)
   })
})
