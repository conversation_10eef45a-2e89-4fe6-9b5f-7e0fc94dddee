class StickyHeader {
   constructor(selector) {
      this.header = document.querySelector(selector)
      this.header.style.transition = 'transform .75s ease-in-out'

      this.setEventListeners()
      this.setHeaderPostition()
      this.setHeaderVisibility()
   }

   setEventListeners() {
      window.addEventListener('resize', this.setHeaderPostition.bind(this))
      window.addEventListener('scroll', this.setHeaderVisibility.bind(this))
   }

   setHeaderPostition() {
      this.initialHeader = this.header.getBoundingClientRect()
      this.wpBar = document.getElementById('wpadminbar') || null
      this.header.style.position = 'fixed'
      this.header.style.zIndex = '50'
      this.header.style.width = '100%'
      this.header.style.top = 0
      this.setPlaceholder()
   }

   setHeaderVisibility() {
      const topScrollLimit = this.initialHeader.height + (this.wpBar ? this.wpBar.getBoundingClientRect().height : 0)

      if (window.scrollY < topScrollLimit) {
         this.show()
      } else {
         const direction = window.scrollY > (this.lastScrollY || 0) ? 'down' : 'up'

         if (direction === 'up') {
            this.show()
         } else {
            this.hide()
         }
      }

      this.lastScrollY = window.scrollY
   }

   hide() {
      const yPosition = Math.abs(this.initialHeader.bottom)

      this.header.style.transform = `translateY(calc(-100% - ${yPosition}px))`
   }

   show() {
      const yPosition = Math.max(0, this.wpBar ? this.wpBar.getBoundingClientRect().bottom : 0)

      this.header.style.transform = `translateY(${yPosition}px)`
   }

   setPlaceholder() {
      if (this.headerPlaceholder) {
         this.headerPlaceholder.remove()
      }

      this.headerPlaceholder = document.createElement('div')
      this.headerPlaceholder.style.height = `${this.initialHeader.height}px`
      this.header.after(this.headerPlaceholder)
   }
}

new StickyHeader('#main-header')
