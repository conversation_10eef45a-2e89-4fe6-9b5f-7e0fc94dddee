import 'cookieconsent'

window.cookieconsent.initialise({
   content: {
      message: 'Be<PERSON>-vindo ao <strong><PERSON><PERSON><PERSON><PERSON></strong>. Sua visita é anônima: não coletamos dados pessoais sem seu consentimento expresso. Conheça nosso <a href="' + ajax.privacyPolicyUrl + '">Compromisso de Privacidade</a>. Ao acessar nosso site, você concorda com nossos <a href="' +  ajax.termsOfUseUrl + '">Termos de Uso</a>.',
      dismiss: 'Prosseguir',
      link: false,
   },
   revokable: false,
   onInitialize: function (status) {
      console.log({status})
   },
   onStatusChange: function (status) {
      console.log({status})
   },
})
