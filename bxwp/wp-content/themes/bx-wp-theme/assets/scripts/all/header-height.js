document.addEventListener('alpine:init', () => {
   window.Alpine.data('headerHeight', () => ({
      headerHeight: 100,

      updateHeaderHeight() {
         const header = document.getElementById('main-header')
         this.headerHeight = header ? header.offsetHeight : 100
      },

      getHeaderHeightStyle() {
         return `height: calc(100vh - ${this.headerHeight}px);`
      },

      getHeaderTopStyle() {
         return `top: ${this.headerHeight}px;`
      },
   }))
})
