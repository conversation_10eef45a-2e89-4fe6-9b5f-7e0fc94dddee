export const assetsFolder = './bxwp/wp-content/themes/bx-wp-theme'

const fallbackFonts = [
   'ui-sans-serif',
   'system-ui',
   '-apple-system',
   'BlinkMacSystemFont',
   'Segoe UI',
   'Roboto',
   'Helvetica Neue',
   'Arial',
   'Noto Sans',
   'sans-serif',
   'Apple Color Emoji',
   'Segoe UI Emoji',
   'Segoe UI Symbol',
   'Noto Color Emoji',
]

export const commonConfig = {
   theme: {
      fontFamily: {
         primary: ['Glosa', ...fallbackFonts],
         secondary: ['Fira Sans', ...fallbackFonts],
         mono: ['Fira Mono', ...fallbackFonts],
      },
      extend: {
         colors: {
            primary: {
               DEFAULT: 'rgb(39, 54, 70)',
               300: 'rgb(151, 150, 165)',
               400: 'rgb(97, 110, 124)',
               500: 'rgb(39, 54, 70)',
            },
            neutral: {
               DEFAULT: 'rgb(233, 235, 237)',
               100: 'rgb(250, 250, 250)',
               200: 'rgb(238, 240, 241)',
               300: 'rgb(233, 235, 237)',
               400: 'rgb(212, 215, 218)',
               500: 'rgb(171, 176, 178)',
            },
            highlight: {
               DEFAULT: 'rgb(45, 180, 255)',
               100: 'rgb(238, 247, 255)',
               200: 'rgb(217, 237, 255)',
               500: 'rgb(45, 180, 255)',
               800: 'rgb(46, 138, 192)',
            },
            alert: {
               DEFAULT: 'rgb(220, 40, 40)',
               500: 'rgb(220, 40, 40)',
            },
            warning: {
               DEFAULT: 'rgb(248, 187, 134)',
               500: 'rgb(248, 187, 134)',
            }
         },
      },
   },
}
