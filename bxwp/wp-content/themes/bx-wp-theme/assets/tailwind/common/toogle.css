.toggle-container {
   @apply inline-flex items-center cursor-pointer;
}

.toggle-container .toggle-input {
   @apply sr-only;
}

.toggle-container .toggle-switch {
   @apply relative w-11 h-6 bg-gray-200 rounded-full after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all;
}

.toggle-container .toggle-checkbox {
   @apply w-6 h-6 p-1 border-[1px] border-primary-500 rounded-md;
}

.toggle-container .toggle-input:focus + .toggle-switch {
   @apply outline-none ring-4 ring-primary-500;
}

.toggle-container .toggle-input:focus + .toggle-checkbox {
   @apply border-[2px];
}

.toggle-container .toggle-input:checked + .toggle-switch,
.toggle-container .toggle-input:checked + .toggle-checkbox {
   @apply bg-primary-500;
}

.toggle-container .toggle-input:checked + .toggle-switch::after {
   @apply translate-x-full;
}

.toggle-container .toggle-label {
   @apply ms-3 text-sm text-primary-500;
}

