@layer base {
   /* Fira Sans Regular */
   @font-face {
      font-family: 'Fira Sans';
      src: url('../fonts/fira-sans/FiraSans-Regular.woff2') format('woff2');
      font-weight: 400;
      font-style: normal;
      font-display: swap;
   }

   /* Fira Sans Bold */
   @font-face {
      font-family: 'Fira Sans';
      src: url('../fonts/fira-sans/FiraSans-Bold.woff2') format('woff2');
      font-weight: 700;
      font-style: normal;
      font-display: swap;
   }

   /* Fira Mono */
   @font-face {
      font-family: 'Fira Mono';
      src: url('../fonts/fira-mono/FiraMono-Regular.woff2') format('woff2');
      font-weight: normal;
      font-style: normal;
      font-display: swap;
   }

   /* Fira Mono Medium */
   @font-face {
      font-family: 'Fira Mono';
      src: url('../fonts/fira-mono/FiraMono-Medium.woff2') format('woff2');
      font-weight: 500;
      font-style: normal;
      font-display: swap;
   }

   /* Fira Mono Bold */
   @font-face {
      font-family: 'Fira Mono';
      src: url('../fonts/fira-mono/FiraMono-Bold.woff2') format('woff2');
      font-weight: 700;
      font-style: normal;
      font-display: swap;
   }

   /* Glosa Roman (Regular) */
   @font-face {
      font-family: 'Glosa';
      src: url('../fonts/glosa/Glosa-Roman.woff2') format('woff2');
      font-weight: 400;
      font-style: normal;
      font-display: swap;
   }

   /* Glosa Bold */
   @font-face {
      font-family: 'Glosa';
      src: url('../fonts/glosa/Glosa-Bold.woff2') format('woff2');
      font-weight: 700;
      font-style: normal;
      font-display: swap;
   }
}
