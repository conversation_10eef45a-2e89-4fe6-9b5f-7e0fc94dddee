body:has(#mobile-menu-toggle:checked),
html:has(#mobile-menu-toggle:checked) {
   @apply overflow-hidden;
}

#mobile-menu-toggle {
   @apply hidden;

   & ~ * #mobile-menu,
   & ~ #mobile-menu {
      @apply flex -translate-x-full;
   }

   &:checked ~ * #mobile-menu,
   &:checked ~ #mobile-menu {
      @apply translate-x-0 transition-transform duration-500 ease-in-out;
   }

   &:checked ~ #mobile-menu ~ * {
      @apply hidden;
   }

   & ~ *,
   & ~ #mobile-menu {
      .mobile-menu-open {
         @apply flex;
      }

      .mobile-menu-close {
         @apply hidden;
      }
   }

   &:checked ~ *,
   &:checked ~ #mobile-menu {
      .mobile-menu-open {
         @apply hidden;
      }

      .mobile-menu-close {
         @apply flex;
      }
   }
}

#mobile-menu-toggle:not(:checked) ~ #mobile-menu {
   @apply transition-transform duration-500 ease-in-out;
}

#mobile-menu {
   @apply flex fixed left-0 -translate-x-full z-10;

   ul li {
      @apply py-2 border-b border-neutral-400;
   }
}
