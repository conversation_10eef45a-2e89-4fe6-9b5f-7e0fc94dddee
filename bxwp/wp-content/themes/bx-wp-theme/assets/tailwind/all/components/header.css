#mobile-menu-toggle {
   @apply hidden;

   & ~ * #mobile-menu,
   & ~ #mobile-menu {
      @apply transition duration-500 translate-x-[-100vw];
   }

   &:checked ~ * #mobile-menu,
   &:checked ~ #mobile-menu {
      @apply flex translate-x-0;
   }

   &:checked ~ #mobile-menu ~ * {
      @apply hidden;
   }

   & ~ *,
   & ~ #mobile-menu {
      .mobile-menu-open {
         @apply flex;
      }

      .mobile-menu-close {
         @apply hidden;
      }
   }

   &:checked ~ *,
   &:checked ~ #mobile-menu {
      .mobile-menu-open {
         @apply hidden;
      }

      .mobile-menu-close {
         @apply flex;
      }
   }
}

#mobile-menu {
   ul li {
      @apply py-2 border-b border-neutral-400;
   }
}
