# O Bastidor / https://

Notícias exclusivas sobre o poder e análises originais acerca de fatos políticos, jurídicos e econômicos da vida nacional.

## Ambientes

| Ambiente        | Branch  | `wp_get_environment_type()` | URL                           |
| --------------- | ------- | --------------------------- | ----------------------------- |
| Produção        | `main`  | `production`                | https://www.obastidor.com.br/ |
| Homologação     | `stage` | `staging`                   | https://stg.obastidor.com.br/ |
| Desenvolvimento | `dev`   | `development`               |                               |
| Local           | -       | `local`                     | https://obastidor.local       |

---

## Estrutura de pastas

```bash
├──assets          # Todos os recursos do tema.
│   ├──icons         # Imagens e vetores para ícones.
│   ├──scripts        # Arquivos Javascript.
│   │   ├──all           # Arquivos .js do tema, separados por pasta de acordo com pages/componentes
│   │   ├──admin         # Arquivos .js do painel do admin.
│   │   └──common        # JS em comum em vários .js
│   ├──tailwind       # .css de conteúdo para Tailwind e configurações
│   │   ├──all           # Arquivos .css do tema, separados por pasta de acordo com pages/componentes
│   │   ├──admin         # Arquivos .css do painel do admin.
│   │   └──common        # CSS em comum em vários .css.
│   ├──js             # .min.js gerados pelo Javascript, não é incluso no repositório.
│   ├──css            # .min.css gerados pelo Tailwind, não é incluso no repositório.
│   └──fonts          # .woff e .woff2 de fontes usadas no tema.
├──includes        # Regras de negócio, configurações, etc.
│   ├──global         # Hooks de configurações e registros gerais do tema.
│   ├──generic        # Classes e funções não especificas do tema.
│   ├──entities       # Classes com CRUD e Registers internos.
│   ├──services       # Classes com Requests e APIs externos.
│   └──hooks          # Hooks que adicionam/modificam HTML e/ou configurações no tema.
├──modules         # Módulos que precisam de JS e CSS específicos. e.g. Login.
├──pages           # Templates chamados pela hierarquia padrão, e suas de partes.
└──components      # Templates partes usados em várias páginas ou pelo includes.
```

---

## Entidades

| Entity | Tipo | Slug   | Taxonomia(s) | Campos personalizados | Descrição               |
| ------ | ---- | ------ | ------------ | --------------------- | ----------------------- |
| Post   | Post | `post` |              |                       | Tipo de post principal. |

## Plugins

Plugins necessários e respectivas configurações.

| Plugin          | Obrigatório | Ambiente | Notas                                    |
| --------------- | ----------- | -------- | ---------------------------------------- |
| BX Essentials   | Sim         | Todos    | Funções e hooks padrões.                 |
| ACF Pro         | Sim         | Todos    | Campos personalizados, páginas de opções |
| Co-Authors Plus | Não         | Todos    | Permite múltiplos autores.               |
| Yoast SEO       | Não         | Todos    | Gerencia SEO e sitemap.                  |

## Serviços

| Serviço | Link documentação/API | Descrição |
| ------- | --------------------- | --------- |
|         |                       |           |

## Módulos

| Módulo | Recursos | Descrição |
| ------ | -------- | --------- |
|        |          |           |

---

## Comandos

### Assets

Para gerar os assets do projeto é necessário rodar o NPM (node).
Recomenda-se a versão do Node 18.16.0.

Abra o terminal na pasta do projeto para executar os comandos seguintes.

```bash
npm install # instale ou atualiza as dependências
npm run assets # gera CSS e JS
npm run css-all-watch # observa atualizações para arquivo all do CSS
npm run js-watch # observa atualizações no JS
```

Para outros comandos, veja `package.json`.

### Instalar Composer

Para integração de dependencias do composer

```bash
cd bxwp/wp-content/themes/bx-wp-theme/includes/composer
composer install

```

---

# Links

-  [Classes deste projeto](docs/Classes)
-  [Responsáveis atuais deste projeto](https://app.clickup.com/30906204/v/b/6-901301462428-2)
-  [Documentação completa deste tema](https://bitbucket.org/buildboxdev/bx_wp_theme/wiki)
