version: '2'
services:
   wordpress:
      image: buildbox/(id-projeto)
      container_name: wordpress
      build:
         context: .
         dockerfile: deploy/docker-local/Dockerfile
      ports:
         - 80:80
         - 443:443
      depends_on:
         - database
      volumes:
         - ${PWD}/bxwp:/var/www/html
      environment:
         - DB_NAME=local
         - DB_USER=root
         - DB_PASSWORD=root
         - DB_HOST=database
         - WP_ENVIRONMENT_TYPE=local
   database:
      image: mariadb
      container_name: database
      ports:
         - 3306:3306
      environment:
         - MARIADB_USER=root
         - MARIADB_PASSWORD=root
         - MARIADB_ROOT_PASSWORD=root
         - MYSQL_DATABASE=borainvestir
      volumes:
         - ${PWD}/resources/db_dump.sql:/var/backup/db_dump.sql
   database-cli:
      image: phpmyadmin
      ports:
         - 8080:80
      environment:
         - PMA_ARBITRARY=1
