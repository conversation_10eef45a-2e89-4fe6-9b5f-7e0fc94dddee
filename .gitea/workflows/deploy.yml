name: Deploy WordPress

on:
  push:
    branches:
      - stg
      - main

env:
  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  AWS_DEFAULT_REGION: sa-east-1
  DISCORD_WEBHOOK_URL: ${{ secrets.DISCORD_WEBHOOK_URL }}
  ECR_REGISTRY_STG: ${{ secrets.ECR_REGISTRY_STG }}
  ECR_REGISTRY_PRD: ${{ secrets.ECR_REGISTRY_PRD }}
  GITEA_REF_NAME: ${{ gitea.ref_name }}
  GITEA_ACTOR: ${{ gitea.actor }}
  GITEA_RUN_NUMBER: ${{ gitea.run_number }}
  TIMESTAMP: ${{ gitea.event.head_commit.timestamp }}

jobs:
  deploy:
    name: 🚀 Deploy WordPress
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.env.outputs.environment }}
      image-name: ${{ steps.env.outputs.image-name }}
      image-tag: ${{ steps.env.outputs.image-tag }}
      original-desired: ${{ steps.deploy.outputs.original-desired }}
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🛠️ Setup environment variables
        id: env
        run: |
          echo "Branch: $GITEA_REF_NAME"

          if [[ "$GITEA_REF_NAME" == "stg" ]] || [[ "$GITEA_REF_NAME" == "feature/pipeline" ]]; then
            echo "environment=stg" >> $GITHUB_OUTPUT
            echo "ecr-registry=$ECR_REGISTRY_STG" >> $GITHUB_OUTPUT
            echo "image-name=obastidor-stg-wordpress" >> $GITHUB_OUTPUT
            echo "✅ Setting up for STG environment"
          elif [[ "$GITEA_REF_NAME" == "main" ]]; then
            echo "environment=prd" >> $GITHUB_OUTPUT
            echo "ecr-registry=$ECR_REGISTRY_PRD" >> $GITHUB_OUTPUT
            echo "image-name=obastidor-prd-wordpress" >> $GITHUB_OUTPUT
            echo "✅ Setting up for PRD environment"
          else
            echo "❌ Unknown branch '$GITEA_REF_NAME'"
            exit 1
          fi

          if [[ -n "$GITEA_RUN_NUMBER" ]]; then
            echo "image-tag=$GITEA_RUN_NUMBER" >> $GITHUB_OUTPUT
          else
            echo "image-tag=$(date +%Y%m%d-%H%M%S)" >> $GITHUB_OUTPUT
          fi

      - name: 🧹 Clean system resources
        run: |
          chmod +x deploy/bin/cleanup-system.sh
          bash deploy/bin/cleanup-system.sh

      - name: 📦 Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version-file: '.nvmrc'

      - name: 📥 Install Node.js dependencies
        run: npm ci --prefer-offline --no-audit --silent

      - name: 🎨 Build frontend assets
        run: npm run assets

      - name: 📥 Download WordPress core files
        run: |
          echo "🔍 Current directory: $(pwd)"
          echo "📁 Listing current files:"
          ls -la

          # Create bxwp directory if it doesn't exist
          mkdir -p bxwp
          echo "📁 Created bxwp directory"

          # Check if scripts exist and are executable
          chmod +x deploy/bin/download-wp.sh deploy/bin/download-plugins.sh
          bash deploy/bin/download-wp.sh
          bash deploy/bin/download-plugins.sh

      - name: 📦 Install AWS CLI
        run: |
          curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
          unzip awscliv2.zip
          sudo ./aws/install

      - name: 🔐 Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: sa-east-1

      - name: 🐳 Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: 🏗️ Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🐳 Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: deploy/Dockerfile
          push: true
          tags: |
            ${{ steps.login-ecr.outputs.registry }}/${{ steps.env.outputs.image-name }}:${{ steps.env.outputs.image-tag }}
            ${{ steps.login-ecr.outputs.registry }}/${{ steps.env.outputs.image-name }}:latest
          platforms: linux/amd64

      - name: 🔍 Check AWS CLI availability
        run: |
          echo "Checking AWS CLI..."
          which aws || echo "AWS CLI not found"
          aws --version || echo "AWS CLI not working"

      - name: 📊 Get Auto Scaling Group capacity
        id: deploy
        run: |
          ASG_NAME="obastidor-${{ steps.env.outputs.environment }}-wordpress-asg"
          ORIGINAL_DESIRED=$(aws autoscaling describe-auto-scaling-groups \
            --auto-scaling-group-names "$ASG_NAME" \
            --region "sa-east-1" \
            --query "AutoScalingGroups[0].DesiredCapacity" \
            --output text)
          echo "original-desired=$ORIGINAL_DESIRED" >> $GITHUB_OUTPUT

      - name: 🚀 Deploy to ECS with zero downtime
        id: deploy-ecs
        run: |
          chmod +x deploy/bin/zero-downtime-rolling-replace.js
          node deploy/bin/zero-downtime-rolling-replace.js \
            "${{ steps.env.outputs.environment }}"

  notify:
    name: 📢 Send Deployment Notifications
    needs: deploy
    runs-on: ubuntu-latest
    if: always() && env.DISCORD_WEBHOOK_URL != ''
    steps:
      - name: 📊 Determine deployment status
        id: status
        run: |
          if [[ "${{ needs.deploy.result }}" == "success" ]]; then
            echo "status=success" >> $GITHUB_OUTPUT
            echo "error-message=" >> $GITHUB_OUTPUT
          elif [[ "${{ needs.deploy.result }}" == "failure" ]]; then
            echo "status=failure" >> $GITHUB_OUTPUT
            echo "error-message=Deployment failed - check logs for details" >> $GITHUB_OUTPUT
          elif [[ "${{ needs.deploy.result }}" == "cancelled" ]]; then
            echo "status=cancelled" >> $GITHUB_OUTPUT
            echo "error-message=Deployment was cancelled" >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
            echo "error-message=Invalid deployment status: ${{ needs.deploy.result }}" >> $GITHUB_OUTPUT
          fi

      - name: 📢 Send Discord notification
        uses: sarisia/actions-status-discord@v1
        with:
          webhook: ${{ env.DISCORD_WEBHOOK_URL }}
          status: ${{ steps.status.outputs.status }}
          title: "WordPress Deploy - ${{ needs.deploy.outputs.environment }}"
          description: |
            **📊 Status:** ${{ steps.status.outputs.status == 'success' && '✅ Sucesso' || steps.status.outputs.status == 'failure' && '❌ Falha' || steps.status.outputs.status == 'cancelled' && '⚠️ Cancelado' || '❓ Desconhecido' }}

            **🌍 Ambiente:** `${{ needs.deploy.outputs.environment | upper }}`
            **🌿 Branch:** `${{ env.GITEA_REF_NAME }}`
            **🏷️ Versão:** `${{ env.GITEA_RUN_NUMBER }}`
            **👤 Autor:** `${{ env.GITEA_ACTOR }}`

            **🐳 Imagem Docker:** `${{ needs.deploy.outputs.image-name }}:${{ needs.deploy.outputs.image-tag }}`

            ${{ steps.status.outputs.error-message != '' && format('**❌ Erro:** {0}', steps.status.outputs.error-message) || '' }}

            ---
            *Deployment completed at ${{ env.TIMESTAMP }}*
          color: ${{ steps.status.outputs.status == 'success' && '3066993' || steps.status.outputs.status == 'failure' && '15158332' || steps.status.outputs.status == 'cancelled' && '16776960' || '8421504' }}
