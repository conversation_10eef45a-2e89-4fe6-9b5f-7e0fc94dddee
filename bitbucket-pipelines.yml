image: alpine
clone:
  depth: 1
  enabled: true

services:
- docker

options:
  docker: true

definitions:
  steps:
  - step: &build
      name: Building assets
      image: node:18.16.0-slim
      script:
      - npm ci && npm run assets
      - rm -rf ./bxwp/wp-content/themes/bx-wp-theme/assets/tailwind/
      - rm -rf ./bxwp/wp-content/themes/bx-wp-theme/assets/scripts/
      artifacts:
      - bxwp/**

  # Needed: SMTP_TO, DOMAIN
  - step: &send-notify
      name: Sending notify
      script:
      - ALERT_TYPE="sucesso"
      - if [[ $BITBUCKET_EXIT_CODE -ne 0 ]]; then ALERT_TYPE="erro" ; fi
      - pipe: atlassian/email-notify:0.13.1
        variables:
          USERNAME: ${SMTP_USER}
          PASSWORD: ${SMTP_PASS}
          FROM: ${SMTP_USER}
          TO: ${SMTP_TO}
          HOST: ${SMTP_HOST}
          SUBJECT: '[${BITBUCKET_PROJECT_KEY} - ${BITBUCKET_REPO_SLUG}] Ambiente ${BITBUCKET_DEPLOYMENT_ENVIRONMENT} atualizado com ${ALERT_TYPE}'
          BODY_PLAIN: 'Endereço atualizado:<br><a href="https://${DOMAIN}">https://${DOMAIN}</a><br><br>Branch:<br>${BITBUCKET_BRANCH}<br><br>Pipeline:<br><a href="https://bitbucket.org/${BITBUCKET_REPO_FULL_NAME}/addon/pipelines/home#!/results/${BITBUCKET_BUILD_NUMBER}">#${BITBUCKET_BUILD_NUMBER}</a><br><br>Repositório:<br><a href="https://bitbucket.org/${BITBUCKET_REPO_FULL_NAME}">${BITBUCKET_REPO_SLUG}</a>'

  - step: &upload
      name: Uploading build
      script:
      - apt-get update -qqy && apt-get upgrade -qqy
      - apt-get install -qqy zip
      - cd ./bxwp/wp-content/themes
      - zip -rq ${BITBUCKET_BRANCH//\//\_}.zip ./bx-wp-theme
      - pipe: atlassian/bitbucket-upload-file:0.7.1
        variables:
          FILENAME: bxwp/wp-content/themes/${BITBUCKET_BRANCH//\//\_}.zip
          BITBUCKET_ACCESS_TOKEN: ${BB_USER_TOKEN}

  # Needed: DOMAIN
  - step: &testing
      name: Running Tests
      image: node:18.16.0
      script:
      - apt-get -qqy update && apt-get -qqy upgrade
      - apt-get -qqy install libgtk2.0-0 libgtk-3-0 libgbm-dev libnotify-dev libnss3 libxss1 libasound2 libxtst6 xauth xvfb ruby ruby-dev chromium
      - npm ci
      - npx lighthouse https://${DOMAIN} --chrome-flags="--headless --no-sandbox" --output json --output-path=./report.json --quiet --no-enable-error-reporting
      - cat report.json
      - npm run test:run
      - gem install wpscan
      - wpscan --url ${DOMAIN} -e vp,tt,cb,dbe,u --api-token ${WPSCAN_TOKEN}

  # Needed: PATH_DIR, ENV_FILE
  - step: &deploy-aws
      name: Deploying to AWS EC2
      image: ubuntu:latest
      script:
      - apt-get update -qqy && apt-get upgrade -qqy
      - apt-get install -qqy curl unzip
      - curl https://rclone.org/install.sh | bash
      - rclone config create remote sftp host=${AWS_BX_HOST} user=${AWS_BX_USER} key_file=/opt/atlassian/pipelines/agent/ssh/id_rsa_tmp
      - rclone copyto bxwp/bx-env-all.php remote:${PATH_DIR}/bx-env-all.php
      - rclone copyto bxwp/${ENV_FILE} remote:${PATH_DIR}/${ENV_FILE}
      - rclone sync --delete-after bxwp/wp-content/themes/bx-wp-theme remote:${PATH_DIR}/wp-content/themes/bx-wp-theme

  - step: &deploy-gcp-1-image
      name: Building image
      image: google/cloud-sdk:alpine
      script:
      - chmod +x docker/bin/gcp-login
      - docker/bin/gcp-login ${GCLOUD_PROJECT} ${GCLOUD_CLUSTER} "${GCLOUD_API_KEYFILE}"
      - export DOCKER_BUILDKIT=0
      - docker build -t ${GCLOUD_ENV} -f docker/${GCLOUD_ENV}/Dockerfile .
      - docker save --output cache.docker ${GCLOUD_ENV}
      artifacts:
      - cache.docker
  - step: &deploy-gcp-2-registry
      name: Deploying container to Container Registry
      image: google/cloud-sdk:alpine
      script:
      - docker load --input ./cache.docker
      - chmod +x docker/bin/test-instance
      - docker/bin/test-instance ${GCLOUD_ENV}
      - export IMAGE_NAME=gcr.io/${GCLOUD_PROJECT}/${GCLOUD_ENV}:${BITBUCKET_BUILD_NUMBER}
      - docker tag ${GCLOUD_ENV} ${IMAGE_NAME}
      - chmod +x docker/bin/gcp-login
      - docker/bin/gcp-login ${GCLOUD_PROJECT} ${GCLOUD_CLUSTER} "${GCLOUD_API_KEYFILE}"
      - docker push ${IMAGE_NAME}
  - step: &deploy-gcp-3-kubernetes
      name: Deploying container to Kubernetes Pod
      image: google/cloud-sdk:alpine
      script:
      - curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
      - install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl
      - export USE_GKE_GCLOUD_AUTH_PLUGIN=True
      - gcloud components update
      - chmod +x docker/bin/gcp-login
      - docker/bin/gcp-login ${GCLOUD_PROJECT} ${GCLOUD_CLUSTER} "${GCLOUD_API_KEYFILE}"
      - gcloud components install gke-gcloud-auth-plugin
      - gcloud container clusters get-credentials ${GCLOUD_CLUSTER} --zone ${GCLOUD_ZONE}
      - kubectl set image deployment ${GCLOUD_ENV} ${GCLOUD_ENV}-env=gcr.io/${GCLOUD_PROJECT}/${GCLOUD_ENV}:${BITBUCKET_BUILD_NUMBER}

  - step: &deploy-ecs
      name: Deploying to AWS ECS
      script:
      -

  - step: &deploy-azure
      name: Deploying to Azure
      script:
      -

pipelines:
  pull-requests:
    hotfix/*:
      deployment: hotfix
      - step: *send-notify

    feature/*:
      - step: *upload

  branches:
    dev:
    - stage:
        name: Deploying to Test
        deployment: Test
        steps:
          - step: *build
          - step: *deploy-aws

    stage:
    - stage:
        name: Deploying to Staging
        deployment: Staging
        steps:
          - step: *build
          - step: *deploy-gcp
          - step: *send-notify
          - step: *testing

  custom:
    scheduled-main:
    - stage:
        name: Deploying to Production
        deployment: Production
        steps:
          - step: *build
          - step: *deploy-gcp-1-image
          - step: *deploy-gcp-2-registry
          - step: *deploy-gcp-3-kubernetes
          - step: *send-notify
