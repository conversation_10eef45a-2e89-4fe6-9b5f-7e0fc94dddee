#!/usr/bin/env node

const {execSync} = require('child_process')
const {argv, exit} = require('process')

// ==================================================
// CONFIGURATION
// ==================================================
const CONFIG = {
   REGION: 'sa-east-1',
   POLL_INTERVAL_MS: 10_000,
   TIMEOUT_MS: 15 * 60_000,
   // NOTE: Deregistration delay is configured to 5 minutes (300s) for security
   // to ensure active connections are properly terminated before
   // removing the instance from the target group. Keep this value for security.
}

// ==================================================
// UTILITIES
// ==================================================
const log = (msg) => {
   const t = new Date().toISOString().replace('T', ' ').slice(0, 19)
   console.log(`[${t}] ${msg}`)
}

const sleep = (ms) => new Promise((r) => setTimeout(r, ms))

const aws = ({desc, cmd, json = false}) => {
   if (desc) log(desc)

   const out = execSync(cmd, {stdio: ['pipe', 'pipe', 'pipe']})
      .toString()
      .trim()
   return json ? JSON.parse(out || '{}') : out
}

// ==================================================
// ARGUMENT VALIDATION
// ==================================================
const validateArgs = () => {
   if (argv.length < 3) {
      console.error(`Usage: node ${argv[1]} <environment>`)
      exit(1)
   }

   const ENV = argv[2]
   const names = {
      cluster: `obastidor-${ENV}-wordpress-cluster`,
      service: `obastidor-${ENV}-wordpress-service`,
      asg: `obastidor-${ENV}-wordpress-asg`,
   }

   return {ENV, names}
}

// ==================================================
// ECS AND ASG OPERATIONS
// ==================================================
const forceServiceDeployment = ({cluster, service}) =>
   aws({
      desc: `Forcing ECS deployment on service ${service}`,
      cmd: `aws ecs update-service --cluster ${cluster} --service ${service} --force-new-deployment`,
   })

const startInstanceRefresh = ({asgName}) =>
   aws({
      desc: `Starting Instance Refresh on ASG ${asgName}`,
      cmd: `aws autoscaling start-instance-refresh --auto-scaling-group-name ${asgName} \
      --preferences MinHealthyPercentage=100,InstanceWarmup=300 \
      --region ${CONFIG.REGION}`,
      json: true,
   }).InstanceRefreshId

const waitForRefresh = async ({asgName, refreshId}) => {
   const started = Date.now()
   let status = ''

   log('🔄 Starting Instance Refresh with 5-minute deregistration delay for security')
   log('   ⏱️  This delay ensures active connections are properly terminated')

   while (Date.now() - started < CONFIG.TIMEOUT_MS) {
      const result = aws({
         desc: `Checking status of Instance Refresh ${refreshId}`,
         cmd: `aws autoscaling describe-instance-refreshes \
        --auto-scaling-group-name ${asgName} \
        --region ${CONFIG.REGION}`,
         json: true,
      })

      const refresh = result.InstanceRefreshes?.find((r) => r.InstanceRefreshId === refreshId)

      status = refresh?.Status
      log(`Current refresh status: ${status}`)

      if (status === 'Successful') return
      if (['Failed', 'Cancelled'].includes(status)) {
         throw new Error(`Instance Refresh failed: ${status}`)
      }

      await sleep(CONFIG.POLL_INTERVAL_MS)
   }

   throw new Error(`Timeout: Instance Refresh ${refreshId} did not finish in time`)
}

// ==================================================
// MAIN EXECUTION
// ==================================================
const main = async () => {
   const {names} = validateArgs()

   forceServiceDeployment({cluster: names.cluster, service: names.service})

   const refreshId = startInstanceRefresh({asgName: names.asg})

   await waitForRefresh({asgName: names.asg, refreshId})

   log('✅ Instance Refresh completed successfully. Deployment is now live.')
   log('🔒 5-minute deregistration delay maintained for security throughout the process')
}

main()
