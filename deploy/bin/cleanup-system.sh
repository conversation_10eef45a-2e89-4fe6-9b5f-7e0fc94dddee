#!/bin/bash

set -e

# Clean Docker resources
echo "🐳 Cleaning Docker resources..."
docker system prune -af || true
docker builder prune -af || true
docker image prune -af || true
docker volume prune -f || true
docker container prune -f || true

# Clean npm cache
echo "📦 Cleaning npm cache..."
npm cache clean --force || true

# Clean workspace artifacts (SAFE - only non-critical files)
echo "🗂️ Cleaning workspace artifacts..."
find . -name "*.log" -delete || true
find . -name "*.tmp" -delete || true
find . -name "*.temp" -delete || true
find . -name ".DS_Store" -delete || true
find . -name "Thumbs.db" -delete || true

# Clean build artifacts that might be left over (SAFE)
echo "🏗️ Cleaning build artifacts..."
rm -rf dist/ || true
rm -rf build/ || true
rm -rf .next/ || true
rm -rf .nuxt/ || true
rm -rf node_modules/.cache/ || true

# Clean system temp files
echo "🗂️ Cleaning system temp files..."
sudo rm -rf /tmp/* || true
sudo rm -rf /var/tmp/* || true

# Clean apt cache (if available)
echo "📦 Cleaning apt cache..."
sudo apt-get clean || true
sudo apt-get autoremove -y || true

# Clean any downloaded files from previous runs (SAFE)
echo "📥 Cleaning downloaded files..."
rm -f awscliv2.zip || true
rm -rf aws/ || true
rm -rf .aws/ || true

# Clean any IDE/editor artifacts (SAFE)
echo "💻 Cleaning IDE artifacts..."
rm -rf .vscode/ || true
rm -rf .idea/ || true
rm -rf *.swp || true
rm -rf *.swo || true
rm -rf *~ || true

# Clean any backup files (SAFE)
echo "💾 Cleaning backup files..."
find . -name "*.bak" -delete || true
find . -name "*.backup" -delete || true
find . -name "*~" -delete || true

# Show disk space after cleanup
echo "💾 Disk space after cleanup:"
df -h

# Show workspace size
echo "📁 Workspace size after cleanup:"
du -sh . || true

echo "✅ System cleanup completed"
