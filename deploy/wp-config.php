<?php

// Força o uso de HTTPS no painel administrativo
define('FORCE_SSL_ADMIN', true);

if (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https') {
   $_SERVER['HTTPS'] = 'on';
}

define('DB_NAME', getenv('WORDPRESS_DB_NAME'));
define('DB_USER', getenv('WORDPRESS_DB_USER'));
define('DB_PASSWORD', getenv('WORDPRESS_DB_PASSWORD'));
define('DB_HOST', getenv('WORDPRESS_DB_HOST'));

define('WP_ENVIRONMENT_TYPE', getenv('WP_ENVIRONMENT_TYPE'));

define('DB_CHARSET', 'utf8mb4');
define('DB_COLLATE', '');

// Constantes para S3/Cloudfront
define('AS3CF_SETTINGS', serialize(array(
   'provider'               => getenv('WP_OFFLOAD_MEDIA_PROVIDER'),
   'delivery-provider'      => getenv('WP_OFFLOAD_MEDIA_PROVIDER'),
   'access-key-id'          => getenv('WP_OFFLOAD_MEDIA_ACCESS_KEY'),
   'secret-access-key'      => getenv('WP_OFFLOAD_MEDIA_SECRET_KEY'),
   'bucket'                 => getenv('WP_OFFLOAD_MEDIA_BUCKET'),
   'region'                 => getenv('WP_OFFLOAD_MEDIA_REGION'),
   'delivery-domain'        => getenv('WP_OFFLOAD_MEDIA_CUSTOM_DOMAIN'),
   'use-server-roles'       => true,
   'copy-to-s3'             => true,
   'serve-from-s3'          => true,
   'enable-delivery-domain' => true,
   'force-https'            => true,
   'remove-local-file'      => false,
   'use-yearmonth-folders'  => true,
   'object-versioning'      => true,
   'enable-object-prefix'   => true,
)));

define('AS3CF_SETTINGS_OVERWRITE', true);

// Constantes para Redis
// define('REDIS_HOST',       getenv('WP_REDIS_HOST'));
// define('REDIS_SCHEME',     getenv('WP_REDIS_SCHEME'));
// define('REDIS_DATABASE',   getenv('WP_REDIS_DATABASE'));
// define('REDIS_PASSWORD',   getenv('WP_REDIS_PASSWORD'));

// Constantes de SMTP
// define('WP_SMTP_USER', '<EMAIL>');
// define('WP_SMTP_PASS', 'vxlz mhdm saei ipbn');
// define('WP_SMTP_HOST', 'smtp.gmail.com');

define('AUTH_KEY',         'V2FnJE2_cw9Cbojb|i7<,PMSwp&|,t1~ u ;gBU-GK|!@^TEkP|,|`n^MPR5G!8o');
define('SECURE_AUTH_KEY',  'Ix()%RX1iNC(aDpgrZ&O|k~lupdCw]xUI5FS8WWDdR2tRz3?T69TX-f=o}zwzW8i');
define('LOGGED_IN_KEY',    '>|ksN+_30F/%R=:r?!vPIEw2ROIo|xrgvF-jHQ-+,t1U9 iu/& 5<@Q[,qIrP($U');
define('NONCE_KEY',        'p}su V&W62G6WX.ARRFX|F!,/4,pL69x@D+)~|E-?)Y4=G! l.+:lQM=[*H+u9JF');
define('AUTH_SALT',        'kUbT8V[BPyw%rhavBbVi`oUjQX?o2Do=?z!W5waWF(-Yt/[<S@`}#.d|0|Hs2z+L');
define('SECURE_AUTH_SALT', 'R!BOr/Nw S>/J`w>1oZ.s2P{Qyz]>mq:(TlHCnEOQm_rM*o8d-?`GoH.XZG:z_*v');
define('LOGGED_IN_SALT',   '|@Mb,f/]-60$2x4z)Mm/1}MCK<j#[C3iv21$DYr|>K^&6Q|l6b4evyszCH$x4N+Z');
define('NONCE_SALT',       ',sm-w.pA43;,80Z+W>zyBVO[H? -9*g:8hgOfcmX+U<[U5S0}qknb[8w i7fM:X&');

$table_prefix = 'wp_';

include 'env-' . WP_ENVIRONMENT_TYPE . '.php';

if (!defined('ABSPATH')) {
   define('ABSPATH', __DIR__ . '/');
}

require_once ABSPATH . 'wp-settings.php';
