FROM henriquedamota/php-for-wordpress:8.2.10

WORKDIR /var/www/html

# Clear workdir folder
RUN rm -rf /var/www/html/*

# Copy configuration files first (these change less frequently)
COPY ["deploy/commons/nginx.conf", "/etc/nginx/nginx.conf"]
COPY ["deploy/commons/publichost.conf", "/etc/nginx/sites-enable/1-publichost.conf"]
COPY ["deploy/commons/php-fpm/www.conf", "/opt/php/etc/php-fpm.d/"]

# Create directories and set permissions
RUN mkdir -p /var/www/html/wp-content/uploads /var/log/php /var/log/nginx \
   && chown -R www-data:www-data /var/log \
   && chmod 755 -R /var/log

# Copy Wordpress source to container
COPY ["bxwp", "/var/www/html"]

# Wp-config move
COPY ["deploy/wp-config.php", "/var/www/html/wp-config.php"]

# Set final permissions - simple and robust
RUN chown -R www-data:www-data /var/www/html \
   && chmod -R 755 /var/www/html

# Start container using Supervisor to start NgInx and PHP Fpm (see /etc/supervisord.conf)
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisord.conf"]
