server {
   listen *:80;
   server_name stg.obastidor.com.br;
   root /var/www/html;
   index index.php;

   add_header Content-Security-Policy "frame-ancestors 'self';" always;
   add_header X-Content-Type-Options "nosniff" always;
   add_header Strict-Transport-Security "max-age=63072000; includeSubDomains" always;
   add_header X-Frame-Options "SAMEORIGIN" always;

   access_log /var/log/nginx/nginx-bx-wp-access.log;

   location = /favicon.ico {
      log_not_found off;
      access_log off;
   }

   location = /robots.txt {
      rewrite ^ /index.php?robots=1 last;
   }

   location ~ /xmlrpc.php {
      return 301 /404;
   }

#    location ~ ^/app/(.*)$ {
#         try_files $uri $uri/ /wp-content/$1;
#    }

   location ~ \.php$ {
      include fastcgi_params;
      fastcgi_intercept_errors on;
      fastcgi_pass php;
      fastcgi_param  SCRIPT_FILENAME $document_root$fastcgi_script_name;
   }

   location ~* \.(js|css|png|jpg|jpeg|gif|ico)$ {
      expires max;
      log_not_found off;
   }

   location / {
      autoindex off;
      try_files $uri $uri/ /index.php?$args;
   }
}
