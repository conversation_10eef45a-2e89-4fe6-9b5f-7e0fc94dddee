{"editor.tabSize": 3, "editor.insertSpaces": true, "editor.wordWrap": "wordWrapColumn", "editor.bracketPairColorization.enabled": true, "editor.detectIndentation": false, "editor.matchBrackets": "always", "editor.rulers": [80, 120], "editor.wordWrapColumn": 120, "editor.fontLigatures": true, "editor.formatOnSave": true, "explorer.fileNesting.patterns": {"*.js": "${capture}.js.map, ${capture}.min.js, ${capture}.d.ts, ${capture}.asset.php", "*.woff2": "${capture}.*", "tsconfig.json": "tsconfig.*.json, webpack.config.js", "package.json": "package-lock.json, yarn.lock, pnpm-lock.yaml, .nvmrc, nest-cli.json, .prettierrc, .eslintrc.js", "docker-compose.*": ".docker*", "gulpfile.babel.*": "wpgulp.config.*", "composer.json": "composer.*", ".php-cs-fixer.php": ".php-cs-fixer.*", ".gitignore": ".git*ignore", "tailwind.config.*": "postcss.config.*", "cypress.config.*": "cypress.*", ".env": ".env*", "env-local*": "env*", "bx-env-local*": "bx-env*"}, "files.eol": "\n", "files.insertFinalNewline": true, "files.trimFinalNewlines": true, "files.trimTrailingWhitespace": true, "files.watcherExclude": {"**/wp-content/uploads/**": true, "**/wp-content/**cache**/**": true}, "workbench.editor.labelFormat": "short", "git.autofetch": true, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsx]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[php]": {"editor.formatOnSave": true, "editor.defaultFormatter": "bmewburn.vscode-intelephense-client"}, "[yaml]": {"editor.formatOnSave": false, "editor.insertSpaces": true, "editor.tabSize": 2, "editor.autoIndent": "advanced", "editor.detectIndentation": false, "editor.indentSize": "tabSize"}, "intelephense.format.braces": "psr12", "prettier.printWidth": 120, "prettier.tabWidth": 3, "prettier.useTabs": false, "prettier.semi": false, "prettier.singleQuote": true, "prettier.quoteProps": "as-needed", "prettier.jsxSingleQuote": false, "prettier.trailingComma": "all", "prettier.bracketSpacing": false, "prettier.bracketSameLine": true, "prettier.arrowParens": "always", "prettier.htmlWhitespaceSensitivity": "css", "prettier.endOfLine": "lf", "cSpell.words": ["sntls", "buildbox"], "tailwind-fold.foldStyle": "QUOTES", "tailwind-fold.unfoldIfLineSelected": true, "tailwind-fold.foldLengthThreshold": 4, "material-icon-theme.folders.associations": {"singular": "docs", "generic": "import", "template-hooks": "hook", "hooks": "hook", "tailwind": "plugin", "entities": "class"}}