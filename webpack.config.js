const path = require('path')
const defaults = require('@wordpress/scripts/config/webpack.config.js')
const TerserPlugin = require('terser-webpack-plugin')

const package = require('./package.json')

let jsFiles = {}

for (const file of package.config.jsFiles) {
   jsFiles[file] = path.resolve(
      process.cwd(),
      'bxwp',
      'wp-content',
      'themes',
      package.name,
      'assets',
      'scripts',
      file,
      '_index.js',
   )
}

module.exports = {
   ...defaults,
   entry: jsFiles,
   output: {
      filename: '[name].min.js',
      path: path.resolve(process.cwd(), 'bxwp', 'wp-content', 'themes', package.name, 'assets', 'js'),
   },
   optimization: {
      ...defaults.optimization,
      minimizer: [
         new TerserPlugin({
            parallel: true,
            terserOptions: {
               output: {
                  comments: /translators:/i,
               },
               compress: {
                  passes: 2,
               },
               mangle: {
                  reserved: ['__', '_n', '_nx', '_x'],
                  keep_classnames: true,
               },
            },
            extractComments: false,
         }),
      ],
   },
   module: {
      ...defaults.module,
      rules: [
         ...defaults.module.rules,
         {
            test: /\.tsx?$/,
            use: [
               {
                  loader: 'ts-loader',
                  options: {
                     configFile: 'tsconfig.json',
                     transpileOnly: true,
                  },
               },
            ],
         },
      ],
   },
   resolve: {
      extensions: ['.ts', '.tsx', ...(defaults.resolve ? defaults.resolve.extensions || ['.js', '.jsx'] : [])],
   },
}
