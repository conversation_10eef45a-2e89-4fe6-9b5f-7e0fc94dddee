{"name": "bx-wp-theme", "config": {"jsFiles": ["all", "admin", "block-editor"]}, "private": true, "scripts": {"css": "npm run css-all && npm run css-admin", "css-all": "tailwindcss -i ./bxwp/wp-content/themes/bx-wp-theme/assets/tailwind/all/_index.css -o ./bxwp/wp-content/themes/bx-wp-theme/assets/css/all.min.css --minify", "css-all-watch": "npm run css-all -- --watch", "css-admin": "tailwindcss -i ./bxwp/wp-content/themes/bx-wp-theme/assets/tailwind/admin/_index.css -o ./bxwp/wp-content/themes/bx-wp-theme/assets/css/admin.min.css --minify", "css-admin-watch": "npm run css-admin -- --watch", "js": "wp-scripts build", "js-watch": "wp-scripts start", "assets": "npm run css && npm run js", "adr": "adr new", "watch": "npm run js-watch & npm run css-all-watch"}, "keywords": [], "version": "3.0.0", "author": "Buildbox", "license": "MIT", "description": "Assets handlers for theme.", "devDependencies": {"@jcamp/tailwindcss-plugin-icons": "^0.6.2", "@wordpress/scripts": "^27.2.0", "adr": "^1.4.5", "cypress": "^13.6.4", "tailwindcss": "^3.4.1", "ts-loader": "^9.5.1", "typescript": "^5.3.3"}, "dependencies": {"cookieconsent": "^3.1.1"}}